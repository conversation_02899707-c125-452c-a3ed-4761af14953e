<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>WebRTC Stream Viewer</title>
    <script src="https://cdn.socket.io/4.7.2/socket.io.min.js"></script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
          sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
      }

      .container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        max-width: 800px;
        width: 100%;
        position: relative;
      }

      .header {
        background: linear-gradient(135deg, #563d39 0%, #bc857d 100%);
        color: white;
        padding: 20px;
        text-align: center;
      }

      .header h1 {
        font-size: 24px;
        font-weight: 600;
        margin-bottom: 5px;
      }

      .header p {
        opacity: 0.9;
        font-size: 14px;
      }

      .video-container {
        position: relative;
        background: #000;
        aspect-ratio: 16/9;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      #remoteVideo {
        width: 100%;
        height: 100%;
        object-fit: contain;
        border-radius: 0;
      }

      .loading {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        text-align: center;
        color: white;
      }

      .spinner {
        width: 40px;
        height: 40px;
        border: 4px solid rgba(255, 255, 255, 0.3);
        border-top: 4px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 15px;
      }

      @keyframes spin {
        0% {
          transform: rotate(0deg);
        }
        100% {
          transform: rotate(360deg);
        }
      }

      .status {
        background: #f8f9fa;
        padding: 15px 20px;
        border-top: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .status-indicator {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .status-dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #dc3545;
        animation: pulse 2s infinite;
      }

      .status-dot.connected {
        background: #28a745;
      }

      .error-message {
        background: #f8d7da;
        color: #721c24;
        padding: 15px 20px;
        border: 1px solid #f5c6cb;
        border-radius: 8px;
        margin: 20px;
        text-align: center;
      }

      .controls {
        padding: 15px 20px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        display: flex;
        gap: 10px;
        justify-content: center;
      }

      .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.2s;
      }

      .btn-primary {
        background: #563d39;
        color: white;
      }

      .btn-primary:hover {
        background: #4a332f;
      }

      .btn-secondary {
        background: #6c757d;
        color: white;
      }

      .btn-secondary:hover {
        background: #5a6268;
      }

      .stream-info {
        padding: 15px 20px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
        font-size: 12px;
        color: #6c757d;
      }

      .hidden {
        display: none;
      }

      @media (max-width: 768px) {
        .container {
          margin: 10px;
          border-radius: 15px;
        }

        .header h1 {
          font-size: 20px;
        }

        .controls {
          flex-direction: column;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>WebRTC Stream Viewer</h1>
        <p>Real-time audio and video stream</p>
      </div>

      <div class="video-container">
        <video id="remoteVideo" autoplay playsinline muted></video>
        <div id="loading" class="loading">
          <div class="spinner"></div>
          <p>Connecting to stream...</p>
        </div>
      </div>

      <div id="errorContainer" class="error-message hidden">
        <p id="errorText"></p>
      </div>

      <div class="status">
        <div class="status-indicator">
          <div id="statusDot" class="status-dot"></div>
          <span id="statusText">Connecting...</span>
        </div>
        <div id="streamInfo" class="stream-info hidden">
          <span id="viewerCount">0 viewers</span>
        </div>
      </div>

      <div class="controls">
        <button id="reconnectBtn" class="btn btn-primary" onclick="reconnect()">
          Reconnect
        </button>
        <button
          id="fullscreenBtn"
          class="btn btn-secondary"
          onclick="toggleFullscreen()"
        >
          Fullscreen
        </button>
      </div>
    </div>

    <script>
      // Configuration
      const SOCKET_URL = "https://live.flowkar.com"; // Live stream socket URL
      const ICE_SERVERS = [
        { urls: "stun:stun.l.google.com:19302" },
        { urls: "stun:stun1.l.google.com:19302" },
        { urls: "stun:stun2.l.google.com:19302" },
        { urls: "stun:stun3.l.google.com:19302" },
        { urls: "stun:stun4.l.google.com:19302" },
        {
          urls: "turn:openrelay.metered.ca:80",
          username: "openrelayproject",
          credential: "openrelayproject",
        },
        {
          urls: "turn:openrelay.metered.ca:443",
          username: "openrelayproject",
          credential: "openrelayproject",
        },
        {
          urls: "turn:openrelay.metered.ca:443?transport=tcp",
          username: "openrelayproject",
          credential: "openrelayproject",
        },
      ];

      // State variables
      let socket = null;
      let peerConnection = null;
      let streamId = null;
      let isConnected = false;
      let reconnectAttempts = 0;
      const maxReconnectAttempts = 5;

      // DOM elements
      const remoteVideo = document.getElementById("remoteVideo");
      const loading = document.getElementById("loading");
      const errorContainer = document.getElementById("errorContainer");
      const errorText = document.getElementById("errorText");
      const statusDot = document.getElementById("statusDot");
      const statusText = document.getElementById("statusText");
      const streamInfo = document.getElementById("streamInfo");
      const viewerCount = document.getElementById("viewerCount");

      // Get stream ID from URL parameters
      function getStreamIdFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return (
          urlParams.get("stream") ||
          urlParams.get("id") ||
          urlParams.get("room")
        );
      }

      // Initialize the viewer
      function init() {
        streamId = getStreamIdFromUrl();

        if (!streamId) {
          showError(
            "No stream ID provided. Please add ?stream=YOUR_STREAM_ID to the URL."
          );
          return;
        }

        console.log("Initializing viewer for stream:", streamId);
        connectToStream();
      }

      // Connect to the WebRTC stream
      function connectToStream() {
        try {
          // Initialize socket connection
          socket = io(SOCKET_URL, {
            transports: ["websocket"],
            path: "/socket.io/",
          });

          // Socket event handlers
          socket.on("connect", handleSocketConnect);
          socket.on("disconnect", handleSocketDisconnect);
          socket.on("connect_error", handleSocketError);
          socket.on("offer", handleOffer);
          socket.on("ice_candidate", handleIceCandidate);
          socket.on("stream_ended", handleStreamEnded);
          socket.on("room_closed", handleRoomClosed);
          socket.on("current_users_number", handleUserCount);

          updateStatus("Connecting to server...", false);
        } catch (error) {
          console.error("Error initializing connection:", error);
          showError("Failed to initialize connection: " + error.message);
        }
      }

      // Handle socket connection
      function handleSocketConnect() {
        console.log("Socket connected");
        updateStatus("Connected to server", true);

        // Join the stream room
        socket.emit("check_room", { room: streamId });
        socket.emit("join_room", { room: streamId });
        socket.emit("current_users_number", { room: streamId });
      }

      // Handle socket disconnection
      function handleSocketDisconnect() {
        console.log("Socket disconnected");
        updateStatus("Disconnected from server", false);
        isConnected = false;

        if (peerConnection) {
          peerConnection.close();
          peerConnection = null;
        }
      }

      // Handle socket connection error
      function handleSocketError(error) {
        console.error("Socket connection error:", error);
        updateStatus("Connection error", false);
        showError(
          "Failed to connect to server: " + (error.message || "Unknown error")
        );
      }

      // Handle WebRTC offer from broadcaster
      async function handleOffer(data) {
        console.log("Received offer:", data);

        if (data.room !== streamId) return;

        try {
          // Create peer connection
          peerConnection = new RTCPeerConnection({ iceServers: ICE_SERVERS });

          // Set up event handlers
          peerConnection.onicecandidate = (event) => {
            if (event.candidate) {
              socket.emit("ice_candidate", {
                candidate: {
                  candidate: event.candidate.candidate,
                  sdpMid: event.candidate.sdpMid,
                  sdpMLineIndex: event.candidate.sdpMLineIndex,
                },
                userId: "viewer_" + Math.random().toString(36).substr(2, 9),
                room: streamId,
              });
            }
          };

          peerConnection.oniceconnectionstatechange = () => {
            console.log(
              "ICE connection state:",
              peerConnection.iceConnectionState
            );
            if (peerConnection.iceConnectionState === "connected") {
              updateStatus("Stream connected", true);
              hideLoading();
            } else if (peerConnection.iceConnectionState === "failed") {
              showError("Connection failed. Please try reconnecting.");
            }
          };

          peerConnection.ontrack = (event) => {
            console.log("Received remote stream:", event.streams[0]);
            if (event.streams && event.streams.length > 0) {
              remoteVideo.srcObject = event.streams[0];
              remoteVideo.play().catch((e) => {
                console.error("Error playing video:", e);
              });
              hideLoading();
              updateStatus("Stream active", true);
            }
          };

          // Set remote description
          const offerToSet = data.offer.sdp ? data.offer : data.offer.offer;
          await peerConnection.setRemoteDescription(offerToSet);

          // Create and send answer
          const answer = await peerConnection.createAnswer();
          await peerConnection.setLocalDescription(answer);

          socket.emit("answer", {
            answer: {
              type: answer.type,
              sdp: answer.sdp,
            },
            userId: data.userId,
            room: streamId,
          });

          isConnected = true;
        } catch (error) {
          console.error("Error handling offer:", error);
          showError("Failed to establish connection: " + error.message);
        }
      }

      // Handle ICE candidates
      async function handleIceCandidate(data) {
        if (data.room !== streamId || !peerConnection) return;

        try {
          const candidateData = data.candidate.candidate
            ? data.candidate
            : data.candidate;
          const iceCandidate = new RTCIceCandidate(candidateData);
          await peerConnection.addIceCandidate(iceCandidate);
        } catch (error) {
          console.error("Error adding ICE candidate:", error);
        }
      }

      // Handle stream ended
      function handleStreamEnded(data) {
        console.log("Stream ended:", data);
        showError("The live stream has ended.");
        updateStatus("Stream ended", false);
      }

      // Handle room closed
      function handleRoomClosed(data) {
        console.log("Room closed:", data);
        if (data.room === streamId) {
          showError("The live stream has been ended by the host.");
          updateStatus("Stream ended", false);
        }
      }

      // Handle user count update
      function handleUserCount(data) {
        if (data.room === streamId) {
          const count = Math.max(0, data.count - 1); // Subtract broadcaster
          viewerCount.textContent = `${count} viewer${count !== 1 ? "s" : ""}`;
          streamInfo.classList.remove("hidden");
        }
      }

      // Update connection status
      function updateStatus(message, connected) {
        statusText.textContent = message;
        statusDot.className = "status-dot" + (connected ? " connected" : "");
      }

      // Show error message
      function showError(message) {
        errorText.textContent = message;
        errorContainer.classList.remove("hidden");
        hideLoading();
      }

      // Hide loading indicator
      function hideLoading() {
        loading.classList.add("hidden");
      }

      // Reconnect function
      function reconnect() {
        if (reconnectAttempts >= maxReconnectAttempts) {
          showError(
            "Maximum reconnection attempts reached. Please refresh the page."
          );
          return;
        }

        reconnectAttempts++;
        console.log(
          `Reconnection attempt ${reconnectAttempts}/${maxReconnectAttempts}`
        );

        // Clean up existing connections
        if (peerConnection) {
          peerConnection.close();
          peerConnection = null;
        }
        if (socket) {
          socket.disconnect();
        }

        // Reset video
        remoteVideo.srcObject = null;
        loading.classList.remove("hidden");
        errorContainer.classList.add("hidden");

        // Reconnect
        setTimeout(() => {
          connectToStream();
        }, 1000);
      }

      // Toggle fullscreen
      function toggleFullscreen() {
        if (!document.fullscreenElement) {
          remoteVideo.requestFullscreen().catch((err) => {
            console.error("Error entering fullscreen:", err);
          });
        } else {
          document.exitFullscreen();
        }
      }

      // Handle page visibility change
      document.addEventListener("visibilitychange", () => {
        if (document.hidden) {
          console.log("Page hidden");
        } else {
          console.log("Page visible");
          // Optionally reconnect when page becomes visible
          if (!isConnected && socket && !socket.connected) {
            reconnect();
          }
        }
      });

      // Handle page unload
      window.addEventListener("beforeunload", () => {
        if (peerConnection) {
          peerConnection.close();
        }
        if (socket) {
          socket.disconnect();
        }
      });

      // Initialize when page loads
      document.addEventListener("DOMContentLoaded", init);

      // Handle keyboard shortcuts
      document.addEventListener("keydown", (e) => {
        if (e.key === "f" || e.key === "F") {
          toggleFullscreen();
        } else if (e.key === "r" || e.key === "R") {
          reconnect();
        }
      });
    </script>
  </body>
</html>
