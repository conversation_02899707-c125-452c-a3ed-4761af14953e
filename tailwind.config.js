/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./node_modules/flowbite/**/*.js",
  ],
  theme: {
    extend: {
      gridTemplateColumns: {
        21: "repeat(21, minmax(0, 1fr))",
      },
      colors: {
        Red: "#563D39",
        lightyellow: "#B9A08B",
        primarBG: "#FAFAFB",
        sidebar: "rgba(9, 56, 69, 1)",
        customblue: "rgba(8, 30, 74, 1)",
        textcolor: "rgba(255, 255, 255, 1)",
        textRedcolor: "#563D39",
        onhover: "rgba(192, 147, 45, 1)",
        tablebt: "rgba(58, 151, 76, 1)",
        btndis: "rgba(192, 147, 45, 1)",
        textcontent: "rgba(8, 30, 74, 1)",
        textGray: "#555555",
        profileCardBG: "#EFEBE9",
        arrowIconColor: "#252520",
      },
      boxShadow: {
        "custom-shadow": "0px 0px 31.3px 20px rgba(10, 4, 60, 0.06)",
        "custom-uniform": "4px 4px 8px rgba(0, 0, 0, 0.1)",
      },
      lineClamp: {
        2: "2",
      },
      fontFamily: {
        Ubuntu: ["Ubuntu", "sans-serif"],
      },
      screens: {
        sidebar: "900px",
        "custom-width": "1440px",
      },
    },
  },
  plugins: [require("flowbite/plugin")],
};
