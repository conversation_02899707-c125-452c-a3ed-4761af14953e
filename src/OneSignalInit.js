import React, { useEffect, useState } from "react";
import { fetchFromStorage } from "./helpers/context/storage";
import siteConstant from "./helpers/constant/siteConstant";
import apiInstance from "./helpers/Axios/axiosINstance";
import { URL } from "./helpers/constant/Url";

const OneSignalDemo = () => {
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [notificationPermission, setNotificationPermission] =
    useState("default");
  const [playerId, setPlayerId] = useState(null);
  const [isLoaded, setIsLoaded] = useState(false);
  const [authToken, setAuthToken] = useState(null);

  console.log("playerId", playerId);

  const notificationAPI = async () => {
    if (playerId) {
      try {
        const response = await apiInstance.post(
          URL.NOTIFICATION,
          {
            onesignal_id: playerId,
          },
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        if (response.status === 200 || response.status === 201) {
          console.log("Player created successfully");
        } else {
          console.error("Failed to create player");
        }
      } catch (error) {
        console.error("API Error:", error);
      }
    }
  };

  useEffect(() => {
    const checkForToken = () => {
      try {
        const userdataString = localStorage.getItem("userData");

        if (userdataString) {
          const userdata = JSON.parse(userdataString);
          const token = userdata?.token;
          setAuthToken(token);
        } else {
          console.log("No userdata found in localStorage");
        }
      } catch (error) {
        console.error("Error accessing token from localStorage:", error);
      }
    };

    checkForToken();
    const intervalId = setInterval(checkForToken, 2000);

    return () => clearInterval(intervalId);
  }, [authToken]);

  useEffect(() => {
    if (!authToken) return;

    const loadOneSignalScript = () => {
      const script = document.createElement("script");
      script.src = "https://cdn.onesignal.com/sdks/OneSignalSDK.js";
      script.async = true;
      script.onload = initializeOneSignal;
      document.head.appendChild(script);
      console.log("Script loaded");
    };

    const initializeOneSignal = () => {
      if (window._oneSignalInitialized) return;

      window.OneSignal = window.OneSignal || [];
      window.OneSignal.push(function () {
        window.OneSignal.init({
          appId: "************************************",
          notifyButton: {
            enable: true,
          },
          allowLocalhostAsSecureOrigin: true,
          promptOptions: {
            slidedown: {
              enabled: true,
              autoPrompt: false,
            },
          },
        });

        window._oneSignalInitialized = true;
        setIsLoaded(true);

        window.OneSignal.isPushNotificationsEnabled(function (isEnabled) {
          setIsSubscribed(isEnabled);
          if (!isEnabled) {
            setTimeout(() => {
              handleSubscribe();
            }, 1000);
          } else {
            getPlayerIdSilently();
          }
        });

        window.OneSignal.getNotificationPermission(function (permission) {
          setNotificationPermission(permission);
        });

        window.OneSignal.getUserId(function (userId) {
          setPlayerId(userId);
        });
      });
    };

    if (!window._oneSignalInitialized) {
      if (!window.OneSignal || !window.OneSignal.push) {
        loadOneSignalScript();
      } else {
        initializeOneSignal();
      }
    } else {
      setIsLoaded(true);
      window.OneSignal.isPushNotificationsEnabled(function (isEnabled) {
        setIsSubscribed(isEnabled);
        if (!isEnabled) {
          handleSubscribe();
        } else {
          getPlayerIdSilently();
        }
      });
    }
  }, [authToken]);

  useEffect(() => {
    if (playerId && token) {
      notificationAPI();
    }
  }, [playerId, token]);

  const handleSubscribe = () => {
    if (!window.OneSignal) {
      console.log("OneSignal not loaded yet");
      return;
    }

    console.log("Automatically triggering OneSignal subscription prompt...");
    window.OneSignal.push(function () {
      window.OneSignal.showSlidedownPrompt();

      window.OneSignal.on("subscriptionChange", function (isSubscribed) {
        console.log("Subscription status changed:", isSubscribed);
        setIsSubscribed(isSubscribed);

        if (isSubscribed) {
          getPlayerIdSilently();
        }
      });
    });
  };

  const getPlayerIdSilently = () => {
    if (!window.OneSignal) return;

    window.OneSignal.push(function () {
      window.OneSignal.getUserId(function (userId) {
        console.log("Retrieved Player ID:", userId);
        setPlayerId(userId);

        if (userId) {
          localStorage.setItem("playerId", userId);
        } else {
          alert(
            "No Player ID available. Please subscribe to notifications first."
          );
        }
      });
    });
  };

  const getPlayerId = () => {
    if (!window.OneSignal) return;

    window.OneSignal.push(function () {
      window.OneSignal.getUserId(function (userId) {
        setPlayerId(userId);
        if (userId) {
          alert(`Your Player ID is: ${userId}`);
        } else {
          alert(
            "No Player ID available. Please subscribe to notifications first."
          );
        }
      });
    });
  };

  return null;
};

export default OneSignalDemo;
