<svg width="1920" height="4375" viewBox="0 0 1920 4375" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_558_623)">
<rect width="1920" height="4375" fill="white"/>
<g opacity="0.2" filter="url(#filter0_f_558_623)">
<ellipse cx="122.5" cy="234" rx="257.5" ry="284" fill="#FFC2C6"/>
</g>
<g opacity="0.2" filter="url(#filter1_f_558_623)">
<ellipse cx="47.5" cy="2643" rx="257.5" ry="284" fill="#FFC2C6"/>
</g>
<g opacity="0.2" filter="url(#filter2_f_558_623)">
<ellipse cx="47.5" cy="4095" rx="257.5" ry="284" fill="#FFC2C6"/>
</g>
<g opacity="0.5" filter="url(#filter3_f_558_623)">
<ellipse cx="1713" cy="277.5" rx="404" ry="445.5" fill="#F3E9FF"/>
</g>
<g opacity="0.5" filter="url(#filter4_f_558_623)">
<ellipse cx="1638" cy="2686.5" rx="404" ry="445.5" fill="#F3E9FF"/>
</g>
</g>
<defs>
<filter id="filter0_f_558_623" x="-485" y="-400" width="1215" height="1268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_558_623"/>
</filter>
<filter id="filter1_f_558_623" x="-560" y="2009" width="1215" height="1268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_558_623"/>
</filter>
<filter id="filter2_f_558_623" x="-560" y="3461" width="1215" height="1268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_558_623"/>
</filter>
<filter id="filter3_f_558_623" x="959" y="-518" width="1508" height="1591" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_558_623"/>
</filter>
<filter id="filter4_f_558_623" x="884" y="1891" width="1508" height="1591" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="175" result="effect1_foregroundBlur_558_623"/>
</filter>
<clipPath id="clip0_558_623">
<rect width="1920" height="4375" fill="white"/>
</clipPath>
</defs>
</svg>
