<svg width="430" height="2695" viewBox="0 0 430 2695" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_558_662)">
<rect width="430" height="2695" fill="white"/>
<g opacity="0.2" filter="url(#filter0_f_558_662)">
<ellipse cx="38" cy="86" rx="78" ry="86" fill="#FFC2C6"/>
</g>
<g opacity="0.5" filter="url(#filter1_f_558_662)">
<ellipse cx="364" cy="79.5" rx="93" ry="102.5" fill="#F3E9FF"/>
</g>
<g opacity="0.2" filter="url(#filter2_f_558_662)">
<ellipse cx="45" cy="1226" rx="78" ry="86" fill="#FFC2C6"/>
</g>
<g opacity="0.5" filter="url(#filter3_f_558_662)">
<ellipse cx="371" cy="1219.5" rx="93" ry="102.5" fill="#F3E9FF"/>
</g>
<g opacity="0.2" filter="url(#filter4_f_558_662)">
<ellipse cx="45" cy="2411" rx="78" ry="86" fill="#FFC2C6"/>
</g>
</g>
<defs>
<filter id="filter0_f_558_662" x="-90" y="-50" width="256" height="272" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="25" result="effect1_foregroundBlur_558_662"/>
</filter>
<filter id="filter1_f_558_662" x="221" y="-73" width="286" height="305" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="25" result="effect1_foregroundBlur_558_662"/>
</filter>
<filter id="filter2_f_558_662" x="-83" y="1090" width="256" height="272" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="25" result="effect1_foregroundBlur_558_662"/>
</filter>
<filter id="filter3_f_558_662" x="228" y="1067" width="286" height="305" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="25" result="effect1_foregroundBlur_558_662"/>
</filter>
<filter id="filter4_f_558_662" x="-83" y="2275" width="256" height="272" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="25" result="effect1_foregroundBlur_558_662"/>
</filter>
<clipPath id="clip0_558_662">
<rect width="430" height="2695" fill="white"/>
</clipPath>
</defs>
</svg>
