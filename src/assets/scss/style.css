body {
    font-family: "Ubuntu", sans-serif;
    background-image: url('../../assets/images/background.svg');
    background-position: 100% cover;
    background-repeat: no-repeat;
    background-size: cover;
    overflow: auto;
  }

  .sub-title {
    font-size: 16px;
    font-weight: 600;
    color: rgba(250, 36, 55, 1);
  }

  .red {
    color: rgba(250, 36, 55, 1);
    ;
  }

  .font {
    color: rgba(102, 102, 102, 1);
    font-size: 15.5px;
    line-height: 24px;
  }

  /* Stronger custom scrollbar for ChatModule - no edge difference */
.chat-scrollbar,
.chat-scrollbar * {
  scrollbar-width: thin;
  scrollbar-color: #563D39 #fff !important;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 12px;
}

.chat-scrollbar::-webkit-scrollbar-track,
.chat-scrollbar *::-webkit-scrollbar-track {
  background: #fff !important;
  border-radius: 8px;
  margin: 0;
}

.chat-scrollbar::-webkit-scrollbar-thumb,
.chat-scrollbar *::-webkit-scrollbar-thumb {
  background: #563D39;
  border-radius: 8px;
  /* border removed to fill the track */
}

.chat-scrollbar {
  scrollbar-gutter: stable;
}

  @media (max-width:600px) {
    body {
      background-image: url('../../assets/images/small-bg.svg');
      background-position: 100% cover;
      background-repeat: no-repeat;
      background-size: cover;
    }
  }