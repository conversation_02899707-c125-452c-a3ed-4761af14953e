// src/components/SidebarLayout.jsx
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { Outlet } from "react-router-dom";
import siteConstant from "../../helpers/constant/siteConstant";

const SidebarLayout = () => {
  const location = useLocation();
  const [currentTab, setCurrentTab] = useState("/privacy-policy");

  const navigate = useNavigate();

  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const isMobile = window.innerWidth <= 768; // You can use Tailwind's screen sizes too

  const handleNavigate = (link) => {
    setIsSidebarOpen(false);
    navigate(link);
  };

  useEffect(() => {
    setCurrentTab(location.pathname);
    console.log(location.pathname);
  }, [location.pathname]);

  return (
    <div className={`flex h-screen ${isSidebarOpen ? "overflow-hidden" : ""}`}>
      {/* Sidebar */}

      {/* Main Content */}
      <main
        className={`flex-grow items-center  transition-all duration-300 ${
          isSidebarOpen ? "md:ml-64" : ""
        }`}
      >
        {/* Hamburger Button */}

        {/* Mobile Navbar Tab Switcher */}
        <div
          className={`md:hidden bg-transparent max-w-full sm:max-w-auto backdrop-blur-lg fixed left-0 w-full z-99 flex justify-around py-3  outline-none md:ml-64 transition-all duration-300 ${
            isSidebarOpen ? "md:ml-64" : ""
          }`}
        >
          <div className={`md:hidden z-50 h-[24px] ml-2`}>
            <button
              onClick={toggleSidebar}
              className="text-gray-700 focus:outline-none"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                strokeWidth={2}
                stroke="currentColor"
                className="w-6 h-6"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M3 6h18M3 12h18m-6 6h6"
                />
              </svg>
            </button>
          </div>
          <div className="flex justify-around w-[80%] gap-2">
            <button
              className={`text-[12px] font-medium px-5 ${
                currentTab === "/privacy-policy"
                  ? "text-[#674941] border-b-2 border-[#674941]"
                  : "text-gray-700"
              }`}
              onClick={() => navigate("/privacy-policy")}
              style={{ paddingBottom: "12px" }}
            >
              Privacy Policy
            </button>
            <span className="w-[1px] bg-[#674941] block"></span>
            <button
              className={`text-[12px] font-medium px-5 ${
                currentTab === "/terms-of-use"
                  ? "text-[#674941] border-b-2 border-[#674941]"
                  : "text-gray-700"
              }`}
              onClick={() => navigate("/terms-of-use")}
              style={{ paddingBottom: "12px" }}
            >
              Terms & Conditions
            </button>
          </div>
        </div>

        {/* Page Content */}
        <div
          className="md:mt-1 sm:mt-2"
          style={{
            marginTop: isMobile && currentTab !== "/" ? "80px" : "80px", // Added top padding to prevent overlap
          }}
        >
          <Outlet />
        </div>
      </main>

      {/* Backdrop for mobile view */}
      {isSidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden"
          onClick={toggleSidebar}
        />
      )}
    </div>
  );
};

export default SidebarLayout;
