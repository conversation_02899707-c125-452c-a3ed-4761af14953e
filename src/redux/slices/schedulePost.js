import { createSlice } from "@reduxjs/toolkit";

export const scheduleSlice = createSlice({
  name: "schedule",
  initialState: {
    value: 0,
  },
  reducers: {
    scheduleData: (state, actions) => {
      state.value = actions.payload;
    },
  },
});

// Action creators are generated for each case reducer function
export const { scheduleData } = scheduleSlice.actions;

export default scheduleSlice.reducer;
