import React, { createContext, useEffect, useState } from "react";
import { useRoutes } from "react-router-dom";
import { HelmetProvider } from "react-helmet-async";
import { publicRoute, userRoutes } from "./routes.jsx";
import siteConstant from "./helpers/constant/siteConstant";
import { fetchFromStorage, saveToStorage } from "./helpers/context/storage";
import OneSignalInit from "./OneSignalInit";
import theme from "./views/components/Planner/theme.jsx";

import messagesEn from "./helpers/locales/en.json";
import { SocketContext, socket } from "./helpers/context/socket";
import {
  BrandProvider,
  UserProvider,
  useUser,
} from "./helpers/context/BrandContext";
import { ThemeProvider } from "@material-tailwind/react";

// const originalError = console.error;
// console.error = (...args) => {
//   if (
//     args[0] &&
//     typeof args[0] === "string" &&
//     args[0].includes("ResizeObserver loop completed")
//   ) {
//     return;
//   }
//   originalError(...args);
// };

const Context = createContext();

// Wrapper component that forces re-render when user changes
const AppContent = () => {
  const { selectedUser } = useUser();
  const localAuth = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
  const viewRoutes = localAuth ? userRoutes : publicRoute;
  const routing = useRoutes(viewRoutes);

  const menuMessages = {
    en: { ...messagesEn },
  };

  const [locale, setLocale] = useState("en");
  const [messages, setMessages] = useState(menuMessages[locale]);
  const [socialIcons, setSocialIcons] = useState([]);
  const [channels, setChannels] = useState([]);

  const switchLanguage = (lang) => {
    setLocale(lang);
    setMessages(menuMessages[lang]);
    saveToStorage(siteConstant?.INDENTIFIERS?.LANGUAGE, lang);
  };

  return (
    <div key={selectedUser?.user_id || "no-user"}>
      <HelmetProvider>
        <SocketContext.Provider value={socket}>
          <React.Suspense fallback="">
            <Context.Provider
              value={{
                locale,
                switchLanguage,
                messages,
                socialIcons,
                channels,
                userConnectedChannel: localAuth,
              }}
            >
              <OneSignalInit />
              {routing}
            </Context.Provider>
          </React.Suspense>
        </SocketContext.Provider>
      </HelmetProvider>
    </div>
  );
};

const App = () => {
  return (
    <ThemeProvider theme={theme}>
      <BrandProvider>
        <UserProvider>
          <AppContent />
        </UserProvider>
      </BrandProvider>
    </ThemeProvider>
  );
};

export default App;
export { Context as IntlContext };
