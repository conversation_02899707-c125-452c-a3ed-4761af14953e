import { useRef, useCallback } from "react";
import { useContext } from "react";
import { SocketContext } from "../helpers/context/socket";

const ICE_SERVERS = {
  iceServers: [
    { urls: "stun:stun.l.google.com:19302" },
    { urls: "stun:stun1.l.google.com:19302" },
    { urls: "stun:stun2.l.google.com:19302" },
    {
      urls: "turn:numb.viagenie.ca",
      username: "<EMAIL>",
      credential: "muazkh",
    },
    {
      urls: "turn:openrelay.metered.ca:80",
      username: "openrelayproject",
      credential: "openrelayproject",
    },
    {
      urls: "turn:openrelay.metered.ca:443",
      username: "openrelayproject",
      credential: "openrelayproject",
    },
    {
      urls: "turn:openrelay.metered.ca:443?transport=tcp",
      username: "openrelayproject",
      credential: "openrelayproject",
    },
  ],
  iceCandidatePoolSize: 10,
};

export const useWebRTC = () => {
  const socket = useContext(SocketContext);
  const peerConnectionsRef = useRef({});
  const pendingIceCandidatesRef = useRef({});
  const localStreamRef = useRef(null);

  const startLocalStream = useCallback(async () => {
    try {
      if (localStreamRef.current) {
        localStreamRef.current.getTracks().forEach((track) => track.stop());
      }

      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          frameRate: { ideal: 30 },
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
        },
      });

      localStreamRef.current = mediaStream;
      return mediaStream;
    } catch (error) {
      console.error("Error accessing media devices:", error);
      throw error;
    }
  }, []);

  const createPeerConnection = useCallback(
    (remoteUserId) => {
      const pc = new RTCPeerConnection(ICE_SERVERS);

      if (!pendingIceCandidatesRef.current[remoteUserId]) {
        pendingIceCandidatesRef.current[remoteUserId] = [];
      }

      pc.ontrack = (event) => {
        const remoteStream = event.streams[0];
        return remoteStream;
      };

      pc.onicecandidate = (event) => {
        if (event.candidate) {
          socket.emit("ice_candidate", {
            candidate: event.candidate,
            userId: localStorage.getItem("UserId"),
            targetUserId: remoteUserId,
            room: `liv-${localStorage.getItem("UserId")}`,
          });
        }
      };

      pc.onconnectionstatechange = () => {
        if (
          pc.connectionState === "failed" ||
          pc.connectionState === "disconnected"
        ) {
          cleanupConnection(remoteUserId);
        }
      };

      pc.onicegatheringstatechange = () => {
        console.log(
          `ICE gathering state for ${remoteUserId}:`,
          pc.iceGatheringState
        );
      };

      pc.onsignalingstatechange = () => {
        console.log(`Signaling state for ${remoteUserId}:`, pc.signalingState);
      };

      pc.onnegotiationneeded = async () => {
        try {
          const offer = await pc.createOffer();
          await pc.setLocalDescription(offer);

          socket.emit("offer", {
            offer: pc.localDescription,
            userId: localStorage.getItem("UserId"),
            targetUserId: remoteUserId,
            room: `liv-${localStorage.getItem("UserId")}`,
          });
        } catch (error) {
          console.error("Error during renegotiation:", error);
        }
      };

      peerConnectionsRef.current[remoteUserId] = pc;
      return pc;
    },
    [socket]
  );

  const cleanupConnection = useCallback((peerId) => {
    const pc = peerConnectionsRef.current[peerId];
    if (pc) {
      pc.close();
      delete peerConnectionsRef.current[peerId];
      delete pendingIceCandidatesRef.current[peerId];
    }
  }, []);

  const cleanupAllConnections = useCallback(() => {
    Object.keys(peerConnectionsRef.current).forEach(cleanupConnection);
    if (localStreamRef.current) {
      localStreamRef.current.getTracks().forEach((track) => track.stop());
      localStreamRef.current = null;
    }
  }, [cleanupConnection]);

  return {
    startLocalStream,
    createPeerConnection,
    cleanupConnection,
    cleanupAllConnections,
    localStreamRef,
    peerConnectionsRef,
    pendingIceCandidatesRef,
  };
};
