import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import apiInstance from "../../helpers/Axios/axiosINstance";
import { URL } from "../../helpers/constant/Url";
import { useContext } from "react";
import { SocketContext } from "../../helpers/context/socket";

export const useLiveStream = () => {
  const queryClient = useQueryClient();
  const socket = useContext(SocketContext);

  // Query for live users
  const { data: liveUsers, isLoading: isLoadingUsers } = useQuery({
    queryKey: ["liveUsers"],
    queryFn: async () => {
      const response = await apiInstance.get(URL.LIVE_USER);
      return response.data.data;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });

  // Mutation for creating a room
  const createRoomMutation = useMutation({
    mutationFn: async ({ roomId, userId }) => {
      return new Promise((resolve, reject) => {
        socket.emit("create_room", { room: roomId }, (response) => {
          if (response?.error) {
            reject(new Error(response.error));
          } else {
            resolve(response);
          }
        });
      });
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries(["liveUsers"]);
    },
  });

  // Mutation for joining a room
  const joinRoomMutation = useMutation({
    mutationFn: async ({ roomId }) => {
      return new Promise((resolve, reject) => {
        socket.emit("join_room", { room: roomId }, (response) => {
          if (response?.error) {
            reject(new Error(response.error));
          } else {
            resolve(response);
          }
        });
      });
    },
  });

  // Mutation for leaving a room
  const leaveRoomMutation = useMutation({
    mutationFn: async ({ roomId, userId }) => {
      return new Promise((resolve, reject) => {
        socket.emit("leave_room", { room: roomId, userId }, (response) => {
          if (response?.error) {
            reject(new Error(response.error));
          } else {
            resolve(response);
          }
        });
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries(["liveUsers"]);
    },
  });

  return {
    liveUsers,
    isLoadingUsers,
    createRoom: createRoomMutation.mutateAsync,
    joinRoom: joinRoomMutation.mutateAsync,
    leaveRoom: leaveRoomMutation.mutateAsync,
    isCreatingRoom: createRoomMutation.isLoading,
    isJoiningRoom: joinRoomMutation.isLoading,
    isLeavingRoom: leaveRoomMutation.isLoading,
    error:
      createRoomMutation.error ||
      joinRoomMutation.error ||
      leaveRoomMutation.error,
  };
};
