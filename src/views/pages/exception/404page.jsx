import React, { useContext } from "react";
import datanotBG from "../../../assets/images/datanotBG.svg";
import img404 from "../../../assets/images/page-not-found.svg";
import Logo from "../../../assets/images/logo.svg";
import { useNavigate } from "react-router-dom";
import { IntlContext } from "../../../App";

const PageNotFound = () => {
  const navigate = useNavigate();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  return (
    <div className="flex flex-col relative h-screen justify-center items-center font-Ubuntu">
      <div
        className="absolute inset-0 bg-cover bg-center z-0"
        style={{ backgroundImage: `url(${datanotBG})` }}
      ></div>
      <div className="logo absolute top-2 left-3 right-0  sm:h-36 sm:w-36  h-28 w-28 sm:mt-4 mt-2">
        <img
          src={Logo}
          alt="Logo"
          className="cursor-pointer"
          onClick={() => navigate("/")}
        />
      </div>
      <div className="relative z-10 flex flex-col items-center">
        <img
          src={img404}
          alt="Data Not Found"
          className="max-w-xs md:max-w-2xl mb-2"
        />
        <div className="mb-5 sm:mb-8 text-center">
          <p className="text-lg sm:text-2xl font-Ubuntu font-bold">
            {localesData.USER_WEB.SORRY_PAGE_NOT_FOUND}
          </p>
          <p className="text-[11px] px-4 sm:px-0 sm:text-sm text-gray-400 font-Ubuntu pt-2 sm:w-[420px]">
            {
              localesData?.USER_WEB
                ?.SORRY_WE_COULD_FIND_THE_PAGE_YOU_RE_LOOKING_FOR_PERHAPS_YOU_VE_MISTYPED_THE_RUL_BR_SURE_TO_CHECKYOUR_SPELLING
            }
          </p>
        </div>
        <div className="flex justify-center">
          <button
            className="bg-Red text-white font-bold py-2 sm:px-8 px-6 rounded-xl text-[12px] sm:text-md"
            onClick={() => navigate("/")}
          >
            {localesData.USER_WEB.GO_TO_HOME}
          </button>
        </div>
      </div>
    </div>
  );
};

export default PageNotFound;
