// import React from "react";
// import Dashboard from "../../components/dashboard";

// const DashboardModule = () => {
//     return (
//         <div>
//             <div className="max-w-sm p-6 rounded-lg shadow-lg bg-white relative overflow-hidden">
//                 <div className="absolute inset-0 bg-gradient-to-b from-transparent to-blue-900 opacity-10 pointer-events-none"></div>

//                 <div className="relative z-10">
//                     <div className="text-red-500 font-bold text-2xl">Total Post</div>
//                     <div className="text-6xl font-extrabold text-gray-900 mt-2">256</div>
//                     <div className="text-gray-500 mt-1 flex items-center">
//                         <span className="text-sm">This Month</span>
//                         <span className="text-red-500 ml-2 animate-bounce">&#x2193;</span>
//                     </div>

//                     <div className="border-t border-gray-200 pt-4 mt-4">
//                         <div className="flex justify-between items-center mb-2">
//                             <span className="text-gray-700 text-lg">Reported Post</span>
//                             <span className="text-red-500 text-lg">&#x2193; 50</span>
//                         </div>
//                         <div className="flex justify-between items-center mb-2">
//                             <span className="text-gray-700 text-lg">Banned Post</span>
//                             <span className="text-gray-900 text-lg">156</span>
//                         </div>
//                         <div className="flex justify-between items-center">
//                             <span className="text-gray-700 text-lg">Active Post</span>
//                             <span className="text-gray-900 text-lg">50</span>
//                         </div>
//                     </div>

//                     <div className="mt-6 text-center">
//                         <button className="text-base font-semibold text-indigo-600 hover:underline transition-all duration-300 ease-in-out transform hover:scale-105">
//                             See Full History
//                         </button>
//                     </div>
//                 </div>
//             </div>
//             <Dashboard />
//         </div>
//     )
// }
// export default DashboardModule