// import { InputAdornment, Typography } from "@mui/material";
// import React, { useState, useContext, useEffect, useRef } from "react";
// import { IntlContext } from "../../../App";
// import apiInstance from "../../../helpers/Axios/axiosINstance";
// import { URL } from "../../../helpers/constant/Url";
// import { setApiMessage } from "../../../helpers/context/toaster";
// import { CustomTextField } from "../../../views/components/custom/CustomTextField";
// import styled from "@emotion/styled";
// import { Badge } from "@mui/material";
// import SocialMediaIcons from "../../../views/components/profile/SocialMediaIcons";
// import { renderHighlightedText } from "../../../helpers/context/common";
// import { RiDeleteBin6Line } from "react-icons/ri";
// import DeletePostModel from "../../../views/components/profile/deletePost";
// import Pagination from "../../common/paginationCommon.jsx";
// import Loader from "../../../helpers/UI/Loader";
// import { renderThumbnail } from "../../../helpers/constant/utils";
// import { useNavigate } from "react-router-dom";
// import { BsThreeDotsVertical } from "react-icons/bs";
// import Android12Switch from "../../../views/components/UploadPost/Android12Switch.jsx";
// import Popover from "@mui/material/Popover";

// const StyledBadge = styled(Badge)(({ theme }) => ({
//   "& .MuiBadge-badge": {
//     right: 4,
//     top: 52,
//     border: `1px solid gray`,
//     padding: "6.5px",
//     borderRadius: "50%",
//     backgroundColor: "white",
//     color: "black",
//     fontSize: "0.75rem",
//   },
// }));

// const PostDetails = () => {
//   const navigate = useNavigate();
//   const [searchQuery, setSearchQuery] = useState("");
//   const [currentPage, setCurrentPage] = useState(1);
//   const [postsPerPage, setPostsPerPage] = useState(8);
//   const intlContext = useContext(IntlContext);
//   const localesData = intlContext?.messages;
//   const [postDetails, setpostDetails] = useState(null);
//   const [selectedPost, setSelectedPost] = useState(null);
//   const [loading, setLoading] = useState(true);
//   const playerRef = useRef(null);
//   const [open, setOpen] = useState(false);
//   const [totalPages, setTotalPages] = useState(1);
//   const [anchorEl, setAnchorEl] = React.useState(null);
//   const openPopup = Boolean(anchorEl);
//   const id = open ? "simple-popover" : undefined;

//   const fetchPosts = async (page) => {
//     try {
//       const { status, data } = await apiInstance.get(
//         `${URL.POST_MANAGEMENT_GET}?page=${page}`
//       );
//       if (data?.results?.status) {
//         setpostDetails(data?.results?.data);
//         setTotalPages(Math.ceil(data?.count / postsPerPage));
//       } else {
//         setApiMessage("error", data?.results?.message || data?.message);
//       }
//     } catch (error) {
//       console.error("Error fetching posts:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   const handleClick = (event) => {
//     setAnchorEl(event.currentTarget);
//   };
//   const handleClose = () => {
//     setAnchorEl(null);
//   };

//   const handleClickOpen = (post) => {
//     setSelectedPost(post);
//     setOpen(true);
//   };

//   const handleDialogClose = () => setOpen(false);

//   useEffect(() => {
//     fetchPosts(currentPage);
//   }, [currentPage]);

//   const handlePrevious = () => {
//     if (currentPage > 1) {
//       setCurrentPage(currentPage - 1);
//     }
//   };

//   const handleNext = () => {
//     if (currentPage < totalPages) {
//       setCurrentPage(currentPage + 1);
//     }
//   };

//   const handlePageClick = (page) => {
//     setCurrentPage(page);
//   };

//   const handleSearchChange = (e) => {
//     setSearchQuery(e.target.value);
//     setCurrentPage(1);
//   };

//   if (loading) {
//     return (
//       <div>
//         <Loader />
//       </div>
//     );
//   }
//   if (!postDetails || postDetails.length === 0) {
//     return <div>{localesData?.USER_WEB?.NO_POST_AVAILABLE}</div>;
//   }

//   return (
//     <div className="bg-primarBG duration-500 pt-8 grid-col-1 px-8 pb-36">
//       {/* Search input */}
//       <div className="flex flex-row  justify-between">
//         <h1 className="text-xl font-bold pt-4">
//           {localesData?.USER_WEB?.POST_MANAGEMENT}
//         </h1>
//         <CustomTextField
//           variant="outlined"
//           borderRadius="40px"
//           type="text"
//           placeholder="Search..."
//           value={searchQuery}
//           onChange={handleSearchChange}
//           className="border border-gray-300 h-3"
//           InputProps={{
//             startAdornment: (
//               <InputAdornment position="start">
//                 <svg
//                   className="w-4 h-4 text-gray-500 dark:text-gray-400"
//                   aria-hidden="true"
//                   xmlns="http://www.w3.org/2000/svg"
//                   fill="none"
//                   viewBox="0 0 20 20"
//                 >
//                   <path
//                     stroke="currentColor"
//                     strokeLinecap="round"
//                     strokeLinejoin="round"
//                     strokeWidth={2}
//                     d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
//                   />
//                 </svg>
//               </InputAdornment>
//             ),
//           }}
//         />
//       </div>
//       <div className="overflow-x-auto pt-9">
//         <table className="min-w-full divide-y divide-gray-200">
//           <thead>
//             <tr className="bg-profileCardBG text-Red uppercase text-sm">
//               <th className="py-5 px-6 text-center">
//                 {localesData?.USER_WEB?.POST_ID}
//               </th>
//               <th className="py-5 px-6 text-center">
//                 {localesData?.USER_WEB?.USER_PROFILE?.POST}
//               </th>
//               <th className="py-5 px-6 text-center">
//                 {localesData?.USER_WEB?.USER_NAME}
//               </th>
//               <th className="py-5 px-6 text-center">
//                 {localesData?.USER_WEB?.USER_PROFILE?.DESCRIPTION}
//               </th>
//               <th className="py-5 px-6 text-center">
//                 {localesData?.USER_WEB?.THIRD_PARTY}
//               </th>
//               <th className="py-5 px-6 text-center">
//                 {localesData?.USER_WEB?.ACTIONS}
//               </th>
//             </tr>
//           </thead>
//           <tbody className="text-gray-600 mt-5 bg-white text-sm font-light">
//             {postDetails?.map((postData, index) => (
//               <tr
//                 key={postData?.post_id}
//                 className={`border-b border-gray-200 hover:bg-gray-50 bg-white`}
//               >
//                 <td className="py-3 px-6 text-center">{postData?.post_id}</td>
//                 <td className="px-4 py-3 font-medium text-gray-900 dark:text-white flex items-center justify-center">
//                   <StyledBadge
//                     badgeContent={
//                       postData?.post_count ? postData?.post_count - 1 : 0
//                     }
//                   >
//                     {renderThumbnail(postData?.post_file, playerRef)}
//                   </StyledBadge>
//                 </td>
//                 <td className="py-3 px-6 text-center whitespace-nowrap">
//                   <div className="flex items-center justify-center">
//                     <span>{postData?.user_id}</span>
//                   </div>
//                 </td>
//                 <td className="px-2 py-4 text-center text-gray-700">
//                   <p
//                     className={`font-Ubuntu ${
//                       postData?.description.length > 10
//                         ? "break-words line-clamp-1 md:line-clamp-none"
//                         : "break-words line-clamp-1 md:line-clamp-none"
//                     }`}
//                   >
//                     {postData?.description
//                       ? renderHighlightedText(postData?.description)
//                       : "No description available"}
//                   </p>
//                 </td>
//                 <td className="px-4 py-4">
//                   <div className="-space-x-2 rtl:space-x-reverse flex text-center items-center justify-center">
//                     {postData && (
//                       <SocialMediaIcons
//                         platforms={postData}
//                         imgStyle="w-12 h-12 border-2 shadow bg-white border-gray-200 rounded-full dark:border-gray-800 p-1"
//                       />
//                     )}
//                   </div>
//                 </td>
//                 <td className="sm:px-2 py-4 relative">
//                   <div className="flex justify-center text-Red bg-gray">
//                     <div className="bg-gray-100 rounded-md flex justify-center items-center h-11 w-11 me-3 hover:bg-[#efebe9]">
//                       <RiDeleteBin6Line
//                         style={{ height: "27px", width: "30px" }}
//                         onClick={() => handleClickOpen(postData)}
//                       />
//                     </div>
//                     <div
//                       className="bg-gray-100 rounded-md flex justify-center items-center h-11 w-11 hover:bg-[#efebe9]  cursor-pointer"
//                       onClick={handleClick}
//                     >
//                       <BsThreeDotsVertical
//                         style={{
//                           height: "25px",
//                           color: "black",
//                           width: "20px",
//                         }}
//                       />
//                     </div>
//                     <Popover
//                       id={id}
//                       open={openPopup}
//                       anchorEl={anchorEl}
//                       onClose={handleClose}
//                       anchorOrigin={{
//                         vertical: "bottom",
//                         horizontal: "left",
//                       }}
//                       PaperProps={{
//                         sx: {
//                           boxShadow: "1px 1px 3px rgba(255, 242, 242)",
//                           backgroundColor: "white",
//                           borderRadius: "10px",
//                         },
//                       }}
//                     >
//                       <div className="flex items-center gap-2 pe-4 p-1  shadow-2xl ">
//                         <Android12Switch />
//                         <Typography sx={{ p: 1 }}>
//                           {localesData?.USER_WEB?.BANNED}
//                         </Typography>
//                       </div>
//                     </Popover>
//                   </div>
//                 </td>
//               </tr>
//             ))}
//           </tbody>
//         </table>
//       </div>
//       <Pagination
//         totalPages={totalPages}
//         currentPage={currentPage}
//         handlePrevious={handlePrevious}
//         handleNext={handleNext}
//         handlePageClick={handlePageClick}
//       />
//       <DeletePostModel
//         open={open}
//         handleDialogClose={handleDialogClose}
//         post={selectedPost}
//         fetchPosts={fetchPosts}
//         postDeleteURL={URL.ADMIN_POST_DELETE}
//       />
//     </div>
//   );
// };

// export default PostDetails;
