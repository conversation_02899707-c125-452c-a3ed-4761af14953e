import React, { useContext, useEffect, useState, useRef } from "react";
import { RiDeleteBin6Line } from "react-icons/ri";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { setApiMessage } from "../../../../helpers/context/toaster";
import { IntlContext } from "../../../../App";
import { URL } from "../../../../helpers/constant/Url";
import Pagination from "../../common/paginationCommon.jsx";
import siteConstant from "../../../../helpers/constant/siteConstant";
import DeletePostModel from "../../../components/profile/deletePost";
import TableSkeleton, { CardSkeleton } from "../../../../helpers/UI/TableSkeleton";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { format } from "date-fns";
// import Android12Switch from "../../../views/components/UploadPost/Android12Switch.jsx";

const UserManagement = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 15;
  const [userlist, setUserlist] = useState([]);
  const [loading, setLoading] = useState(true);
  const [totalPages, setTotalPages] = useState(1);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [selectedUser, setselectedUser] = useState(null);
  const [additionalData, setAdditionalData] = useState({});
  const [dateRange, setDateRange] = useState([null, null]);
  const [startDate, endDate] = dateRange;
  const containerRef = useRef(null);
  const cardsRef = useRef(null);

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const fetchUserManage = async (page, from = startDate, to = endDate) => {
    try {
      let params = `?page=${page}`;
      if (from) {
        params += `&from_date=${format(new Date(from), "yyyy-MM-dd")}`;
      }
      if (to) {
        params += `&to_date=${format(new Date(to), "yyyy-MM-dd")}`;
      }
      const { data } = await apiInstance.get(URL.USER_MANAGEMENT_API + params);
      const users = data?.results?.data || [];
      setUserlist(users);
      setTotalPages(Math.ceil(data?.count / itemsPerPage));
      setAdditionalData(data?.results?.additional_data || {});
    } catch (error) {
      console.error("Error fetching profile:", error);
      setApiMessage("error", error?.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUserManage(currentPage, startDate, endDate);
    // eslint-disable-next-line
  }, [currentPage]);

  // Scroll to top of cards after loading is done on page change
  useEffect(() => {
    if (!loading && cardsRef.current) {
      cardsRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [currentPage, loading]);

  const handlePrevious = () => {
    if (currentPage > 1) {
      setLoading(true);
      setCurrentPage(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      setLoading(true);
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePageClick = (page) => {
    setLoading(true);
    setCurrentPage(page);
  };

  const handleDialogClose = () => setDeleteOpen(false);
  const handleClickOpen = (user) => {
    if (!user.is_deleted) {
      setselectedUser(user);
      setDeleteOpen(true);
    }
  };

  const handleDateRangeChange = (update) => {
    setDateRange(update);
  };

  const handleSearchSubmit = (e) => {
    e.preventDefault();
    setLoading(true);
    setCurrentPage(1);
    fetchUserManage(1, startDate, endDate);
  };

  if (loading) {
    return (
      <div className="mt-[100px]">
        <CardSkeleton count={6} />
        <TableSkeleton rows={itemsPerPage} />
      </div>
    );
  }

  return (
    <div
      ref={containerRef}
      className="ps-3 pe-2 sm:px-4 sm:pt-4 overflow-auto bg-100"
    >
      <div className="flex justify-between items-center">
        <h2 className="text-[16px] md:text-[18px] lg:text-[20px] mt-[80px] font-bold mb-0 ms-1 pt-5">
          {localesData?.USER_WEB?.USER_MANAGEMENT?.USER_TITLE}
        </h2>
      </div>

      {/* End Search and Date Range Filter */}
      {/* Additional Data Cards */}
      <div
        ref={cardsRef}
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mt-6 mb-8"
      >
        <div className="bg-white rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-Red">
            {additionalData.users_in_last_24h ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 24h
          </span>
        </div>
        <div className="bg-white rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-Red">
            {additionalData.users_in_last_7d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 7 days
          </span>
        </div>
        <div className="bg-white rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-Red">
            {additionalData.users_in_last_30d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 30 days
          </span>
        </div>
        <div className="bg-white rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-Red">
            {additionalData.users_in_last_90d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 90 days
          </span>
        </div>
        <div className="bg-white rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-Red">
            {additionalData.users_in_last_180d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 180 days
          </span>
        </div>
        <div className="bg-white rounded-xl shadow p-5 flex flex-col items-center">
          <span className="text-2xl font-bold text-Red">
            {additionalData.users_in_last_365d ?? 0}
          </span>
          <span className="text-gray-500 text-sm mt-2 text-center">
            Users in last 365 days
          </span>
        </div>
      </div>
      {/* Search and Date Range Filter */}
      <form
        onSubmit={handleSearchSubmit}
        className="flex flex-col md:flex-row gap-4 items-center mb-2 mt-4"
      >
        <div className="w-full md:w-auto bg-white rounded-xl shadow p-4 flex flex-col md:flex-row items-center gap-4 border border-gray-100">
          <label className="block text-gray-700 font-semibold mb-2 md:mb-0 md:mr-4 text-sm md:text-base">
            Date Range Filter
          </label>
          <div className="flex gap-2 items-center">
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none z-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3.75 7.5h16.5M4.5 21h15a.75.75 0 00.75-.75V7.5a.75.75 0 00-.75-.75h-15a.75.75 0 00-.75.75v12.75c0 .414.336.75.75.75z"
                  />
                </svg>
              </span>
              <DatePicker
                selected={startDate}
                onChange={(date) => handleDateRangeChange([date, endDate])}
                selectsStart
                startDate={startDate}
                endDate={endDate}
                placeholderText="Start Date"
                dateFormat="yyyy-MM-dd"
                className="border border-gray-300 rounded-md px-10 py-2 pr-10 focus:border-Red focus:ring-2 focus:ring-Red/20 transition-all duration-150 shadow-sm hover:border-Red"
                maxDate={endDate || null}
                isClearable
              />
            </div>
            <span className="mx-1 text-gray-500 font-semibold">to</span>
            <div className="relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 pointer-events-none z-10">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                  strokeWidth={1.5}
                  stroke="currentColor"
                  className="w-5 h-5"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M6.75 3v2.25M17.25 3v2.25M3.75 7.5h16.5M4.5 21h15a.75.75 0 00.75-.75V7.5a.75.75 0 00-.75-.75h-15a.75.75 0 00-.75.75v12.75c0 .414.336.75.75.75z"
                  />
                </svg>
              </span>
              <DatePicker
                selected={endDate}
                onChange={(date) => handleDateRangeChange([startDate, date])}
                selectsEnd
                startDate={startDate}
                endDate={endDate}
                placeholderText="End Date"
                dateFormat="yyyy-MM-dd"
                className="border border-gray-300 rounded-md px-10 py-2 pr-10 focus:border-Red focus:ring-2 focus:ring-Red/20 transition-all duration-150 shadow-sm hover:border-Red"
                minDate={startDate || null}
                isClearable
              />
            </div>
          </div>
        </div>
        <button
          type="submit"
          className="bg-Red text-white px-6 py-2 rounded-md font-semibold hover:bg-red-600 focus:ring-2 focus:ring-Red/30 transition-all duration-150 shadow-md"
        >
          Search
        </button>
      </form>
      {/* End Additional Data Cards */}
      <div className="py-6">
        <div className="min-w-full container overflow-x-auto channel-scrollbar font-Ubuntu ">
          <table className="min-w-full table-auto text-center text-gray-500 dark:text-gray-400 border-separate border-spacing-y-4 ">
            <thead>
              <tr className="bg-profileCardBG text-Red font-Ubuntu whitespace-nowrap ps-6 rounded-md">
                <th className="py-6 text-[14px] sm:text-[15px] ps-3">S. No.</th>
                <th className="py-6 text-[14px] sm:text-[15px] ps-3">
                  User ID
                </th>
                <th className="py-8 text-[14px] sm:text-[15px] ps-3">
                  {localesData?.USER_WEB?.USER_MANAGEMENT?.PROIFLE_IMAGE}
                </th>
                <th className="text-[14px] sm:text-[15px] text-left ps-14">
                  {localesData?.USER_WEB?.USER_MANAGEMENT?.USERNAME}
                </th>
                <th className="text-[14px] sm:text-[15px] text-left ps-16">
                  {localesData?.USER_WEB?.USER_MANAGEMENT?.EMAIL}
                </th>
                <th className="text-[14px] sm:text-[15px] ">Date</th>
                <th className="text-[14px] sm:text-[15px] ">
                  Social Media Connected
                </th>
                <th className="text-[14px] sm:text-[15px] ">
                  {localesData?.USER_WEB?.USER_PROFILE?.POST}
                </th>
                <th className="text-[14px] sm:text-[15px] ">
                  {localesData?.USER_WEB?.USER_PROFILE?.STATUS}
                </th>
                <th className="text-[14px] sm:text-[15px]">
                  {localesData?.USER_WEB?.USER_PROFILE?.ACTION}
                </th>
              </tr>
            </thead>
            <tbody className="font-Ubuntu">
              {userlist.map((user, index) => (
                <tr
                  key={user.id}
                  className="border-b bg-white hover:bg-primarBG"
                >
                  <td className="p-5">
                    <p className="block antialiased text-sm font-Ubuntu leading-normal text-Red font-bold">
                      {(currentPage - 1) * itemsPerPage + index + 1}
                    </p>
                  </td>
                  <td className="p-5">
                    <p className="block antialiased text-sm font-Ubuntu leading-normal text-Red font-bold">
                      {user.id}
                    </p>
                  </td>
                  <td className="">
                    <div className="flex items-center justify-center gap-3 py-2">
                      <img
                        src={
                          user.profile_image ||
                          siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                        }
                        alt={user.username}
                        className="border-2 border-Red p-[5px] rounded-[20px] lg:rounded-[25px] object-cover h-14 w-14 sm:h-14 sm:w-14 lg:h-16 lg:w-16"
                      />
                    </div>
                  </td>
                  <td className="p-5 text-left">
                    <p className=" ps-10 block antialiased  text-sm font-Ubuntu leading-normal text-Red font-normal">
                      {user.username}
                    </p>
                  </td>
                  <td className="p-5 text-left">
                    <p className="block antialiased  text-sm font-Ubuntu leading-normal text-Red font-normal">
                      {user.email}
                    </p>
                  </td>
                  <td className="p-5">
                    <p className="block antialiased  text-sm font-Ubuntu leading-normal text-Red font-normal whitespace-nowrap">
                      {new Date(user.date).toLocaleDateString()}
                    </p>
                  </td>
                  <td className="p-5">
                    <p className="block antialiased  text-sm font-Ubuntu leading-normal text-Red font-normal whitespace-nowrap">
                      {user.connected_social_platforms}
                    </p>
                  </td>
                  <td className="p-5">
                    <p className="block antialiased  text-sm leading-normal text-Red font-normal whitespace-nowrap">
                      {user.posts}
                    </p>
                  </td>
                  <td className="p-5">
                    <p className="block antialiased   justify-center text-sm leading-normal text-Red font-normal whitespace-nowrap">
                      {user.is_deleted ? (
                        <span className="text-white font-semibold bg-red-500 px-5 py-2 rounded-xl text-center">
                          {localesData?.USER_WEB?.DELETE}
                        </span>
                      ) : (
                        <span className="text-white font-semibold bg-green-500 px-5 py-2 rounded-xl justify-center">
                          {localesData?.USER_WEB?.USER_MANAGEMENT?.ACTIVE}
                        </span>
                      )}
                    </p>
                  </td>
                  <td className="py-4 flex justify-center gap-4 px-5">
                    {!user.is_deleted && (
                      <div
                        className="bg-gray-50 rounded-md flex justify-center items-center text-Red h-12 w-12 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleClickOpen(user)}
                        aria-disabled={user?.is_deleted}
                      >
                        <RiDeleteBin6Line
                          className="text-Red"
                          style={{ height: "25px", width: "28px" }}
                        />
                      </div>
                    )}
                    {/* <div
                      className="bg-gray-100 rounded-md flex justify-center items-center h-12 w-12 hover:bg-profileCardBGcursor-pointer"
                      onClick={handleClick}
                    >
                      <BsThreeDotsVertical
                        style={{ height: "25px", width: "20px" }}
                      />
                    </div> */}
                    {/* <Popover
                      id={id}
                      open={open}
                      anchorEl={anchorEl}
                      onClose={handleClose}
                      anchorOrigin={{
                        vertical: "bottom",
                        horizontal: "left",
                      }}
                      PaperProps={{
                        sx: {
                          boxShadow: "1px 1px 3px rgba(255, 242, 242)",
                          backgroundColor: "white",
                          borderRadius: "10px",
                        },
                      }}
                    >
                      <div className="flex items-center gap-2 pe-4 p-1 shadow-2xl ">
                        <Android12Switch />
                        <Typography sx={{ p: 1 }}>Banned</Typography>
                      </div>
                    </Popover> */}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        <div className="mb-20">
          <Pagination
            totalPages={totalPages}
            currentPage={currentPage}
            handlePrevious={handlePrevious}
            handleNext={handleNext}
            handlePageClick={handlePageClick}
          />
        </div>
      </div>
      <DeletePostModel
        open={deleteOpen}
        handleDialogClose={handleDialogClose}
        post={selectedUser}
        fetchPosts={fetchUserManage}
        postDeleteURL={URL.ADMIN_USER_DELETE}
      />
    </div>
  );
};

export default UserManagement;
