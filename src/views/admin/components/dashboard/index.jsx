// import React, { useContext, useEffect, useState, useRef } from "react";
// import { IntlContext } from "../../../../App";
// import "./Linechart.css";
// import { FaArrowDown } from "react-icons/fa";
// import { MdOutlineArrowDropDown, MdOutlineArrowDropUp } from "react-icons/md";
// import UserManageChart from "./UserManageChart";
// import { URL } from "../../../../helpers/constant/Url";
// import apiInstance from "../../../../helpers/Axios/axiosINstance";
// import Loader from "../../../../helpers/UI/Loader";
// import Postslider from "../../../common/postslider";
// import { IoArrowDown } from "react-icons/io5";
// import mostpostex from "../../../../assets/images/svg_icon/most_valuable.svg";

// const Dashboard = ({ posts }) => {
//   const intlContext = useContext(IntlContext);
//   const localesData = intlContext?.messages || {};
//   const [cardData, setCardData] = useState([]);
//   const [mostValuablePost, setMostValuablePost] = useState([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const [showDropdown, setShowDropdown] = useState(null);
//   const dropdownRefs = useRef([]);

//   const fetchDashboard = async () => {
//     try {
//       const { data } = await apiInstance.get(URL.ADMIN_DASHBOARD);
//       const { users, posts, post } = data;
//       console.log("data", data.post[0].is_liked);

//       setCardData([
//         {
//           title: "Total Users",
//           count: users.total_user,
//           details: [
//             {
//               label: "Suspend User",
//               value: users.total_user,
//               icon: <IoArrowDown className="text-Red" size={10} />,
//             },
//             { label: "Banned User", value: users.banned_user },
//             { label: "Active User", value: users.active_users },
//             { label: "Pending User", value: users.new_users },
//           ],
//           icon: <FaArrowDown className="text-Red" size={10} />,
//         },
//         {
//           title: "Total Post",
//           count: posts.total_post,
//           details: [
//             {
//               label: "Reported Post",
//               value: posts.reported_post,
//               icon: <IoArrowDown className="text-Red" size={10} />,
//             },
//             { label: "Banned Post", value: posts.banned_posts },
//             { label: "Active Post", value: posts.active_posts },
//           ],
//           icon: <FaArrowDown size={10} color="#B9A08B " />,
//         },
//         {
//           title: "New Users",
//           count: users.new_users,
//           details: [
//             {
//               label: "Reported Post",
//               value: posts.reported_post,
//               icon: <IoArrowDown className="text-Red" size={10} />,
//             },
//             { label: "Banned Post", value: posts.banned_posts },
//             { label: "Active Post", value: posts.active_posts },
//           ],
//           icon: <FaArrowDown className="text-Red" size={12} />,
//         },
//       ]);
//       setMostValuablePost(post);
//       setIsLoading(false);
//     } catch (error) {
//       console.error("Error fetching dashboard data:", error);
//     }
//   };

//   const toggleDropdown = (index) => {
//     setShowDropdown(showDropdown === index ? null : index);
//   };

//   const handleClickOutside = (event) => {
//     if (
//       dropdownRefs.current.every((ref) => ref && !ref.contains(event.target))
//     ) {
//       setShowDropdown(null);
//     }
//   };
//   useEffect(() => {
//     fetchDashboard();
//   }, []);

//   useEffect(() => {
//     document.addEventListener("mousedown", handleClickOutside);
//     return () => {
//       document.removeEventListener("mousedown", handleClickOutside);
//     };
//   }, [showDropdown]);

//   if (isLoading) {
//     return <Loader />;
//   }

//   return (
//     <div className="mx-3">
//       <div className="mt-4">
//         <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5 mb-5">
//           {cardData?.map((card, index) => (
//             <div
//               key={index}
//               className={`w-full card mx-auto flex flex-col relative ${
//                 showDropdown === index ? "z-50" : ""
//               }`}
//             >
//               <div className="flex flex-col flex-grow">
//                 <div
//                   className={`flex items-center top-card rounded-tl-xl rounded-tr-xl justify-between rounded-lg bg-white  p-5 relative transition-shadow duration-300 ease-in-out ${
//                     showDropdown === index ? "shadow-lg z-40" : ""
//                   }`}
//                 >
//                   <div>
//                     <p className="admin-title mb-4 font-Ubuntu font-bold 2xl:text-[23px] xl:text-[21px] text-[19px] leading-[43px] text-Red whitespace-nowrap">
//                       {card.title}
//                     </p>
//                     <div className="flex items-center gap-3">
//                       <p className="mb-0 font-bold text-Red text-[17px] lg:text-[18px] 2xl:text-[21px] leading-[43px] admin-count">
//                         {card.count}
//                       </p>
//                       <span className="ms-2 ">{card.icon}</span>
//                       <div className="flex items-center">
//                         <span>{card.change}</span>
//                         <div className="border-l-2 border-[#07102F] opacity-20 h-5 mr-2 rounded"></div>
//                         <p className="text-[12px] text-[#0A043C] opacity-50 whitespace-nowrap admin-month me-4">
//                           {localesData?.USER_WEB?.USER_PROFILE?.THIS_MONTH}
//                         </p>
//                       </div>
//                     </div>
//                   </div>
//                   <div className="flex items-center gap-3">
//                     <p className="text-Red font-Ubuntu font-normal text-sm leading-[30px] admin-lasets">
//                       {localesData?.USER_WEB?.USER_PROFILE?.LATEST}
//                     </p>
//                     <div className="relative flex items-center">
//                       <span className="absolute inset-0 bg-[#A4836B] opacity-5 rounded-full"></span>
//                       <span
//                         role="button"
//                         onClick={() => toggleDropdown(index)}
//                         className="relative p-1.5 text-Red rounded-full"
//                       >
//                         {showDropdown === index ? (
//                           <MdOutlineArrowDropUp size={20} />
//                         ) : (
//                           <MdOutlineArrowDropDown size={20} />
//                         )}
//                       </span>
//                     </div>
//                   </div>
//                 </div>

//                 <div
//                   ref={(el) => (dropdownRefs.current[index] = el)}
//                   className={` absolute rounded-bl-xl rounded-br-xl bg-white  top-full left-0 w-full text-black transition-all duration-300 ease-in-out ${
//                     showDropdown === index
//                       ? "max-h-80 opacity-100 z-30 shadow-lg"
//                       : "max-h-0 opacity-0"
//                   }`}
//                 >
//                   <ul className="pt-3 p-5">
//                     {card?.details?.map((item, itemIndex) => (
//                       <>
//                         <li
//                           key={itemIndex}
//                           className="flex justify-between items-center p-1 sm:p-3 text-Red"
//                         >
//                           <span className="font-Ubuntu font-normal text-[15px] leading-6 title-child ">
//                             {item.label}
//                           </span>
//                           <div className="flex items-center space-x-2 title-child">
//                             <span>{item.icon}</span>
//                             <span className=" font-Ubuntu font-normal text-[15px] leading-3 sm:leading-6 title-child">
//                               {item.value}
//                             </span>
//                           </div>
//                         </li>
//                       </>
//                     ))}
//                   </ul>
//                 </div>
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//       <div className="sm:grid sm:grid-cols-4 lg:grid lg:grid-cols-3 mb-24">
//         <div className="col-span-4 sm:mb-5 md:col-span-2 lg:col-span-2 bg-white rounded-lg me-2 ">
//           <UserManageChart />
//         </div>
//         <div className="col-span-4 md:col-span-2 lg:col-span-1 rounded-lg sm:ms-3 mt-5 sm:mt-0">
//           {mostValuablePost?.length ? (
//             <Postslider
//               posts={mostValuablePost}
//               isMostValuable={true}
//               sectionTitle="Most Valuable Post"
//             />
//           ) : (
//             <div className="font-Ubuntu rounded-lg">
//               <p className="text-Red font-bold text-[20px] leading-tight  whitespace-nowrap bg-white p-3 py-4">
//                 {localesData?.MOST_VALUABLE_MESSAGE}
//               </p>
//               <div className="text-center">
//                 <img src={mostpostex} alt="No Posts" className="mx-auto mb-4" />
//                 <div className="pt-3">
//                   <p className="text-xl font-semibold mb-2">
//                     {localesData?.NO_POST_YET}
//                   </p>
//                   <p className="text-gray-500 text-[12px]">
//                     {localesData?.TO_BE_VALUABLE}
//                   </p>
//                 </div>
//               </div>
//             </div>
//           )}
//         </div>
//       </div>
//     </div>
//   );
// };

// export default Dashboard;
