import _ from "lodash";
import moment from "moment";
import axios from "axios";
import React, { useEffect, useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
} from "recharts";
import ChartOption from "./ChartOption";

const UserManageChart = () => {
  const [opacity, setOpacity] = useState({ user: 1 });
  const [chartData, setChartData] = useState([]);
  const [timePeriod, setTimePeriod] = useState("this_week");

  const CustomTick = ({ x, y, payload }) => {
    return (
      <text
        x={x}
        y={y}
        dy={16}
        textAnchor="middle"
        fill="#666"
        fontSize="12px"
        fontFamily="Ubuntu"
      >
        {payload.value}
      </text>
    );
  };

  const token =
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbl90eXBlIjoicmVmcmVzaCIsImV4cCI6MTgxMDI3MzgxMSwiaWF0IjoxNzIzODczODExLCJqdGkiOiIxMDM5ZDJkNTU2YzE0MGU0YWZmN2YzZjI2OGMzYmMwMyIsInVzZXJfaWQiOjJ9.RFfYbpTFQZGwSoPbDeIxsNm2tevm9xcodLJQe106ejQ";
  useEffect(() => {
    const fetchData = async () => {
      try {
        let fromDate, toDate;
        if (timePeriod?.fromDate && timePeriod?.toDate) {
          fromDate = moment(timePeriod.fromDate).format("YYYY-MM-DD");
          toDate = moment(timePeriod.toDate).format("YYYY-MM-DD");
        } else {
          switch (timePeriod) {
            case "this_week":
              fromDate = moment().startOf("week").format("YYYY-MM-DD");
              toDate = moment().format("YYYY-MM-DD");
              break;
            case "this_month":
              fromDate = moment().startOf("month").format("YYYY-MM-DD");
              toDate = moment().format("YYYY-MM-DD");
              break;
            case "this_year":
              fromDate = moment().startOf("year").format("YYYY-MM-DD");
              toDate = moment().format("YYYY-MM-DD");
              break;
            default:
              fromDate = moment().startOf("week").format("YYYY-MM-DD");
              toDate = moment().format("YYYY-MM-DD");
          }
        }

        const response = await axios.get(
          `https://dev.yooii.com/api/graph-admin/?from_date=${fromDate}&to_date=${toDate}`,
          {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          }
        );

        const filteredData = response.data.data.filter((item) => {
          const itemDate = moment(item.date, "DD-MM-YYYY");
          return itemDate.isBetween(fromDate, toDate, null, "[]");
        });

        const transformedData = filteredData.map((item) => {
          const dateMoment = moment(item.date, "DD-MM-YYYY");
          let dateFormatted;
          if (timePeriod === "this_week") {
            dateFormatted = dateMoment.format("dddd"); // Day of the week
          } else if (timePeriod === "this_month") {
            dateFormatted = dateMoment.format("DD-MMM"); // Day of the month
          } else if (timePeriod === "this_year") {
            dateFormatted = dateMoment.format("MMMM YYYY"); // Month and year
          } else {
            dateFormatted = dateMoment.format("DD-MM-YYYY");
          }

          return {
            date: dateFormatted,
            users: item.count,
          };
        });

        let result = [];
        if (timePeriod === "this_year") {
          const groupedByMonthYear = _.groupBy(transformedData, "date");
          result = _.map(groupedByMonthYear, (items, monthYear) => {
            const totalUsers = _.sumBy(items, "users");
            return {
              date: monthYear,
              users: totalUsers,
            };
          });
        } else {
          const groupedByDate = _.groupBy(transformedData, "date");
          result = _.map(groupedByDate, (items, date) => {
            const totalUsers = _.sumBy(items, "users");
            return {
              date,
              users: totalUsers,
            };
          });
        }

        setChartData(result);
      } catch (error) {
        console.error("Error fetching chart data:", error);
      }
    };

    fetchData();
  }, [timePeriod]);

  const handleMouseEnter = (o) => {
    const { dataKey } = o;
    setOpacity((op) => ({ ...op, [dataKey]: 0.5 }));
  };

  const handleMouseLeave = (o) => {
    const { dataKey } = o;
    setOpacity((op) => ({ ...op, [dataKey]: 1 }));
  };

  const handleTimePeriodChange = (period, range = {}) => {
    if (period === "custom_range") {
      setTimePeriod({ ...range, type: "custom_range" });
    } else {
      setTimePeriod(period);
    }
  };

  const renderLegend = (props) => {
    const { payload } = props;
    return (
      <div className="flex">
        {payload.map((entry, index) =>
          entry.value !== "user" ? (
            <div key={`item-${index}`} className="flex items-center me-4">
              <p
                className="h-2 w-2 rounded-full"
                style={{ backgroundColor: entry.color }}
              ></p>
              <p className="text-sm text-gray-500 ms-1">{entry.value}</p>
            </div>
          ) : null
        )}
      </div>
    );
  };

  const getTimePeriodLabel = () => {
    switch (timePeriod) {
      case "this_week":
        return "This Week Activity";
      case "this_month":
        return "This Month Activity";
      case "this_year":
        return "This Year Activity";
      case "custom_range":
        return `Activity from ${moment(timePeriod.fromDate).format(
          "DD-MM-YYYY"
        )} to ${moment(timePeriod.toDate).format("DD-MM-YYYY")}`;
      default:
        return "Custome Range Activity";
    }
  };

  return (
    <>
      <div className="mx-auto p-4 w-full pb-9 font-Ubuntu">
        <div>
          <div className="flex justify-between items-center">
            <p className="2xl:text-[20px] xl:text-[18px] text-[17px] text-Red font-semibold">
              User Manage
            </p>
            <ChartOption onSelect={handleTimePeriodChange} />
          </div>
          <div className="flex gap-4 justify-end mr-16 sm:mr-24 mb-2 pt-2 sm:mb-6">
            <div className="flex items-center gap-2">
              <p className="h-[7px] w-[7px] rounded-full bg-lightyellow"></p>
              <p className="admin-label text-[12px] text-gray-400 whitespace-nowrap">
                {getTimePeriodLabel()}
              </p>
            </div>
          </div>
        </div>
        <ResponsiveContainer
          width="100%"
          height={380}
          className="sm:pe-4 admin-dashboard"
        >
          <LineChart
            data={chartData}
            margin={{ top: 10, right: 0, left: -30, bottom: 0 }}
          >
            <CartesianGrid
              vertical={false}
              strokeDasharray="0 0"
              horizontal={true}
              stroke="#ccc"
            />
            <XAxis
              dataKey="date"
              axisLine={false}
              tickLine={false}
              tick={<CustomTick />}
            />

            <YAxis axisLine={false} tickLine={false} />
            <Tooltip />
            {/* <Legend
              content={renderLegend}
              onMouseEnter={handleMouseEnter}
              onMouseLeave={handleMouseLeave}
            /> */}
            <Line
              type="monotone"
              dataKey="users"
              stroke="#674941"
              strokeWidth={2}
              opacity={0.3}
              dot={false}
              className="blue-drop-shadow"
            />
          </LineChart>
        </ResponsiveContainer>
      </div>
    </>
  );
};

export default UserManageChart;
