import React, { useContext, useEffect, useRef, useState } from "react";
import { Menu } from "@headlessui/react";
import { BsThreeDotsVertical } from "react-icons/bs";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { IntlContext } from "../../../../App";

export default function ChartOption({ onSelect }) {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const calendarRef = useRef(null);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const handleCalendarClick = () => {
    setIsCalendarOpen(!isCalendarOpen);
  };

  const handleDateChange = (dates) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
    if (start && end) {
      setIsCalendarOpen(false);
      onSelect("custom_range", { fromDate: start, toDate: end });
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        setIsCalendarOpen(false);
      }
    };

    if (isCalendarOpen) {
      window.addEventListener("mousedown", handleClickOutside);
    } else {
      window.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      window.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCalendarOpen]);

  return (
    <div className="relative inline-block text-left">
      <Menu as="div">
        <div>
          <Menu.Button className="flex items-center justify-center h-10 w-10 rounded-md text-Red hover:bg-[#EFEBE9]  cursor-pointer z-40 relative">
            <BsThreeDotsVertical style={{ height: "20px", width: "20px" }} />
          </Menu.Button>
        </div>
        <Menu.Items className="absolute right-0 z-40 mt-2 w-28 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 transition focus:outline-none">
          <div className="py-1">
            <Menu.Item>
              <a
                href="#"
                onClick={() => onSelect("this_week")}
                className="block px-4 py-2 text-sm text-Red hover:bg-gray-100 whitespace-nowrap "
              >
                {localesData?.USER_WEB?.USER_PROFILE?.THIS_WEEK}
              </a>
            </Menu.Item>
            <Menu.Item>
              <a
                href="#"
                onClick={() => onSelect("this_month")}
                className="block px-4 py-2 text-sm text-Red hover:bg-gray-100 whitespace-nowrap"
              >
                {localesData?.USER_WEB?.USER_PROFILE?.THIS_MONTH}
              </a>
            </Menu.Item>
            <Menu.Item>
              <a
                href="#"
                onClick={() => onSelect("this_year")}
                className="block px-4 py-2 text-sm text-Red hover:bg-gray-100 whitespace-nowrap"
              >
                {localesData?.USER_WEB?.USER_PROFILE?.THIS_YEAR}
              </a>
            </Menu.Item>
            <Menu.Item>
              <a
                href="#"
                onClick={handleCalendarClick}
                className="block px-4 py-2 text-sm text-Red hover:bg-gray-100 whitespace-nowrap"
              >
                {localesData?.USER_WEB?.USER_PROFILE?.CELENDER}
              </a>
            </Menu.Item>
          </div>
        </Menu.Items>
      </Menu>
      {isCalendarOpen && (
        <div className="absolute right-0 z-30 mt-2" ref={calendarRef}>
          <DatePicker
            selected={startDate}
            onChange={handleDateChange}
            startDate={startDate}
            endDate={endDate}
            selectsRange
            inline
            maxDate={new Date()}
          />
        </div>
      )}
    </div>
  );
}
