// import React, { useState, useEffect } from "react";
// import {
//   Search,
//   Edit,
//   Trash2,
//   MoreVertical,
//   ChevronDown,
//   Plus,
// } from "lucide-react";

// const UserManage = () => {
//   const [activeTab, setActiveTab] = useState("Invitee");
//   const [users, setUsers] = useState([]);
//   const [isLoading, setIsLoading] = useState(true);
//   const [searchQuery, setSearchQuery] = useState("");
//   const [selectedBrand, setSelectedBrand] = useState("Any brand");
//   const [dropdownOpen, setDropdownOpen] = useState(null);
  

//   // Fetch users from API
//   useEffect(() => {
//     const fetchUsers = async () => {
//       try {
//         setIsLoading(true);
//         // Replace with your actual API endpoint
//         const response = await fetch("https://api.example.com/users");
//         const data = await response.json();
//         setUsers(data || sampleUsers);
//       } catch (error) {
//         console.error("Error fetching users:", error);
//         setUsers(sampleUsers); // Fallback to sample data
//       } finally {
//         setIsLoading(false);
//       }
//     };

//     fetchUsers();
//   }, []);

//   // Sample user data for demonstration
//   const sampleUsers = [
//     { id: 1, email: "<EMAIL>", brands: ["F", "F", "F"] },
//     { id: 2, email: "<EMAIL>", brands: ["F", "F", "F"] },
//     { id: 3, email: "<EMAIL>", brands: ["F", "F", "F"] },
//     { id: 4, email: "<EMAIL>", brands: ["F", "F", "F"] },
//     { id: 5, email: "<EMAIL>", brands: ["F", "F", "F"] },
//   ];

//   const toggleDropdown = (userId) => {
//     setDropdownOpen(dropdownOpen === userId ? null : userId);
//   };

//   const handleEdit = (userId) => {
//     console.log(`Edit user ${userId}`);
//     setDropdownOpen(null);
//     // Add your edit logic here
//   };

//   const handleDelete = (userId) => {
//     console.log(`Delete user ${userId}`);
//     setDropdownOpen(null);
//     // Add your delete logic here
//   };

//   const filteredUsers = users.filter((user) =>
//     user.email.toLowerCase().includes(searchQuery.toLowerCase())
//   );

//   return (
//     <div className="max-w-6xl mx-auto p-4 bg-white rounded-lg shadow-sm">
//       <h1 className="text-2xl font-bold text-gray-800 mb-6">User Management</h1>

//       {/* Tabs */}
//       <div className="border-b border-gray-200 mb-6">
//         <div className="flex">
//           <button
//             className={`py-3 px-6 font-medium ${
//               activeTab === "Invitee"
//                 ? "text-gray-800 border-b-2 border-gray-800"
//                 : "text-gray-500"
//             }`}
//             onClick={() => setActiveTab("Invitee")}
//           >
//             Invitee
//           </button>
//           <button
//             className={`py-3 px-6 font-medium ${
//               activeTab === "Invited"
//                 ? "text-gray-800 border-b-2 border-gray-800"
//                 : "text-gray-500"
//             }`}
//             onClick={() => setActiveTab("Invited")}
//           >
//             Invited
//           </button>
//         </div>
//       </div>

//       {/* Search and Filters */}
//       <div className="flex flex-col md:flex-row gap-4 mb-8">
//         <div className="relative flex-grow">
//           <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
//             <Search className="h-5 w-5 text-gray-400" />
//           </div>
//           <input
//             type="text"
//             placeholder="Search"
//             className="w-full pl-10 pr-4 py-2 bg-gray-50 border border-gray-200 rounded-md"
//             value={searchQuery}
//             onChange={(e) => setSearchQuery(e.target.value)}
//           />
//         </div>

//         <div className="relative">
//           <button
//             className="w-full md:w-64 px-4 py-2 bg-white border border-gray-200 rounded-md flex items-center justify-between"
//             onClick={() => setDropdownOpen("brand")}
//           >
//             <span>{selectedBrand}</span>
//             <ChevronDown className="h-5 w-5 text-gray-400" />
//           </button>
//           {dropdownOpen === "brand" && (
//             <div className="absolute z-10 mt-1 w-full bg-white border border-gray-200 rounded-md shadow-lg">
//               <div className="py-1">
//                 {["Any brand", "Brand 1", "Brand 2", "Brand 3"].map((brand) => (
//                   <button
//                     key={brand}
//                     className="block w-full text-left px-4 py-2 hover:bg-gray-100"
//                     onClick={() => {
//                       setSelectedBrand(brand);
//                       setDropdownOpen(null);
//                     }}
//                   >
//                     {brand}
//                   </button>
//                 ))}
//               </div>
//             </div>
//           )}
//         </div>

//         <button className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-md">
//           <Plus className="h-5 w-5" />
//           <span>Add User</span>
//         </button>

//         <button className="px-6 py-2 bg-gray-700 hover:bg-gray-800 text-white rounded-md">
//           User Role
//         </button>
//       </div>

//       {/* Users and Brands Headers */}
//       <div className="flex justify-between mb-4">
//         <h2 className="text-lg font-medium text-gray-800">Users</h2>
//         <h2 className="text-lg font-medium text-gray-800">Brands</h2>
//       </div>

//       {/* Users List */}
//       <div className="space-y-4">
//         {isLoading ? (
//           <div className="text-center py-8">Loading users...</div>
//         ) : filteredUsers.length === 0 ? (
//           <div className="text-center py-8">No users found</div>
//         ) : (
//           filteredUsers.map((user) => (
//             <div
//               key={user.id}
//               className="flex items-center justify-between p-4 border border-gray-200 rounded-lg"
//             >
//               <div className="flex items-center gap-4">
//                 <div className="w-12 h-12 rounded-full bg-gray-200 overflow-hidden">
//                   <img
//                     src={`https://api.dicebear.com/6.x/notionists/svg?seed=${user.id}`}
//                     alt="Avatar"
//                     className="w-full h-full object-cover"
//                   />
//                 </div>
//                 <span className="text-gray-800">{user.email}</span>
//               </div>

//               <div className="flex items-center">
//                 <div className="flex items-center gap-2 mr-4">
//                   {user.brands.map((brand, idx) => (
//                     <span key={idx} className="font-semibold text-gray-700">
//                       {brand}
//                     </span>
//                   ))}
//                 </div>

//                 <div className="relative">
//                   <button
//                     className="p-1 hover:bg-gray-100 rounded-full"
//                     onClick={() => toggleDropdown(user.id)}
//                   >
//                     <MoreVertical className="h-6 w-6 text-gray-500" />
//                   </button>

//                   {dropdownOpen === user.id && (
//                     <div className="absolute right-0 z-10 mt-1 w-36 bg-white border border-gray-200 rounded-md shadow-lg">
//                       <div className="py-1">
//                         <button
//                           className="flex items-center gap-2 w-full text-left px-4 py-2 hover:bg-gray-100"
//                           onClick={() => handleEdit(user.id)}
//                         >
//                           <Edit className="h-4 w-4" />
//                           <span>Edit</span>
//                         </button>
//                         <button
//                           className="flex items-center gap-2 w-full text-left px-4 py-2 hover:bg-gray-100 text-red-600"
//                           onClick={() => handleDelete(user.id)}
//                         >
//                           <Trash2 className="h-4 w-4" />
//                           <span>Delete</span>
//                         </button>
//                       </div>
//                     </div>
//                   )}
//                 </div>
//               </div>
//             </div>
//           ))
//         )}
//       </div>
//     </div>
//   );
// };

// export default UserManage;
