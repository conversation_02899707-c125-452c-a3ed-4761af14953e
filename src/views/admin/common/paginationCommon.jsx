import React from "react";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";

const Pagination = ({
  totalPages,
  currentPage,
  handlePrevious,
  handleNext,
  handlePageClick,
  isLoading,
}) => {
  if (!totalPages) return null;

  const createPageNumbers = () => {
    const pages = [];
    const showDotsThreshold = 3;

    if (totalPages <= 5) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= showDotsThreshold) {
        for (let i = 1; i <= showDotsThreshold + 1; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      } else if (currentPage > totalPages - showDotsThreshold) {
        pages.push(1);
        pages.push("...");
        for (let i = totalPages - showDotsThreshold; i <= totalPages; i++) {
          pages.push(i);
        }
      } else {
        pages.push(1);
        pages.push("...");
        for (let i = currentPage - 1; i <= currentPage + 1; i++) {
          pages.push(i);
        }
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  };

  const pageNumbers = createPageNumbers();

  return (
    <nav aria-label="Page navigation example flex">
      <ul className="flex items-center -space-x-px h-8 text-sm justify-end my-5 me-2">
        <li>
          <button
            onClick={handlePrevious}
            className="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-Red"
            disabled={currentPage === 1 || isLoading}
          >
            <span className="sr-only">Previous</span>
            <FaChevronLeft className="w-2.5 h-2.5 rtl:rotate-180" />
          </button>
        </li>
        {pageNumbers.map((page, index) => (
          <li key={index}>
            {page === "..." ? (
              <span className="flex items-center justify-center px-3 h-8 leading-tight text-Red">
                ...
              </span>
            ) : (
              <button
                onClick={() => handlePageClick(page)}
                disabled={isLoading}
                className={`flex items-center justify-center px-3 h-8 leading-tight mx-1 ${
                  currentPage === page
                    ? "z-10 text-white bg-Red font-Ubuntu rounded-lg text-md font-medium dark:border-gray-700 dark:bg-gray-700 dark:text-white"
                    : "text-Red border-gray-300 font-medium text-md bg-profileCardBG rounded-lg font-Ubuntu"
                }`}
              >
                {page}
              </button>
            )}
          </li>
        ))}
        <li>
          <button
            onClick={handleNext}
            className="flex items-center justify-center px-3 h-8 ms-0 leading-tight text-Red"
            disabled={currentPage === totalPages || isLoading}
          >
            <span className="sr-only">Next</span>
            <FaChevronRight className="w-2.5 h-2.5 rtl:rotate-180" />
          </button>
        </li>
      </ul>
    </nav>
  );
};

export default Pagination;
