import { Dialog } from "@mui/material";
import React, { useContext, useState } from "react";
import { useNavigate } from "react-router-dom";
import { IntlContext } from "../../../App";
import siteConstant from "../../../helpers/constant/siteConstant";
import { saveToStorage } from "../../../helpers/context/storage";

const DialogueModel = ({ open, handleDialogClose }) => {
  const navigate = useNavigate();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const handleClick = (type) => {
    if (type === localesData?.ADMIN_TAB) {
      saveToStorage(
        siteConstant?.INDENTIFIERS.currentTab,
        localesData?.ADMIN_TAB
      );
      navigate("/admin/user-management");
    } else {
      saveToStorage(
        siteConstant?.INDENTIFIERS.currentTab,
        localesData?.USER_TAB
      );
      navigate("/dashboard");
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleDialogClose}
        className="bg-transparent font-Ubuntu"
      >
        <div class="bg-white p-7 rounded-xl shadow-lg w-full max-w-md">
          <div class="flex flex-col gap-3 items-center">
            <div class="mb-4">
              <img
                src={intlContext?.socialIcons?.FLOWKARLOGO}
                alt="Flowkar Logo"
                className="w-28 "
              />
            </div>
            <p class="text-[15px] mb-4 text-center">
              Welcome to Flowkar! Choose your experience
            </p>
            <div class="flex space-x-8 md:space-x-12">
              <button
                className="text-Red py-1.5 w-28 whitespace-nowrap sm:py-3 sm:w-36 text-[13.5px] sm:text-[15px] rounded-xl hover:bg-[#efebe9]  font-semibold border border-pink-100"
                onClick={() => handleClick(localesData?.ADMIN_TAB)}
              >
                Admin Web
              </button>
              <button
                className="bg-Red text-white py-1.5 w-28 whitespace-nowrap sm:py-3 sm:w-36 text-[13.5px] sm:text-[15px] rounded-xl  font-semibold"
                onClick={() => handleClick(localesData?.USER_TAB)}
              >
                User Web
              </button>
            </div>
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default DialogueModel;
