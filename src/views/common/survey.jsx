import React, { useState } from "react";
import apiInstance from "../../helpers/Axios/axiosINstance";
import siteConstant from "../../helpers/constant/siteConstant";
import { URL } from "../../helpers/constant/Url";
import { fetchFromStorage } from "../../helpers/context/storage";
import survey from "../../assets/images/survey.svg";
import { Dialog } from "@mui/material";

const SurverPopup = ({ issurvey, setIssurvey }) => {
  const [formData, setFormData] = useState({
    name: "",
    is_opt_in: false,
    is_app_used: "Yes",
    app_name: "",
    app_description: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState("");

  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleRadioChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const payload = {
        name: formData.name,
        is_opt_in: !!formData.is_opt_in,
        is_app_used: formData.is_app_used === "Yes",
        app_name: formData.app_name,
        app_description: formData.app_description,
      };

      const response = await apiInstance?.post(URL?.SURVEY, payload, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.data) {
        setSubmitMessage("success", response?.data?.message);
        setIssurvey(false);
        setFormData({
          name: "",
          is_opt_in: false,
          is_app_used: "Yes",
          app_name: "",
          app_description: "",
        });
      }
    } catch (error) {
      console.error(
        "Error submitting survey:",
        error?.response?.data || error.message
      );
      setSubmitMessage("Something went wrong. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={issurvey}
      onClose={() => setIssurvey(false)}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        style: {
          borderRadius: "16px",
          padding: 0,
          background: "transparent",
          boxShadow: "none",
        },
        elevation: 0,
      }}
    >
      <div className="relative font-Ubuntu bg-white rounded-2xl shadow-lg p-8">
        <button
          onClick={() => setIssurvey(false)}
          className="absolute top-5 right-5 text-gray-400 hover:text-gray-600 text-2xl font-bold focus:outline-none"
          aria-label="Close"
          type="button"
        >
          &times;
        </button>
        <div className="flex flex-col items-center mb-6">
          <div className="p-3 rounded-full mb-2">
            <img src={survey} alt="Icon" className="h-8 w-8" />
          </div>

          <h2 className="text-xl font-semibold text-Red">Survey</h2>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="mb-4">
            <label
              htmlFor="name"
              className="block text-sm font-medium text-Red mb-2"
            >
              Your Name
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-[12px] focus:ring-2 focus:ring-Red focus:border-none"
              required
            />
          </div>

          <div className="mb-4">
            <p className="block text-sm font-medium text-Red mb-2">
              Are you using similar app?
            </p>
            <div className="flex flex-col gap-4">
              <label className="flex items-center w-20">
                <input
                  type="radio"
                  name="is_app_used"
                  value="Yes"
                  checked={formData.is_app_used === "Yes"}
                  onChange={handleRadioChange}
                  className="h-4 w-4 text-Red accent-Red focus:ring-2 focus:ring-Red focus:border-none"
                />
                <span className="ml-2 text-sm text-Red">Yes</span>
              </label>
              <label className="flex items-center w-20">
                <input
                  type="radio"
                  name="is_app_used"
                  value="No"
                  checked={formData.is_app_used === "No"}
                  onChange={handleRadioChange}
                  className="h-4 w-4 text-Red accent-Red focus:ring-2 focus:ring-Red focus:border-none"
                />
                <span className="ml-2 text-sm text-Red">No</span>
              </label>
            </div>
          </div>

          {formData.is_app_used === "Yes" && (
            <>
              <div className="mb-4">
                <label
                  htmlFor="app_name"
                  className="block text-sm font-medium text-Red mb-2"
                >
                  App Name
                </label>
                <input
                  type="text"
                  id="app_name"
                  name="app_name"
                  value={formData.app_name}
                  onChange={handleChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-[12px] focus:ring-2 focus:ring-Red focus:border-none"
                />
              </div>

              <div className="mb-6">
                <label
                  htmlFor="app_description"
                  className="block text-sm font-medium text-Red mb-2"
                >
                  Tell us something about the app
                </label>
                <textarea
                  id="app_description"
                  name="app_description"
                  value={formData.app_description}
                  onChange={handleChange}
                  rows="4"
                  className="w-full px-3 py-2 border border-gray-300 rounded-[12px] focus:ring-2 focus:ring-Red focus:border-none"
                ></textarea>
              </div>
            </>
          )}

          {submitMessage && (
            <div className="mb-4 text-center text-sm font-medium text-gray-700">
              {submitMessage}
            </div>
          )}
          <div className="flex justify-center">
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full md:w-1/2 p-3 text-lg font-bold text-white bg-Red rounded-lg transition"
            >
              {isSubmitting ? "Submitting..." : "Submit"}
            </button>
          </div>
        </form>
      </div>
    </Dialog>
  );
};

export default SurverPopup;
