import React, { useContext, useEffect, useState } from "react";
import { IntlContext } from "../../App";
import SubscriptionIcon from "../../assets/images/svg_icon/subscription-icon.svg";
import apiInstance from "../../helpers/Axios/axiosINstance";
import { URL } from "../../helpers/constant/Url";

function SubscriptionPage({ isOpen, setIsOpen }) {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const handleSubscription = async () => {
    try {
      const payload = JSON.stringify({ is_opt_in: true });

      const response = await apiInstance.post(
        URL.UPDATE_SUBSCRIPTION,
        payload,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (response.data.is_opt_in) {
        setIsOpen(false);
      }
    } catch (error) {
      console.error(
        "Error updating subscription:",
        error?.response?.data || error.message
      );
    }
  };

  return (
    <>
      {isOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[9999999] font-Ubuntu">
          <div className="bg-white p-6 md:p-8 rounded-lg shadow-lg max-w-md w-full text-center modal-slide-in">
            <div className="flex justify-center mb-4">
              <img src={SubscriptionIcon} alt="Subscription Icon" />
            </div>
            <h1 className="text-xl md:text-2xl font-bold text-[#563D39] mb-4">
              {localesData?.USER_WEB?.SUBSCRIPTION}
            </h1>
            <p className="text-sm md:text-md text-gray-600 mb-6">
              {localesData?.USER_WEB?.SUBSCRIPTION_CONTENT}
            </p>
            <div className="flex flex-col md:flex-row gap-4">
              <button
                onClick={() => setIsOpen(false)}
                className="w-full md:w-1/2 p-3 text-lg font-bold border-2 border-gray-300 text-[#563D39] bg-white rounded-lg hover:bg-gray-100 transition"
              >
                {localesData?.USER_WEB?.NO}
              </button>
              <button
                onClick={handleSubscription}
                className="w-full md:w-1/2 p-3 text-lg font-bold text-white bg-[#563D39] rounded-lg  transition"
              >
                {localesData?.USER_WEB?.YES}
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}

export default SubscriptionPage;
