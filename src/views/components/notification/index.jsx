import React from "react";
import { X } from "lucide-react";

const Notifypage = ({ handleNotifyClose }) => {
  const notifications = [
    {
      id: 1,
      user: "<PERSON>",
      action: "Liked your Post",
      time: "3 hours ago",
      avatar: "VA",
      avatarColor: "bg-red-500",
    },
    {
      id: 2,
      user: "<PERSON>",
      action: "Comment On Your Post",
      time: "3 hours ago",
      avatar: "https://picsum.photos/id/237/200/300",
      avatarColor: "bg-gray-300",
    },
    {
      id: 3,
      user: "<PERSON>",
      action: "Started Following You",
      time: "3 hours ago",
      avatar: "https://picsum.photos/id/237/200/300",
      avatarColor: "bg-gray-300",
    },
    {
      id: 4,
      user: "<PERSON>",
      action: "Comment On Your Post",
      time: "3 hours ago",
      avatar: "https://picsum.photos/id/237/200/300",
      avatarColor: "bg-gray-300",
    },
    {
      id: 5,
      user: "<PERSON>",
      action: "Comment On Your Post",
      time: "3 hours ago",
      avatar:
        "https://fastly.picsum.photos/id/866/200/300.jpg?hmac=rcadCENKh4rD6MAp6V_ma-AyWv641M4iiOpe1RyFHeI",
      avatarColor: "bg-gray-300",
    },
  ];

  return (
    <div className=" overflow-hidden font-Ubuntu bg-[#FFFFFF]">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <h2 className="text-[18px] font-semibold text-Red">Notifications</h2>
        <button className="text-Red " onClick={() => handleNotifyClose()}>
          <X size={20} />
        </button>
      </div>

      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className="flex items-center p-4 hover:bg-gray-50 border-b border-gray-100"
          >
            <div className="flex-shrink-0 mr-3">
              {notification.avatar.startsWith("/") ? (
                <img
                  src={notification.avatar}
                  alt={notification.user}
                  className="w-8 h-8 rounded-full"
                />
              ) : (
                <div
                  className={`w-8 h-8 rounded-full ${notification.avatarColor} flex items-center justify-center`}
                >
                  <span className="text-white text-sm font-medium">
                    {notification.avatar}
                  </span>
                </div>
              )}
            </div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-1">
                <span className="font-medium text-gray-900 text-sm">
                  {notification.user}
                </span>
                <span className="text-gray-600 text-sm">
                  {notification.action}
                </span>
              </div>
              <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
            </div>
          </div>
        ))}

        {/* Invitation Card */}
        <div className="p-4 border-gray-100">
          <div className="flex items-start space-x-3">
            <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-white text-sm font-medium">PA</span>
            </div>
            <div className="flex-1 min-w-0">
              <div className="text-sm">
                <span className="font-medium text-gray-900">Pam Asonas</span>
                <span className="text-gray-600">
                  {" "}
                  has invited you to manage{" "}
                </span>
                <span className="font-medium text-gray-900">pam Asonas</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">3 hours ago</p>
              <div className="flex space-x-2 mt-3 justify-center">
                <button className="px-10 py-2 rounded-[8px] text-sm text-textRedcolor bg-gray-200  transition-colors">
                  Decline
                </button>
                <button className="px-10 py-2 rounded-[8px] text-sm text-white bg-Red  transition-colors">
                  Accept
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="p-2 border border-gray-200 mx-10 rounded-[8px]">
        <button className="w-full text-center text-[15px] text-Red  font-medium">
          View All Notification
        </button>
      </div>
    </div>
  );
};

export default Notifypage;
