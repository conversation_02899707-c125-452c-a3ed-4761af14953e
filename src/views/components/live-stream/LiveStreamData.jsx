import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import { liveSocket } from "../../../helpers/context/socket";

const LiveStreamData = () => {
  const { streamId } = useParams();
  const socket = liveSocket;
  
  const [streamData, setStreamData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [viewerCount, setViewerCount] = useState(0);
  const [comments, setComments] = useState([]);
  const [isConnected, setIsConnected] = useState(false);

  // Get user data from localStorage
  const getUserData = () => {
    try {
      const userData = JSON.parse(localStorage.getItem("userData"));
      const userId = localStorage.getItem("UserId");
      const username = localStorage.getItem("username") || localStorage.getItem("name") || localStorage.getItem("userName");
      
      return {
        userId: userId,
        username: username,
        userData: userData,
        profileImage: userData?.profile_image || userData?.profileImage || null,
        email: userData?.email || null,
        phone: userData?.phone || null,
        bio: userData?.bio || userData?.description || null,
        followers: userData?.followers || 0,
        following: userData?.following || 0,
        posts: userData?.posts || 0,
        verified: userData?.verified || false,
        createdAt: userData?.created_at || userData?.createdAt || null,
        lastActive: userData?.last_active || userData?.lastActive || new Date().toISOString(),
      };
    } catch (error) {
      console.error("Error parsing userData:", error);
      return {
        userId: localStorage.getItem("UserId"),
        username: localStorage.getItem("username") || localStorage.getItem("name") || localStorage.getItem("userName") || "Unknown",
        userData: null,
        profileImage: null,
        email: null,
        phone: null,
        bio: null,
        followers: 0,
        following: 0,
        posts: 0,
        verified: false,
        createdAt: null,
        lastActive: new Date().toISOString(),
      };
    }
  };

  // Get stream metadata
  const getStreamMetadata = () => {
    const userData = getUserData();
    const currentTime = new Date().toISOString();
    
    return {
      streamId: streamId,
      roomId: streamId,
      broadcaster: {
        userId: userData.userId,
        username: userData.username,
        displayName: userData.username,
        profileImage: userData.profileImage ? `${URL.SOCKET_URL}${userData.profileImage}` : null,
        email: userData.email,
        phone: userData.phone,
        bio: userData.bio,
        followers: userData.followers,
        following: userData.following,
        posts: userData.posts,
        verified: userData.verified,
        createdAt: userData.createdAt,
        lastActive: userData.lastActive,
      },
      stream: {
        status: "live",
        startedAt: currentTime,
        duration: 0, // Will be calculated
        viewerCount: viewerCount,
        peakViewers: viewerCount,
        totalViews: viewerCount,
        comments: comments.length,
        hearts: 0,
        shares: 0,
        platform: "web",
        quality: "720p",
        bitrate: "2500kbps",
        fps: 30,
        resolution: "1280x720",
        audioCodec: "AAC",
        videoCodec: "H.264",
        serverRegion: "auto",
        streamKey: `${streamId}_${Date.now()}`,
        rtmpUrl: `${URL.SOCKET_URL.replace('https://', 'rtmp://')}/live`,
        hlsUrl: `${URL.SOCKET_URL}/live/${streamId}/index.m3u8`,
        webrtcUrl: `${window.location.origin}/live/${streamId}`,
        embedUrl: `${window.location.origin}/live/${streamId}`,
        shareUrl: `${window.location.origin}/live/${streamId}`,
        apiUrl: `${window.location.origin}/stream-data/${streamId}`,
      },
      platform: {
        name: "Flowkar",
        version: "1.0.0",
        domain: window.location.origin,
        apiEndpoint: URL.SOCKET_URL,
        socketEndpoint: URL.SOCKET_URL,
        webRTCEnabled: true,
        hlsEnabled: true,
        rtmpEnabled: true,
        features: [
          "live-streaming",
          "real-time-comments",
          "viewer-count",
          "screen-sharing",
          "multi-platform",
          "mobile-responsive",
          "chat-moderation",
          "stream-recording",
          "analytics",
          "social-sharing"
        ],
        supportedPlatforms: [
          "facebook",
          "instagram",
          "youtube",
          "twitch",
          "tiktok",
          "twitter",
          "linkedin",
          "discord",
          "telegram",
          "whatsapp"
        ],
        integrations: {
          facebook: {
            enabled: true,
            apiVersion: "v18.0",
            permissions: ["pages_manage_posts", "pages_read_engagement"],
            webhookUrl: `${URL.SOCKET_URL}/webhooks/facebook`,
          },
          instagram: {
            enabled: true,
            apiVersion: "v18.0",
            permissions: ["instagram_basic", "instagram_content_publish"],
            webhookUrl: `${URL.SOCKET_URL}/webhooks/instagram`,
          },
          youtube: {
            enabled: true,
            apiVersion: "v3",
            scopes: ["https://www.googleapis.com/auth/youtube.force-ssl"],
            webhookUrl: `${URL.SOCKET_URL}/webhooks/youtube`,
          },
          twitch: {
            enabled: true,
            apiVersion: "helix",
            scopes: ["channel:read:stream_key", "channel:manage:broadcast"],
            webhookUrl: `${URL.SOCKET_URL}/webhooks/twitch`,
          }
        }
      },
      analytics: {
        realTime: {
          viewers: viewerCount,
          comments: comments.length,
          engagement: comments.length > 0 ? (comments.length / Math.max(viewerCount, 1)) * 100 : 0,
          retention: 0,
          quality: "good",
          bitrate: "2500kbps",
          fps: 30,
          droppedFrames: 0,
          audioLevel: -20,
          videoLevel: 0.8,
        },
        historical: {
          totalStreams: 0,
          totalWatchTime: 0,
          averageViewers: 0,
          peakViewers: 0,
          totalComments: 0,
          totalHearts: 0,
          totalShares: 0,
        }
      },
      social: {
        hashtags: [],
        mentions: [],
        shares: 0,
        bookmarks: 0,
        reports: 0,
        moderation: {
          enabled: true,
          autoModeration: true,
          blockedWords: [],
          blockedUsers: [],
          moderationLevel: "medium",
        }
      },
      technical: {
        webrtc: {
          enabled: true,
          iceServers: [
            { urls: "stun:stun.l.google.com:19302" },
            { urls: "stun:stun1.l.google.com:19302" },
            { urls: "stun:stun2.l.google.com:19302" },
            { urls: "stun:stun3.l.google.com:19302" },
            { urls: "stun:stun4.l.google.com:19302" },
            {
              urls: "turn:openrelay.metered.ca:80",
              username: "openrelayproject",
              credential: "openrelayproject",
            },
            {
              urls: "turn:openrelay.metered.ca:443",
              username: "openrelayproject",
              credential: "openrelayproject",
            },
            {
              urls: "turn:openrelay.metered.ca:443?transport=tcp",
              username: "openrelayproject",
              credential: "openrelayproject",
            },
          ],
          iceCandidatePoolSize: 10,
        },
        media: {
          video: {
            codec: "H.264",
            profile: "baseline",
            level: "3.1",
            bitrate: "2500kbps",
            framerate: 30,
            resolution: "1280x720",
            keyframeInterval: 2,
            quality: "high",
          },
          audio: {
            codec: "AAC",
            sampleRate: 48000,
            channels: 2,
            bitrate: "128kbps",
            quality: "high",
          }
        },
        network: {
          protocol: "WebRTC",
          transport: "UDP/TCP",
          encryption: "DTLS-SRTP",
          bandwidth: "unlimited",
          latency: "< 100ms",
          jitter: "< 10ms",
          packetLoss: "< 1%",
        }
      },
      metadata: {
        title: `${getUserData().username}'s Live Stream`,
        description: "Live streaming on Flowkar",
        category: "general",
        tags: ["live", "streaming", "flowkar"],
        language: "en",
        ageRestriction: false,
        monetization: {
          enabled: false,
          type: "none",
          currency: "USD",
          amount: 0,
        },
        recording: {
          enabled: false,
          autoRecord: false,
          storage: "cloud",
          retention: "7 days",
        },
        privacy: {
          type: "public",
          password: null,
          whitelist: [],
          blacklist: [],
        },
        schedule: {
          scheduled: false,
          startTime: null,
          endTime: null,
          timezone: "UTC",
        }
      },
      timestamps: {
        createdAt: currentTime,
        startedAt: currentTime,
        lastUpdated: currentTime,
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 hours from now
      },
      webhooks: {
        streamStarted: `${URL.SOCKET_URL}/webhooks/stream-started`,
        streamEnded: `${URL.SOCKET_URL}/webhooks/stream-ended`,
        viewerJoined: `${URL.SOCKET_URL}/webhooks/viewer-joined`,
        viewerLeft: `${URL.SOCKET_URL}/webhooks/viewer-left`,
        commentReceived: `${URL.SOCKET_URL}/webhooks/comment-received`,
        streamError: `${URL.SOCKET_URL}/webhooks/stream-error`,
      },
      api: {
        endpoints: {
          streamInfo: `${URL.SOCKET_URL}/api/streams/${streamId}`,
          viewerList: `${URL.SOCKET_URL}/api/streams/${streamId}/viewers`,
          comments: `${URL.SOCKET_URL}/api/streams/${streamId}/comments`,
          analytics: `${URL.SOCKET_URL}/api/streams/${streamId}/analytics`,
          control: `${URL.SOCKET_URL}/api/streams/${streamId}/control`,
        },
        methods: {
          GET: ["streamInfo", "viewerList", "comments", "analytics"],
          POST: ["startStream", "endStream", "sendComment", "moderateComment"],
          PUT: ["updateStream", "updateSettings"],
          DELETE: ["deleteStream", "deleteComment"],
        },
        authentication: {
          type: "Bearer Token",
          header: "Authorization",
          format: "Bearer {token}",
          required: true,
        },
        rateLimiting: {
          requests: 100,
          window: "1 minute",
          burst: 10,
        }
      }
    };
  };

  // Fetch stream data
  const fetchStreamData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Get stream metadata
      const metadata = getStreamMetadata();
      setStreamData(metadata);

      // Check if stream is active
      if (socket && socket.connected) {
        socket.emit("check_room", { room: streamId });
        socket.emit("current_users_number", { room: streamId });
        socket.emit("get_comment_list", { room: streamId });
      }

      setIsLoading(false);
    } catch (error) {
      console.error("Error fetching stream data:", error);
      setError("Failed to fetch stream data");
      setIsLoading(false);
    }
  };

  // Socket event handlers
  useEffect(() => {
    if (!socket) {
      setError("Socket connection not available");
      return;
    }

    const handleConnect = () => {
      setIsConnected(true);
    };

    const handleDisconnect = () => {
      setIsConnected(false);
    };

    const handleCurrentUsersNumber = (data) => {
      if (data.room === streamId) {
        setViewerCount(data.count || 0);
      }
    };

    const handleCommentList = (data) => {
      if (data.room === streamId) {
        setComments(data.comments || []);
      }
    };

    const handleNewComment = (data) => {
      if (data.room === streamId) {
        setComments(prev => [...prev, data]);
      }
    };

    if (socket.connected) {
      setIsConnected(true);
    }

    socket.on("connect", handleConnect);
    socket.on("disconnect", handleDisconnect);
    socket.on("current_users_number", handleCurrentUsersNumber);
    socket.on("comment_list", handleCommentList);
    socket.on("new_comment", handleNewComment);

    return () => {
      socket.off("connect", handleConnect);
      socket.off("disconnect", handleDisconnect);
      socket.off("current_users_number", handleCurrentUsersNumber);
      socket.off("comment_list", handleCommentList);
      socket.off("new_comment", handleNewComment);
    };
  }, [socket, streamId]);

  // Fetch data on mount
  useEffect(() => {
    fetchStreamData();
  }, [streamId]);

  // Update stream data when viewer count or comments change
  useEffect(() => {
    if (streamData) {
      const updatedData = {
        ...streamData,
        stream: {
          ...streamData.stream,
          viewerCount: viewerCount,
          peakViewers: Math.max(streamData.stream.peakViewers, viewerCount),
          totalViews: viewerCount,
          comments: comments.length,
        },
        analytics: {
          ...streamData.analytics,
          realTime: {
            ...streamData.analytics.realTime,
            viewers: viewerCount,
            comments: comments.length,
            engagement: comments.length > 0 ? (comments.length / Math.max(viewerCount, 1)) * 100 : 0,
          }
        },
        timestamps: {
          ...streamData.timestamps,
          lastUpdated: new Date().toISOString(),
        }
      };
      setStreamData(updatedData);
    }
  }, [viewerCount, comments.length]);

  // Copy JSON data to clipboard
  const copyStreamData = () => {
    if (streamData) {
      const jsonData = JSON.stringify(streamData, null, 2);
      navigator.clipboard.writeText(jsonData).then(() => {
        alert("Stream data copied to clipboard!");
      }).catch(() => {
        alert("Failed to copy data");
      });
    }
  };

  // Download JSON file
  const downloadStreamData = () => {
    if (streamData) {
      const jsonData = JSON.stringify(streamData, null, 2);
      const blob = new Blob([jsonData], { type: "application/json" });
      const url = URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `stream-data-${streamId}-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F9F9F9] via-[#F3EDE7] to-[#E0E0E0] font-[Ubuntu,sans-serif] flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#563D39] mx-auto mb-4"></div>
          <p className="text-[#563D39]">Loading stream data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F9F9F9] via-[#F3EDE7] to-[#E0E0E0] font-[Ubuntu,sans-serif] flex items-center justify-center">
        <div className="text-center">
          <div className="text-2xl mb-4">⚠️</div>
          <p className="text-lg font-semibold text-[#563D39]">{error}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#F9F9F9] via-[#F3EDE7] to-[#E0E0E0] font-[Ubuntu,sans-serif] p-4">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <header className="bg-white rounded-2xl shadow-lg p-6 mb-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-[#563D39] mb-2">
                Live Stream Data API
              </h1>
              <p className="text-[#674941]">
                Comprehensive stream data for backend integration
              </p>
            </div>
            <div className="flex gap-3">
              <button
                onClick={copyStreamData}
                className="px-4 py-2 bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white rounded-lg font-bold shadow hover:scale-105 transition"
              >
                Copy JSON
              </button>
              <button
                onClick={downloadStreamData}
                className="px-4 py-2 bg-gradient-to-r from-[#674941] to-[#BC857D] text-white rounded-lg font-bold shadow hover:scale-105 transition"
              >
                Download
              </button>
            </div>
          </div>
          
          {/* Quick Info */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-[#F9F9F9] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-[#563D39]">{viewerCount}</div>
              <div className="text-sm text-[#674941]">Viewers</div>
            </div>
            <div className="bg-[#F9F9F9] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-[#563D39]">{comments.length}</div>
              <div className="text-sm text-[#674941]">Comments</div>
            </div>
            <div className="bg-[#F9F9F9] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-[#563D39]">{isConnected ? "🟢" : "🔴"}</div>
              <div className="text-sm text-[#674941]">Status</div>
            </div>
            <div className="bg-[#F9F9F9] rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-[#563D39]">{streamId}</div>
              <div className="text-sm text-[#674941]">Stream ID</div>
            </div>
          </div>
        </header>

        {/* Data Display */}
        <div className="bg-white rounded-2xl shadow-lg p-6">
          <h2 className="text-2xl font-bold text-[#563D39] mb-4">Stream Data (JSON)</h2>
          <div className="bg-[#F9F9F9] rounded-lg p-4 overflow-auto max-h-96">
            <pre className="text-sm text-[#563D39] whitespace-pre-wrap">
              {streamData ? JSON.stringify(streamData, null, 2) : "No data available"}
            </pre>
          </div>
        </div>

        {/* API Endpoints */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mt-6">
          <h2 className="text-2xl font-bold text-[#563D39] mb-4">API Endpoints</h2>
          <div className="space-y-3">
            {streamData?.api?.endpoints && Object.entries(streamData.api.endpoints).map(([key, url]) => (
              <div key={key} className="flex items-center justify-between bg-[#F9F9F9] rounded-lg p-3">
                <span className="font-semibold text-[#563D39]">{key}:</span>
                <code className="text-sm text-[#674941] bg-white px-2 py-1 rounded">{url}</code>
              </div>
            ))}
          </div>
        </div>

        {/* Platform Integration URLs */}
        <div className="bg-white rounded-2xl shadow-lg p-6 mt-6">
          <h2 className="text-2xl font-bold text-[#563D39] mb-4">Integration URLs</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-[#F9F9F9] rounded-lg p-4">
              <h3 className="font-bold text-[#563D39] mb-2">Stream URLs</h3>
              <div className="space-y-2 text-sm">
                <div><strong>WebRTC:</strong> <code className="bg-white px-2 py-1 rounded">{streamData?.stream?.webrtcUrl}</code></div>
                <div><strong>HLS:</strong> <code className="bg-white px-2 py-1 rounded">{streamData?.stream?.hlsUrl}</code></div>
                <div><strong>RTMP:</strong> <code className="bg-white px-2 py-1 rounded">{streamData?.stream?.rtmpUrl}</code></div>
                <div><strong>Embed:</strong> <code className="bg-white px-2 py-1 rounded">{streamData?.stream?.embedUrl}</code></div>
              </div>
            </div>
            <div className="bg-[#F9F9F9] rounded-lg p-4">
              <h3 className="font-bold text-[#563D39] mb-2">Webhook URLs</h3>
              <div className="space-y-2 text-sm">
                {streamData?.webhooks && Object.entries(streamData.webhooks).map(([key, url]) => (
                  <div key={key}>
                    <strong>{key}:</strong> <code className="bg-white px-2 py-1 rounded">{url}</code>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LiveStreamData; 