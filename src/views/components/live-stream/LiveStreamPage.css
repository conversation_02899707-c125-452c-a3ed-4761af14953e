.app-container {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }
  
  header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  h1 {
    margin: 0;
    color: #333;
  }
  
  .status {
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
  }
  
  .status.success {
    background-color: #d4edda;
    color: #155724;
  }
  
  .status.error {
    background-color: #f8d7da;
    color: #721c24;
  }
  
  .controls {
    margin-bottom: 20px;
  }
  
  .room-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
  
  input[type="text"] {
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 16px;
  }
  
  .button-group {
    display: flex;
    gap: 10px;
  }
  
  button {
    padding: 10px 20px;
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.2s;
  }
  
  button:hover {
    background-color: #0069d9;
  }
  
  button:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
  }
  
  .leave-button {
    background-color: #dc3545;
  }
  
  .leave-button:hover {
    background-color: #c82333;
  }
  
  .loading {
    margin-top: 10px;
    color: #666;
  }
  
  .video-containers {
    display: none;
    flex-direction: column;
    gap: 20px;
  }
  
  .video-containers.active {
    display: flex;
  }
  
  .video-container {
    position: relative;
  }
  
  .video-label {
    position: absolute;
    bottom: 10px;
    left: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
  }
  
  .local-video {
    width: 100%;
    max-height: 300px;
    background-color: #333;
    border-radius: 8px;
    object-fit: cover;
  }
  
  .local-video.host {
    max-height: 400px;
  }
  
  .remote-videos-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 20px;
  }
  
  .remote-video {
    width: 100%;
    height: 300px;
    background-color: #333;
    border-radius: 8px;
    object-fit: cover;
  }
  
  .streaming-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
  }
  
  .room-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  
  .room-label {
    font-weight: bold;
  }
  
  .room-name {
    font-weight: bold;
    color: #007bff;
  }
  
  .room-type {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 14px;
    color: white;
  }
  
  .room-type.host {
    background-color: #28a745;
  }
  
  .room-type.viewer {
    background-color: #17a2b8;
  }
  
  @media (max-width: 768px) {
    .room-controls {
      flex-direction: column;
    }
    
    .button-group {
      flex-direction: column;
    }
  }