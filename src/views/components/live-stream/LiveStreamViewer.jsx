import React, { useState, useRef, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { liveSocket } from "../../../helpers/context/socket";
import { setApiMessage } from "../../../helpers/context/toaster";
import { MdOutlineEmojiEmotions } from "react-icons/md";
import Send from "../../../assets/images/svg_icon/sned.svg";
import EmojiPicker from "emoji-picker-react";

const LiveStreamViewer = () => {
  const { streamId } = useParams();
  const navigate = useNavigate();
  const socket = liveSocket;

  const [isConnected, setIsConnected] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stream, setStream] = useState(null);
  const [comments, setComments] = useState([]);
  const [commentInput, setCommentInput] = useState("");
  const [viewerCount, setViewerCount] = useState(0);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [username, setUsername] = useState("");
  const [showUsernameModal, setShowUsernameModal] = useState(true);
  const [platform, setPlatform] = useState("");
  const [isMobile, setIsMobile] = useState(false);
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);

  const localVideoRef = useRef(null);
  const commentsContainerRef = useRef(null);
  const peerConnectionsRef = useRef({});
  const pendingIceCandidatesRef = useRef({});
  const remoteStreamRef = useRef(null);
  const emojiPickerRef = useRef(null);

  // Close on outside click
  useEffect(() => {
    function handleClickOutside(event) {
      if (
        emojiPickerRef.current &&
        !emojiPickerRef.current.contains(event.target)
      ) {
        setShowEmojiPicker(false);
      }
    }

    if (showEmojiPicker) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showEmojiPicker]);

  // ICE configuration for WebRTC
  const iceConfiguration = {
    iceServers: [
      { urls: "stun:stun.l.google.com:19302" },
      { urls: "stun:stun1.l.google.com:19302" },
      { urls: "stun:stun2.l.google.com:19302" },
      { urls: "stun:stun3.l.google.com:19302" },
      { urls: "stun:stun4.l.google.com:19302" },
      {
        urls: "turn:openrelay.metered.ca:80",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
      {
        urls: "turn:openrelay.metered.ca:443",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
      {
        urls: "turn:openrelay.metered.ca:443?transport=tcp",
        username: "openrelayproject",
        credential: "openrelayproject",
      },
    ],
    iceCandidatePoolSize: 10,
  };

  // Generate random user ID for anonymous viewers
  const generateAnonymousUserId = () => {
    return `anon_${Math.random().toString(36).substr(2, 9)}`;
  };

  const anonymousUserId = generateAnonymousUserId();

  // Detect platform and device
  const detectPlatform = () => {
    const userAgent = navigator.userAgent.toLowerCase();
    let detectedPlatform = "web";
    let isMobileDevice = false;

    // Detect mobile devices
    if (
      /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(
        userAgent
      )
    ) {
      isMobileDevice = true;
    }

    // Detect specific platforms
    if (userAgent.includes("facebook")) {
      detectedPlatform = "facebook";
    } else if (userAgent.includes("instagram")) {
      detectedPlatform = "instagram";
    } else if (userAgent.includes("whatsapp")) {
      detectedPlatform = "whatsapp";
    } else if (userAgent.includes("telegram")) {
      detectedPlatform = "telegram";
    } else if (userAgent.includes("twitter")) {
      detectedPlatform = "twitter";
    } else if (userAgent.includes("linkedin")) {
      detectedPlatform = "linkedin";
    } else if (userAgent.includes("youtube")) {
      detectedPlatform = "youtube";
    } else if (userAgent.includes("tiktok")) {
      detectedPlatform = "tiktok";
    } else if (userAgent.includes("snapchat")) {
      detectedPlatform = "snapchat";
    } else if (userAgent.includes("discord")) {
      detectedPlatform = "discord";
    }

    setPlatform(detectedPlatform);
    setIsMobile(isMobileDevice);
  };

  // Format stream ID for universal compatibility
  const formatStreamId = (id) => {
    // Remove any prefixes like 'liv-' if present
    if (id.startsWith("liv-")) {
      return id;
    }
    // Add 'liv-' prefix if not present
    return `liv-${id}`;
  };

  const formattedStreamId = formatStreamId(streamId);

  // Function to scroll to bottom of comments
  const scrollToBottom = () => {
    if (commentsContainerRef.current) {
      commentsContainerRef.current.scrollTo({
        top: commentsContainerRef.current.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  // Handle scroll events to show/hide scroll-to-bottom button
  const handleScroll = () => {
    if (commentsContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        commentsContainerRef.current;
      const isNearBottom = scrollHeight - scrollTop - clientHeight < 50;
      setShowScrollToBottom(!isNearBottom);
    }
  };

  // Function to send comment
  const sendComment = () => {
    if (!commentInput.trim() || !socket || !streamId || !username) return;

    const comment = commentInput.trim();
    console.log("[sendComment] Sending comment:", comment);

    socket.emit("send_comment", {
      room: streamId,
      comment: comment,
      userId: anonymousUserId,
      username: username,
      is_animated: false,
    });

    setCommentInput("");
    setTimeout(() => {
      scrollToBottom();
    }, 50);
  };

  // Function to send emoji comment
  const sendEmojiComment = (emoji) => {
    if (!socket || !streamId || !username) return;

    console.log("[sendEmojiComment] Sending emoji:", emoji);

    socket.emit("send_comment", {
      room: streamId,
      comment: emoji,
      userId: anonymousUserId,
      username: username,
      is_animated: true,
    });

    setTimeout(() => {
      scrollToBottom();
    }, 50);
  };

  // Create peer connection
  const createPeerConnection = (userId) => {
    if (peerConnectionsRef.current[userId]) {
      console.log("Peer connection already exists for:", userId);
      return peerConnectionsRef.current[userId];
    }

    const pc = new RTCPeerConnection(iceConfiguration);

    pc.onicecandidate = (event) => {
      if (event.candidate) {
        socket.emit("ice_candidate", {
          candidate: {
            candidate: event.candidate.candidate,
            sdpMid: event.candidate.sdpMid,
            sdpMLineIndex: event.candidate.sdpMLineIndex,
          },
          userId: userId,
          room: streamId,
        });
      }
    };

    pc.oniceconnectionstatechange = () => {
      console.log(
        `[PeerConnection] ICE state for ${userId}:`,
        pc.iceConnectionState
      );
    };

    pc.onconnectionstatechange = () => {
      console.log(
        `[PeerConnection] Connection state for ${userId}:`,
        pc.connectionState
      );
    };

    pc.ontrack = (event) => {
      console.log(
        `[ontrack] Remote track received from ${userId}`,
        event.streams[0]
      );

      if (event.streams && event.streams.length > 0) {
        const streamId = event.streams[0].id;
        console.log(
          `[ontrack] Stream ID: ${streamId}, Track kind: ${event.track.kind}`
        );

        setStream(event.streams[0]);
        remoteStreamRef.current = event.streams[0];

        if (localVideoRef.current) {
          localVideoRef.current.srcObject = event.streams[0];
          localVideoRef.current.play().catch((e) => {
            console.error("[ontrack] Error playing remote video:", e);
          });
        }
      }
    };

    peerConnectionsRef.current[userId] = pc;
    console.log(
      "[createPeerConnection] Created new peer connection for:",
      userId
    );
    return pc;
  };

  // Join the live stream
  const joinStream = () => {
    if (!socket || !isConnected || !formattedStreamId || !username) return;

    setIsLoading(true);
    setError(null);

    console.log("[joinStream] Joining stream:", formattedStreamId);
    socket.emit("check_room", { room: formattedStreamId });
    socket.emit("join_room", { room: formattedStreamId });
    socket.emit("get_comment_list", { room: formattedStreamId });
    socket.emit("current_users_number", { room: formattedStreamId });

    setShowUsernameModal(false);
  };

  // Handle username submission
  const handleUsernameSubmit = (e) => {
    e.preventDefault();
    if (username.trim()) {
      joinStream();
    }
  };

  // Copy stream link to clipboard
  const copyStreamLink = () => {
    const streamLink = `${window.location.origin}/u/${streamId}`;
    navigator.clipboard
      .writeText(streamLink)
      .then(() => {
        setApiMessage("success", "Universal stream link copied to clipboard!");
      })
      .catch(() => {
        setApiMessage("error", "Failed to copy link");
      });
  };

  // Share stream link
  const shareStreamLink = () => {
    copyStreamLink();
  };

  // Generate universal link for any platform
  const generateUniversalLink = () => {
    return `${window.location.origin}/u/${streamId}`;
  };

  // Auto-scroll to bottom when comments change
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      scrollToBottom();
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [comments]);

  // Effect for playing remote stream
  useEffect(() => {
    if (localVideoRef.current && stream) {
      console.log("[VideoEffect] Setting remote stream for viewer");
      localVideoRef.current.srcObject = stream;

      const handleLoadedMetadata = () => {
        console.log("[VideoEffect] Remote stream metadata loaded");
        if (localVideoRef.current && !localVideoRef.current.paused) {
          localVideoRef.current.play().catch((e) => {
            console.error("Error playing remote video:", e);
            setError("Error playing stream");
          });
        }
      };

      const handleCanPlay = () => {
        console.log("[VideoEffect] Remote stream can play");
        setIsLoading(false);
      };

      const handleError = (e) => {
        console.error("[VideoEffect] Remote stream error:", e);
        setError("Error loading stream");
        setIsLoading(false);
      };

      localVideoRef.current.onloadedmetadata = handleLoadedMetadata;
      localVideoRef.current.oncanplay = handleCanPlay;
      localVideoRef.current.onerror = handleError;

      return () => {
        if (localVideoRef.current) {
          localVideoRef.current.onloadedmetadata = null;
          localVideoRef.current.oncanplay = null;
          localVideoRef.current.onerror = null;
          localVideoRef.current.srcObject = null;
        }
      };
    }
  }, [stream]);

  // Socket event handlers
  useEffect(() => {
    if (!socket) {
      setError("Socket connection not available");
      return;
    }

    const handleConnect = () => {
      console.log("[socket] Connected");
      setIsConnected(true);
    };

    const handleDisconnect = () => {
      console.log("[socket] Disconnected");
      setIsConnected(false);
      setError("Connection lost");
    };

    const handleConnectError = (error) => {
      console.error("[socket] Connection error:", error);
      setError("Connection error");
    };

    const handleCheckRoom = (data) => {
      if (!data.valid) {
        setError("Live stream not found or has ended");
        setIsLoading(false);
      }
    };

    const handleOffer = async (data) => {
      if (data.room === formattedStreamId) {
        const broadcasterId = data.userId;
        console.log(
          "[handleOffer] Received offer from broadcaster:",
          broadcasterId
        );
        const pc = createPeerConnection(broadcasterId);

        try {
          const offerToSet = data.offer.sdp ? data.offer : data.offer.offer;
          await pc.setRemoteDescription(offerToSet);

          const pending = pendingIceCandidatesRef.current[broadcasterId];
          if (pending && pending.length > 0) {
            for (const candidate of pending) {
              const iceCandidate = new RTCIceCandidate(candidate);
              await pc.addIceCandidate(iceCandidate);
            }
            pendingIceCandidatesRef.current[broadcasterId] = [];
          }

          const answer = await pc.createAnswer();
          await pc.setLocalDescription(answer);

          socket.emit("answer", {
            answer: {
              type: answer.type,
              sdp: answer.sdp,
            },
            userId: broadcasterId,
            room: streamId,
          });
        } catch (error) {
          console.error("[handleOffer] Error handling offer:", error);
          setError("Failed to connect to stream");
        }
      }
    };

    const handleIceCandidate = async (data) => {
      if (data.room === formattedStreamId) {
        try {
          const pc = peerConnectionsRef.current[data.userId];
          if (pc && pc.remoteDescription) {
            const candidateData = data.candidate.candidate
              ? data.candidate
              : data.candidate;
            const iceCandidate = new RTCIceCandidate(candidateData);
            await pc.addIceCandidate(iceCandidate);
          } else if (pc) {
            if (!pendingIceCandidatesRef.current[data.userId]) {
              pendingIceCandidatesRef.current[data.userId] = [];
            }
            pendingIceCandidatesRef.current[data.userId].push(data.candidate);
          }
        } catch (error) {
          console.error(
            "[handleIceCandidate] Error adding ICE candidate:",
            error
          );
        }
      }
    };

    const handleCommentList = (data) => {
      if (data.room === formattedStreamId) {
        setComments([]);
        if (data.comments && Array.isArray(data.comments)) {
          const formattedComments = data.comments.map((comment) => ({
            message: comment.comment || comment.message,
            username: comment.username,
            timestamp: comment.timestamp || new Date(),
            isOwnMessage: false,
          }));
          setComments(formattedComments);
          setTimeout(() => {
            scrollToBottom();
          }, 200);
        }
      }
    };

    const handleNewComment = (data) => {
      if (data.room === formattedStreamId) {
        setComments((prev) => [
          ...prev,
          {
            message: data.comment,
            username: data.username,
            timestamp: data.timestamp || new Date(),
            isOwnMessage: false,
          },
        ]);
      }
    };

    const handleCurrentUsersNumber = (data) => {
      if (data.room === formattedStreamId) {
        const viewerCount = data.count > 0 ? data.count - 2 : 0;
        setViewerCount(Math.max(0, viewerCount));
      }
    };

    const handleRoomClosed = (data) => {
      if (data.room === formattedStreamId) {
        setError("The live stream has ended");
        setIsLoading(false);
      }
    };

    const handleStreamEnded = (data) => {
      if (data.room === formattedStreamId) {
        setError("The live stream has ended");
        setIsLoading(false);
      }
    };

    if (socket.connected) {
      setIsConnected(true);
    }

    socket.on("connect", handleConnect);
    socket.on("disconnect", handleDisconnect);
    socket.on("connect_error", handleConnectError);
    socket.on("check_room", handleCheckRoom);
    socket.on("offer", handleOffer);
    socket.on("ice_candidate", handleIceCandidate);
    socket.on("comment_list", handleCommentList);
    socket.on("new_comment", handleNewComment);
    socket.on("current_users_number", handleCurrentUsersNumber);
    socket.on("room_closed", handleRoomClosed);
    socket.on("stream_ended", handleStreamEnded);

    return () => {
      socket.off("connect", handleConnect);
      socket.off("disconnect", handleDisconnect);
      socket.off("connect_error", handleConnectError);
      socket.off("check_room", handleCheckRoom);
      socket.off("offer", handleOffer);
      socket.off("ice_candidate", handleIceCandidate);
      socket.off("comment_list", handleCommentList);
      socket.off("new_comment", handleNewComment);
      socket.off("current_users_number", handleCurrentUsersNumber);
      socket.off("room_closed", handleRoomClosed);
      socket.off("stream_ended", handleStreamEnded);
    };
  }, [socket, formattedStreamId]);

  // Detect platform on mount
  useEffect(() => {
    detectPlatform();
  }, []);

  // Username modal
  if (showUsernameModal) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#F9F9F9] via-[#F3EDE7] to-[#E0E0E0] font-Ubuntu flex items-center justify-center p-4">
        <div className="bg-white rounded-2xl shadow-lg p-8 max-w-md w-full">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold text-[#563D39] mb-2">
              Join Live Stream
            </h1>
            {platform && platform !== "web" && (
              <div className="flex items-center justify-center gap-2 text-sm text-[#674941]">
                <span>Opening from</span>
                <span className="font-semibold capitalize">{platform}</span>
                {isMobile && <span>• Mobile</span>}
              </div>
            )}
            <div className="text-xs text-[#674941] mt-2">
              Stream ID: {formattedStreamId}
            </div>
          </div>

          <form onSubmit={handleUsernameSubmit} className="space-y-6">
            <div>
              <label className="block text-[#563D39] font-semibold mb-2">
                Your Name
              </label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                placeholder="Enter your name"
                className="w-full px-4 py-3 border border-[#E0E0E0] rounded-lg focus:outline-none focus:ring-2 focus:ring-[#674941] font-[Ubuntu,sans-serif]"
                required
                maxLength={20}
              />
            </div>

            <button
              type="submit"
              disabled={!username.trim()}
              className={`w-full py-3 rounded-lg font-bold shadow transition ${
                username.trim()
                  ? "bg-gradient-to-r from-[#563D39] to-[#BC857D] hover:scale-105 text-white"
                  : "bg-gray-300 text-gray-400 cursor-not-allowed"
              }`}
            >
              Join Stream
            </button>
          </form>

          <div className="mt-4 text-center">
            <button
              onClick={() => navigate("/")}
              className="text-[#674941] hover:text-[#563D39] transition mr-4"
            >
              Back to Home
            </button>
            <button
              onClick={copyStreamLink}
              className="text-[#674941] hover:text-[#563D39] transition"
            >
              Copy Link
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen overflow-y-auto bg-gradient-to-br from-[#F9F9F9] via-[#F3EDE7] to-[#E0E0E0] font-Ubuntu">
      <header className="sticky top-0 z-20 backdrop-blur-md px-6 py-4 flex items-center justify-between">
        <h1 className="text-2xl font-bold text-[#563D39] tracking-tight">
          Live Stream
        </h1>
        <div className="flex justify-center">
          <p className="text-Red font-bold text-2xl">Welcome to Flowkar </p>
        </div>
        <div className="flex items-center gap-3">
          <span className="flex items-center gap-1 bg-[#EB5757] text-white px-5 py-2 rounded-[10px] text-sm font-semibold">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              />
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              />
            </svg>
            {viewerCount}
          </span>
          <button
            onClick={shareStreamLink}
            className="px-4 py-2 bg-Red text-white rounded-[10px] font-bold  text-[13.5px]"
          >
            Share
          </button>
        </div>
      </header>

      <main className="max-w-[90rem] mx-auto flex flex-col lg:flex-row gap-8 py-8 px-2 md:px-6">
        {/* Video Area */}
        <section className="flex-1 w-full mx-auto">
          <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="relative  bg-gray-100  overflow-hidden flex items-center justify-center p-4 sm:p-10">
              <video
                ref={localVideoRef}
                autoPlay
                playsInline
                className="w-full h-full rounded-[12px] object-cover"
              />

              {isLoading && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
                    <p>Connecting to stream...</p>
                  </div>
                </div>
              )}

              {error && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-white text-center">
                    <div className="text-2xl mb-4">⚠️</div>
                    <p className="text-lg font-semibold">{error}</p>
                    <button
                      onClick={() => navigate("/")}
                      className="mt-4 px-6 py-2 bg-white text-[#563D39] rounded-lg font-bold"
                    >
                      Go Back
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </section>

        {/* Comments Panel */}
        <aside className="w-full lg:w-[350px] xl:w-[400px] flex flex-col bg-white max-h-[500px] sm:max-h-[600px] lg:max-h-[600px] h-[400px] lg:h-[520px] xl:min-h-[625px]  2xl:min-h-[740px] relative order-1 lg:order-2">
          <div className="p-4 border-b border-[#E0E0E0] bg-gradient-to-r from-[#F9F9F9] to-[#E0E0E0] rounded-t-2xl">
            <h3 className="font-bold text-[#563D39] text-lg">Comments</h3>
          </div>

          <div
            className="flex-1 overflow-y-auto p-4 space-y-4 thin-scrollbar pb-32 relative"
            ref={commentsContainerRef}
            onScroll={handleScroll}
          >
            {showScrollToBottom && comments.length > 0 && (
              <button
                onClick={scrollToBottom}
                className="absolute bottom-20 right-4 z-10 bg-gradient-to-r from-[#563D39] to-[#BC857D] text-white p-2 rounded-full shadow-lg hover:scale-110 transition-all duration-200"
                title="Scroll to bottom"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 14l-7 7m0 0l-7-7m7 7V3"
                  />
                </svg>
              </button>
            )}

            {comments.length === 0 ? (
              <div className="flex flex-col items-center justify-center h-full text-[#674941] opacity-60">
                <span>No comments yet</span>
              </div>
            ) : (
              comments.map((comment, index) => (
                <div key={index} className="flex items-end gap-2 justify-start">
                  <div className="w-8 h-8 rounded-full bg-gradient-to-r from-[#563D39] to-[#BC857D] flex items-center justify-center text-white font-bold text-sm">
                    {comment.username?.[0]?.toUpperCase() || "?"}
                  </div>
                  <div className="max-w-[75%] px-4 py-2 rounded-2xl shadow bg-[#F9F9F9] text-[#674941]">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="font-semibold text-xs">
                        {comment.username}
                      </span>
                      <span className="text-xs text-[#BC857D]">
                        {comment.timestamp instanceof Date
                          ? comment.timestamp.toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })
                          : new Date(comment.timestamp).toLocaleTimeString([], {
                              hour: "2-digit",
                              minute: "2-digit",
                            })}
                      </span>
                    </div>
                    <p className="text-sm break-words">{comment.message}</p>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Input */}
          <div className="absolute bottom-0 left-0 w-full p-2 sm:p-3 bg-white ">
            <div className="flex items-center gap-2 relative">
              {/* Input with Send Icon Inside */}
              <div className="relative flex-1 ">
                <input
                  type="text"
                  placeholder="Add a comment..."
                  className=" px-4 sm:px-5 py-2.5 w-full pl-10 pr-12 sm:pl-10 sm:pr-10 rounded-[10px] font-Ubuntu placeholder-white text-xs sm:text-sm focus:border-Red focus:ring-0 focus:outline-none bg-Red text-white"
                  value={commentInput}
                  onChange={(e) => setCommentInput(e.target.value)}
                  onKeyPress={(e) =>
                    e.key === "Enter" && e.target.value.trim() && sendComment()
                  }
                />
                {/* Emoji Picker Icon */}
                <div className="">
                  <button
                    onClick={() => setShowEmojiPicker(!showEmojiPicker)}
                    className="text-white text-xl  p-2 rounded-full absolute left-0 top-1/2 -translate-y-1/2"
                  >
                    <MdOutlineEmojiEmotions />
                  </button>

                  {showEmojiPicker && (
                    <div
                      ref={emojiPickerRef}
                      className="absolute bottom-full mb-2 z-50"
                    >
                      <EmojiPicker
                        onEmojiClick={(emojiData) =>
                          setCommentInput((prev) => prev + emojiData.emoji)
                        }
                        theme="white"
                      />
                    </div>
                  )}
                </div>
                <button
                  onClick={sendComment}
                  disabled={!commentInput.trim()}
                  className={`absolute right-2 top-1/2 -translate-y-1/2 text-xs sm:text-sm bg-white p-1 rounded-[5px] ${
                    commentInput.trim()
                      ? "text-white hover:scale-110"
                      : "text-gray-300 cursor-not-allowed"
                  }`}
                >
                  <img src={Send} alt="" className="h-4 w-4 p-px" />
                </button>
              </div>
            </div>
          </div>
        </aside>
      </main>

      <style jsx>{`
        .thin-scrollbar::-webkit-scrollbar {
          width: 6px;
        }
        .thin-scrollbar::-webkit-scrollbar-thumb {
          background: #e0e0e0;
          border-radius: 6px;
        }
        .thin-scrollbar::-webkit-scrollbar-track {
          background: #f9f9f9;
        }

        .emoji-scrollbar::-webkit-scrollbar {
          height: 4px;
        }
        .emoji-scrollbar::-webkit-scrollbar-thumb {
          background: #bc857d;
          border-radius: 2px;
        }
        .emoji-scrollbar::-webkit-scrollbar-track {
          background: #f0f0f0;
          border-radius: 2px;
        }
      `}</style>
    </div>
  );
};

export default LiveStreamViewer;
