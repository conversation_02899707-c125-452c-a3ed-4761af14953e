// import React, { useContext, useState } from "react";
// import { IntlContext } from "../../../App";
// import siteConstant from "../../../helpers/constant/siteConstant";
// import contacthome from "../../../assets/images/contact-us-home.svg";
// import Card from "./card";
// import Info from "./info";
// import { Dialog, DialogContent } from "@mui/material";
// import ContactForm from "./contactform";
// import Footer from "./footer";

// function ContactusPage() {
//   const intlContext = useContext(IntlContext);
//   const localesData = intlContext?.messages;

//   const [open, setOpen] = useState(false);

//   const handleContactClick = () => {
//     setOpen(true);
//   };

//   const handleClose = () => {
//     setOpen(false);
//   };

//   return (
//     <>
//       <div className="bg-white font-Ubuntu h-screen overflow-y-auto">
//         <div className="container mx-auto px-8">
//           <div className="flex justify-between items-center">
//             <div className="flex items-center">
//               <img
//                 src={siteConstant.SOCIAL_ICONS.FLOWKARLOGO}
//                 className="h-[60px] w-[60px] sm:h-[65px] sm:w-[65px] lg:h-[75px] lg:w-[75px]"
//               />
//               <p className="font-Flowkarlogo text-[35px]  sm:text-[38px] lg:text-[40px] 2xl:text-[47px] mb-4">
//                 {" "}
//                 {localesData?.USER_WEB?.FLOWKAR}
//               </p>
//             </div>
//             <div
//               className="px-3 py-2 lg:px-4 lg:py-3 text-sm lg:text-md bg-[#563D39] text-white font-semibold rounded-full cursor-pointer"
//               onClick={handleContactClick}
//             >
//               Contact Us
//             </div>
//           </div>
//         </div>
//         <div className="shadow-custom-shadow h-[1.5px] bg-gray-100 w-full"></div>
//         <div className="container mx-auto px-8">
//           <div className="flex flex-col sm:flex-row justify-between items-center">
//             <div className="mt-5 sm:mt-10">
//               <p className="text-sm sm:text-xl 2xl:text-[24px] ">Get Started</p>
//               <p className="text-[26px] sm:text-[27px] md:text-[37px] lg:text-[50px] 2xl:text-[60px] mt-2 leading-normal font-bold">
//                 Get in touch with
//                 <br />
//                 us. We're here to <br />
//                 assist you.
//               </p>
//             </div>
//             <div className="mt-4 md:mt-0">
//               <img
//                 src={contacthome}
//                 className="w-full md:w-auto max-w-xs sm:w-[300px] lg:max-w-[400px] 2xl:max-w-lg "
//                 alt="Contact"
//               />
//             </div>
//           </div>
//         </div>
//         <div className="bg-[#F4F4F4]">
//           <Card />
//         </div>
//         <Info />
//         <Footer />
//         <Dialog
//           open={open}
//           onClose={handleClose}
//           sx={{
//             "& .MuiDialog-paper": {
//               backgroundColor: "rgba(255, 255, 255, 0.9)",
//               borderRadius: "26px",
//               boxShadow: "0 4px 20px rgba(0, 0, 0, 0.2)",
//               width: {
//                 xs: "90%",
//                 sm: "80%",
//                 md: "70%",
//                 lg: "600px",
//                 xl: "800px",
//               },
//             },
//           }}
//         >
//           <div className="bg-white p-7 rounded-xl shadow-lg w-full">
//             <DialogContent>
//               <ContactForm handleClose={handleClose} />
//             </DialogContent>
//           </div>
//         </Dialog>
//       </div>
//     </>
//   );
// }

// export default ContactusPage;
