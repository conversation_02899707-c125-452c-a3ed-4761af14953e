import React, { useContext, useState } from "react";
import { S<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, But<PERSON> } from "@mui/material";
import { CustomTextField } from "../custom/CustomTextField";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    name: "",
    surname: "",
    email: "",
    description: "",
  });
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState("");
  const [loading, setLoading] = useState(false);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
  };

  const validateEmail = (email) => {
    return /\S+@\S+\.\S+/.test(email);
  };

  const handleSubmit = (e) => {
    e.preventDefault();

    if (
      !formData.name ||
      !formData.surname ||
      !formData?.description ||
      !validateEmail(formData.email)
    ) {
      setSnackbarMessage("Please fill all fields correctly.");
      setSnackbarOpen(true);
      return;
    }

    setLoading(true);

    setTimeout(() => {
      setLoading(false);
      setSnackbarMessage("Your message has been sent!");
      setSnackbarOpen(true);
      setFormData({ name: "", surname: "", email: "", description: "" });
    }, 2000);
  };

  // const handleSnackbarClose = () => {
  //   setSnackbarOpen(false);
  // };

  return (
    <form
      className="flex flex-col gap-4 w-xl font-Ubuntu"
      onSubmit={handleSubmit}
    >
      <p className="text-center font-medium text-black text-xl lg:text-2xl 2xl:text-3xl py-2 sm:py-4 ">
        Contact Us
      </p>
      <CustomTextField
        variant="outlined"
        borderRadius="20px"
        fullWidth
        name="name"
        label="Name"
        className="w-full rounded-lg border-gray-200 p-4 pe-12 text-lg"
        placeholder="Enter Your Name"
        onChange={handleChange}
        value={formData.name}
        required
      />

      <CustomTextField
        variant="outlined"
        borderRadius="20px"
        fullWidth
        label="Surname"
        name="surname"
        className="w-full rounded-lg border-gray-200 p-4 pe-12 text-sm"
        placeholder="Enter Your SirName"
        value={formData.surname}
        onChange={handleChange}
        required
      />

      <CustomTextField
        variant="outlined"
        borderRadius="20px"
        fullWidth
        label="Email"
        name="email"
        className="w-full rounded-lg border-gray-200 p-4 pe-12 text-sm"
        placeholder="Enter Your Email"
        value={formData.email}
        onChange={handleChange}
        error={!validateEmail(formData.email) && formData.email !== ""}
        helperText={
          !validateEmail(formData.email) && formData.email !== ""
            ? "Please enter a valid email address"
            : ""
        }
        required
      />

      <CustomTextField
        variant="outlined"
        borderRadius="20px"
        label="description"
        onChange={handleChange}
        fullWidth
        type="text"
        name="description"
        multiline
        rows={3}
        className="w-full rounded-lg border-gray-200 p-4 pe-12 text-sm"
        placeholder="Enter Description"
        value={formData.description}
        required
      />
      <Button
        type="submit"
        variant="contained"
        sx={{
          backgroundColor: "#563D39",
          color: "white",
          "&:hover": {
            backgroundColor: "#d21717",
          },
          borderRadius: "20px",
          fontWeight: "bold",
          transition: "background-color 0.3s ease",
          fontSize: {
            xs: "0.9rem",
            sm: "1.1rem",
          },
          padding: {
            xs: "8px 16px",
            sm: "10px 18px",
          },
          fontFamily: "Ubuntu, sans-serif",
          textTransform: "none",
        }}
        disabled={loading}
      >
        {loading ? "Sending..." : "Send"}
      </Button>

      <Snackbar
        open={snackbarOpen}
        autoHideDuration={3000}
        message={snackbarMessage}
        anchorOrigin={{ vertical: "top", horizontal: "center" }}
      />
    </form>
  );
};

export default ContactForm;
