import React from "react";

function Footer() {
  return (
    <div
      className="bg-[#555555] flex flex-col md:flex md:flex-row justify-around  px-4 md:px-2 py-10
]"
    >
      <div>
        <p className="text-[20px] sm:text-[25px] md:text-[28px] lg:text-[31px] 2xl:text-[35px] mt-2 leading-normal font-bold text-white">
          Subcribe to our Newsletter
        </p>
        <p className="text-gray-200 text-[12px] md:text-sm">
          Subscribe for Updates: Stay informed about the latest investor
          <br />
          updates, financial results, and announcements by subscribing to <br />
          our newsletter.
        </p>
      </div>
      <div>
        <div className="flex flex-col items-center mt-5 md:mt-10 relative">
          <input
            type="email"
            placeholder="Enter Your Email"
            className="border rounded-xl p-3 sm:p-4 pe-[80px] sm:pe-[80px] md:pe-[80px] lg:pe-[80px] xl:pe-[70px] 2xl:pe-20 w-full md:w-96 mb-4 focus:outline-none focus:ring-2 bg-[#FFFFFF42] focus:ring-[#efebe9] text-sm placeholder:text-white"
            required
          />

          <button className="bg-white text-Red rounded-r-xl lg px-2 py-[13px] sm:py-[16.8px] absolute top-0 right-[0] text-[12px] font-medium">
            Subscribe
          </button>
        </div>
      </div>
    </div>
  );
}

export default Footer;
