import React from "react";
import inovation from "../../../assets/images/svg_icon/contact-inavation.svg";
import like from "../../../assets/images/svg_icon/contact-like.svg";
import user from "../../../assets/images/svg_icon/user-contact.svg";

function Card() {
  const cardData = [
    {
      icon: user,
      title: "User-Centric Assistance",
      description:
        "Our dedicated support team ensures that your experience on Flowkar is seamless and enjoyable. From technical help to feature guidance, we're here to make sure you get the most out of Flowkar every step of the way.",
    },
    {
      icon: inovation,
      title: "Innovative Solutions",
      description:
        "At Flowkar, we're constantly pushing the boundaries of what's possible in social media. Our innovation-driven approach means that you always have access to the latest features, tailored to enhance your social media journey.",
    },
    {
      icon: like,
      title: "Commitment to Excellence",
      description:
        "We pride ourselves on delivering a platform that meets the highest standards of performance and reliability. Your satisfaction is at the heart of everything we do, ensuring a premium experience every time you use Flowkar.",
    },
  ];

  return (
    <div className="container mx-auto px-8 py-16">
      <div className="">
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 sm:gap-6 md:gap-8">
          {cardData.map((card, index) => (
            <div
              key={index}
              className="bg-[#FFFFFF] dark:bg-darkGrayFaq p-8 rounded-[38.31px] shadow-custom-shadow flex flex-col items-center text-center transition-transform duration-500 ease-out hover:scale-105"
            >
              <div className="bg-profileCardBG p-4 rounded-full mt-8 ">
                <img src={card.icon} alt={card.title} className="w-8 h-8" />
              </div>
              <h2 className="text-xl font-semibold my-5 text-black dark:text-white">
                {card.title}
              </h2>
              <p className=" dark:text-gray-300 text-[14px] 2xl:text-[15px] text-center">
                {card.description}
              </p>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

export default Card;
