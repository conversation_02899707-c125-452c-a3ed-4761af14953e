import React, { useState, useRef, useEffect, useMemo } from "react";
import calendar from "../../../assets/images/Analytics/calendar.svg";
import customParseFormat from "dayjs/plugin/customParseFormat";
import Loader from "../../../helpers/UI/Loader";

import {
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from "recharts";
import $ from "jquery";
import "daterangepicker";
import "daterangepicker/daterangepicker.css";
import { tab } from "@testing-library/user-event/dist/tab";
import dayjs from "dayjs";
import moment from "moment";
window.moment = moment;
dayjs.extend(customParseFormat);

// Custom styles for daterangepicker (unchanged)
const customStyles = `
  .daterangepicker {
    border-color: #E0E0E0;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 8px;
    font-family: Ubuntu;
  }
  
  .daterangepicker .calendar-table {
    border-color: #E0E0E0;
    background-color: #fff;
  }
  
  .daterangepicker td.active, 
  .daterangepicker td.active:hover {
    background-color: #563D39 !important;
    border-color: transparent;
    color: white !important;
    border-radius: 0;
  }
  
  .daterangepicker td.in-range {
    background-color: rgba(86, 61, 57, 0.05);
    color: #373E3E;
    border-radius: 0 !important;
  }
  
  .daterangepicker td.start-date {
    border-radius: 8px 0 0 8px !important;
  }
  
  .daterangepicker td.end-date {
    border-radius: 0 8px 8px 0 !important;
  }
  
  .daterangepicker td.start-date.end-date {
    border-radius: 8px !important;
  }
  
  .daterangepicker .ranges li.active {
    background-color: #563D39;
    border-color: transparent;
    color: white;
  }
  
  .daterangepicker .ranges li:hover {
    background-color: rgba(86, 61, 57, 0.05);
    color: #563D39;
  }
  
  .daterangepicker .drp-buttons {
    border-top: 1px solid #E0E0E0;
    padding: 10px;
  }
  
  .daterangepicker .drp-buttons .btn {
    padding: 6px 12px;
    font-size: 13px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s ease;
  }
  
  .daterangepicker .drp-buttons .btn-primary {
    background-color: #563D39;
    border-color: transparent;
    color: white;
  }
  
  .daterangepicker .drp-buttons .btn-primary:hover {
    background-color: #4a352f;
  }
  
  .daterangepicker .drp-buttons .btn-default {
    background-color: #F7F6F5;
    border-color: #E0E0E0;
    color: #373E3E;
  }
  
  .daterangepicker .drp-buttons .btn-default:hover {
    background-color: #E0E0E0;
  }
  
  .daterangepicker .calendar-table th, 
  .daterangepicker .calendar-table td {
    font-size: 13px;
    padding: 6px;
    width: 28px;
    height: 28px;
  }
  
  .daterangepicker .calendar-table th {
    color: #373E3E;
    font-weight: 600;
    font-size: 11px;
  }
  
  .daterangepicker .calendar-table td {
    color: #373E3E;
  }
  
  .daterangepicker .calendar-table td.available:hover {
    background-color: rgba(86, 61, 57, 0.05);
    color: #373E3E;
    border-radius: 8px;
  }
  
  .daterangepicker .calendar-table td.available.in-range:hover {
    border-radius: 0 !important;
  }
  
  .daterangepicker .ranges {
    border-right: 1px solid #E0E0E0;
    background-color: #fff;
  }
  
  .daterangepicker .ranges ul {
    padding: 8px;
    margin: 0;
  }
  
  .daterangepicker .ranges li {
    padding: 8px 12px;
    margin: 2px 0;
    border-radius: 4px;
    color: #373E3E;
    font-size: 13px;
    transition: all 0.2s ease;
  }
  
  .daterangepicker .calendar-table .next span,
  .daterangepicker .calendar-table .prev span {
    border-color: #373E3E;
  }
  
  .daterangepicker .calendar-table .next:hover span,
  .daterangepicker .calendar-table .prev:hover span {
    border-color: #563D39;
  }
  
  .daterangepicker .calendar-table .off.in-range {
    background-color: #f8f8f8;
    color: #999;
  }
  
  .daterangepicker .calendar-table .off {
    color: #ccc;
  }
  
  .daterangepicker td.today {
    background-color: #a38d89;
    color: #FFFFFF
   
  }
`;

// Add custom styles for native input[type=date] calendar picker (mobile)
const customDateInputStyles = `
  input[type="date"]::-webkit-calendar-picker-indicator {
    filter: invert(32%) sepia(16%) saturate(1100%) hue-rotate(330deg) brightness(90%) contrast(90%);
    /* This filter tints the icon to a brownish color similar to #563D39 */
  }
  input[type="date"] {
    color: #563D39;
    border-color: #E0E0E0;
    background: #fff;
    font-family: Ubuntu, sans-serif;
    font-weight: 500;
  }
  input[type="date"]:focus {
    outline: 2px solid #563D39;
    border-color: #563D39;
  }
  input[type="date"]::-webkit-input-placeholder {
    color: #A9ABAD;
  }
  input[type="date"]::-moz-placeholder {
    color: #A9ABAD;
  }
  input[type="date"]:-ms-input-placeholder {
    color: #A9ABAD;
  }
  input[type="date"]::placeholder {
    color: #A9ABAD;
  }
`;

// Add styles to document
const styleSheet = document.createElement("style");
styleSheet.type = "text/css";
styleSheet.innerText = customStyles + customDateInputStyles;
document.head.appendChild(styleSheet);

// Reusable Stat Card
const StatCard = ({ label, value }) => (
  <div className="bg-white py-4 sm:py-6 px-4 rounded-[12px]">
    <p className="text-sm sm:text-base font-bold text-[#616569] mb-1">
      {label}
    </p>
    <h3 className="text-xl sm:text-2xl font-bold">{value}</h3>
  </div>
);

// Reusable Metric Card
const MetricCard = ({ label, value }) => (
  <div className="text-center">
    <p className="text-sm text-gray-600 mb-1">{label}</p>
    <h3 className="text-xl font-bold">{value}</h3>
  </div>
);

// Modern Date Range Selector Component
const DateRangeSelector = ({ onDateRangeChange, dateRange }) => {
  const dateRangeRef = useRef(null);
  const [displayRange, setDisplayRange] = useState("");
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [mobileStart, setMobileStart] = useState(dateRange?.startDate || "");
  const [mobileEnd, setMobileEnd] = useState(dateRange?.endDate || "");

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    setMobileStart(dateRange?.startDate || "");
    setMobileEnd(dateRange?.endDate || "");
  }, [dateRange]);

  useEffect(() => {
    if (isMobile) return;
    if (!dateRangeRef.current) return;

    const $element = $(dateRangeRef.current);
    const start = moment(dateRange?.startDate);
    const end = moment(dateRange?.endDate);

    // Initialize daterangepicker
    $element.daterangepicker(
      {
        startDate: start,
        endDate: end,
        ranges: {
          Today: [moment(), moment()],
          Yesterday: [
            moment().subtract(1, "days"),
            moment().subtract(1, "days"),
          ],
          "Last 7 Days": [moment().subtract(6, "days"), moment()],
          "Last 30 Days": [moment().subtract(29, "days"), moment()],
          "This Month": [moment().startOf("month"), moment().endOf("month")],
          "Last Month": [
            moment().subtract(1, "month").startOf("month"),
            moment().subtract(1, "month").endOf("month"),
          ],
        },
        opens: "left",
        drops: "down",
        buttonClasses: "btn btn-sm",
        applyClass: "btn-primary",
        cancelClass: "btn-secondary",
        showCustomRangeLabel: false,
        alwaysShowCalendars: true,
        locale: {
          format: "MMMM D, YYYY",
          separator: " - ",
          applyLabel: "Apply",
          cancelLabel: "Cancel",
          fromLabel: "From",
          toLabel: "To",
          customRangeLabel: "Custom",
          weekLabel: "W",
          daysOfWeek: ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"],
          monthNames: [
            "January",
            "February",
            "March",
            "April",
            "May",
            "June",
            "July",
            "August",
            "September",
            "October",
            "November",
            "December",
          ],
          firstDay: 1,
        },
      },
      function (start, end) {
        const range =
          start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY");
        setDisplayRange(range);
      }
    );

    // Handle date range changes
    $element.on("apply.daterangepicker", function (ev, picker) {
      const range =
        picker.startDate.format("MMMM D, YYYY") +
        " - " +
        picker.endDate.format("MMMM D, YYYY");
      setDisplayRange(range);

      if (onDateRangeChange) {
        onDateRangeChange(
          picker.startDate.format("YYYY-MM-DD"),
          picker.endDate.format("YYYY-MM-DD")
        );
      }
    });

    // Set initial date range display
    const initialRange =
      start.format("MMMM D, YYYY") + " - " + end.format("MMMM D, YYYY");
    setDisplayRange(initialRange);

    // Cleanup
    return () => {
      $element.off("apply.daterangepicker");
      const picker = $element.data("daterangepicker");
      if (picker) {
        picker.remove();
      }
    };
  }, [onDateRangeChange, dateRange, isMobile]);

  if (isMobile) {
    return (
      <div className="flex flex-col gap-2 w-full">
        <label className="text-xs text-[#A9ABAD]">Start Date</label>
        <input
          type="text"
          placeholder="YYYY-MM-DD"
          className="border border-[#E0E0E0] rounded-[8px] px-4 py-2"
          value={mobileStart}
          max={mobileEnd || undefined}
          onChange={e => {
            setMobileStart(e.target.value);
          }}
        />
        <label className="text-xs text-[#A9ABAD]">End Date</label>
        <input
          type="text"
          placeholder="YYYY-MM-DD"
          className="border border-[#E0E0E0] rounded-[8px] px-4 py-2"
          value={mobileEnd}
          min={mobileStart || undefined}
          onChange={e => {
            setMobileEnd(e.target.value);
          }}
        />
        <button
          className="mt-2 bg-[#563D39] text-white rounded-[8px] px-4 py-2 font-medium disabled:opacity-50"
          disabled={!mobileStart || !mobileEnd}
          onClick={() => {
            if (onDateRangeChange && mobileStart && mobileEnd) {
              onDateRangeChange(mobileStart, mobileEnd);
            }
          }}
        >
          Search
        </button>
      </div>
    );
  }

  return (
    <div
      ref={dateRangeRef}
      className="flex items-center border border-[#E0E0E0] rounded-[8px] px-6 py-3 cursor-pointer hover:shadow-md transition-all duration-300"
    >
      <img src={calendar} alt="Date" className="mr-3" />
      <span className="text-base font-normal text-[#A9ABAD]">
        {displayRange}
      </span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        stroke="#A9ABAD"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
        className="ml-6"
      >
        <polyline points="6 9 12 15 18 9" />
      </svg>
    </div>
  );
};

// Donut Chart Component with optional left and right legends
const DonutChart = ({
  data,
  leftLegend,
  rightLegend,
  showLeftLegend = true,
  showRightLegend = true,
}) => {
  const [hoveredSegment, setHoveredSegment] = useState(null);
  const [isSingleColumn, setIsSingleColumn] = useState(
    window.innerWidth < 1536
  );

  // Calculate total and add "Other" category if needed
  const total = data.reduce((sum, d) => sum + d.value, 0);
  const processedData = [...data];
  const processedLeftLegend = leftLegend ? [...leftLegend] : null;
  const processedRightLegend = rightLegend ? [...rightLegend] : null;

  useEffect(() => {
    const handleResize = () => {
      setIsSingleColumn(window.innerWidth < 1536);
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  // Check for no data conditions
  if (!data || data.length === 0 || total === 0) {
    return (
      <div className="flex items-center justify-center w-full h-56 text-sm text-gray-500">
        No data available
      </div>
    );
  }

  if (total < 100 && total > 0) {
    const otherValue = 100 - total;
    processedData.push({
      color: "#E2D0CA", // Using a light color for "Other"
      value: otherValue,
      label: "Other",
    });

    if (processedLeftLegend) {
      processedLeftLegend.push({
        color: "#E2D0CA",
        label: "Other",
      });
    }

    if (processedRightLegend) {
      processedRightLegend.push({
        color: "#E2D0CA",
        label: "Other",
      });
    }
  }

  const GAP_ANGLE = 1; // Gap between segments in degrees
  const RADIUS = 40;
  const CENTER = 50;
  let currentAngle = -90; // Start from top

  const getArcPath = (startAngle, endAngle) => {
    const startRad = (startAngle * Math.PI) / 180;
    const endRad = (endAngle * Math.PI) / 180;

    const x1 = CENTER + RADIUS * Math.cos(startRad);
    const y1 = CENTER + RADIUS * Math.sin(startRad);
    const x2 = CENTER + RADIUS * Math.cos(endRad);
    const y2 = CENTER + RADIUS * Math.sin(endRad);

    const largeArcFlag = endAngle - startAngle <= 180 ? 0 : 1;

    return `M ${x1} ${y1} A ${RADIUS} ${RADIUS} 0 ${largeArcFlag} 1 ${x2} ${y2}`;
  };

  return (
    <div className="flex items-center justify-center w-full">
      {/* Left Legend - only shown if showLeftLegend is true and not in single column */}
      {showLeftLegend && processedLeftLegend && !isSingleColumn && (
        <div className="flex-none w-24 pr-4 text-xs">
          {processedLeftLegend.map(({ color, label }, i) => (
            <div
              key={i}
              className="flex flex-row-reverse gap-1 items-center mb-2"
            >
              <div
                className="w-3 h-3 mr-1 rounded-sm flex-shrink-0"
                style={{ backgroundColor: color }}
              />
              <span className="whitespace-nowrap text-[11px]">{label}</span>
            </div>
          ))}
        </div>
      )}

      {/* Donut Chart */}
      <div className="w-56 h-56 relative flex-none">
        <svg viewBox="0 0 100 100" className="w-full h-full">
          {processedData.map(({ color, value, label }, i) => {
            const angle =
              (value / 100) * (360 - processedData.length * GAP_ANGLE);
            const startAngle = currentAngle;
            const endAngle = startAngle + angle;
            const path = getArcPath(startAngle, endAngle);
            currentAngle = endAngle + GAP_ANGLE; // Add gap after each segment

            return (
              <path
                key={i}
                d={path}
                fill="transparent"
                stroke={color}
                strokeWidth="13"
                onMouseEnter={() => {
                  const segmentLabel =
                    (processedLeftLegend && processedLeftLegend[i]?.label) ||
                    (processedRightLegend && processedRightLegend[i]?.label) ||
                    `Segment ${i + 1}`;
                  setHoveredSegment({ value, color, label: segmentLabel });
                }}
                onMouseLeave={() => setHoveredSegment(null)}
                className="cursor-pointer transition-opacity duration-200 hover:opacity-90"
                style={{
                  strokeLinecap: "butt",
                  strokeLinejoin: "miter",
                  pointerEvents: "all",
                }}
              />
            );
          })}
          <circle cx={CENTER} cy={CENTER} r="31" fill="white" />
        </svg>

        {/* Tooltip */}
        {hoveredSegment !== null && (
          <div
            className="absolute bg-white shadow-md rounded px-2 py-1 text-xs border border-gray-200"
            style={{
              top: "50%",
              left: "50%",
              transform: "translate(-50%, -50%)",
              pointerEvents: "none",
            }}
          >
            <div className="flex items-center">
              <div
                className="w-2 h-2 mr-1 rounded-sm"
                style={{ backgroundColor: hoveredSegment.color }}
              />
              <span className="font-medium">
                {hoveredSegment.label}
                <span className="text-gray-500 ml-1">
                  ({Math.round(hoveredSegment.value)}%)
                </span>
              </span>
            </div>
          </div>
        )}
      </div>

      {/* Right Legend - shown if showRightLegend is true or in single column */}
      {(showRightLegend || isSingleColumn) &&
        (processedRightLegend || (isSingleColumn && processedLeftLegend)) && (
          <div className="flex-none w-24 pl-4 text-xs">
            {(isSingleColumn
              ? [
                  ...(processedLeftLegend || []),
                  ...(processedRightLegend || []),
                ]
              : processedRightLegend
            ).map(({ color, label }, i) => (
              <div key={i} className="flex items-center mb-2">
                <div
                  className="w-3 h-3 mr-1 rounded-sm flex-shrink-0"
                  style={{ backgroundColor: color }}
                />
                <span className="whitespace-nowrap text-[11px]">{label}</span>
              </div>
            ))}
          </div>
        )}
    </div>
  );
};

function Analytics({
  platformName,
  dateRange,
  onDateRangeChange,
  platformData,
}) {
  const [isLoading, setIsLoading] = useState(true);
  const platformTabs = {
    facebook: ["Followers", "Impressions", "Shares", "Interactions"],
    instagram: ["Followers", "Impressions", "Reach", "Visits"],
    twitter: ["Followers", "Retweets", "Mentions", "Engagement"],
    youtube: ["Subscribers", "Views", "Like,Comments"],
    linkedin: ["Followers", "Impressions"],
    thread: ["Views"],
    pinterest: ["Impressions", "Engagement", "PinClicks"],
  };

  // Set activeTab to the first tab of the current platform
  const [activeTab, setActiveTab] = useState(
    platformTabs[platformName.toLowerCase()]?.[0] || "Followers"
  );

  // Update activeTab when platform changes
  useEffect(() => {
    setActiveTab(platformTabs[platformName.toLowerCase()]?.[0] || "Followers");
  }, [platformName]);

  // Reset loading state when platform or date range changes
  useEffect(() => {
    setIsLoading(true);
  }, [platformName, dateRange]);

  // Set loading to false when data is received
  useEffect(() => {
    if (platformData) {
      setIsLoading(false);
    }
  }, [platformData]);

  // Get stats from analytics data
  const getStatsForPlatform = (platform, tab, data) => {
    const platformLC = platform.toLowerCase();

    if (platformLC === "instagram") {
      const graphData = data?.graph1?.data?.graph_data || {};
      const graph1Metric = graphData?.graph_1_metric || {};

      switch (tab) {
        case "Followers":
          return [
            {
              label: "Followers",
              value: graphData?.followers_count || "0",
            },
            {
              label: "Followers in Range",
              value: graph1Metric?.followers_in_range || "0",
            },
            {
              label: "Posts Per Week",
              value: Number(graph1Metric?.posts_per_week || 0).toFixed(2),
            },
          ];
        case "Impressions":
          return [
            {
              label: "Impressions",
              value: graphData?.total_impressions_in_date_range || "0",
            },
            {
              label: "Likes Per Post",
              value: graphData?.graph_3_metric?.likes_per_post || "0",
            },
            {
              label: "Comments Per Post",
              value: graphData?.graph_3_metric?.comments_per_post || "0",
            },
          ];
        case "Reach":
          return [
            {
              label: "Reach",
              value: graphData?.total_reach_in_date_range || "0",
            },
            {
              label: "Likes Per Post",
              value: graphData?.graph_3_metric?.likes_per_post || "0",
            },
            {
              label: "Comments Per Post",
              value: graphData?.graph_3_metric?.comments_per_post || "0",
            },
          ];
        case "Visits":
          return [
            {
              label: "Profile Visits",
              value: graphData?.total_profile_visits_in_date_range || "0",
            },
            {
              label: "Posts Per Week",
              value: Number(
                graphData?.graph_1_metric?.posts_per_week || 0
              ).toFixed(2),
            },
            {
              label: "Comments Per Post",
              value: graphData?.graph_3_metric?.comments_per_post || "0",
            },
          ];
        default:
          return [
            {
              label: "Followers",
              value: graphData?.followers_count || "0",
            },
            {
              label: "Following",
              value: graphData?.following_count || "0",
            },
            {
              label: "Posts",
              value: graphData?.total_posts || "0",
            },
          ];
      }
    }

    if (platformLC === "facebook") {
      const graph1 = data?.graph1 || {};

      switch (tab) {
        case "Followers":
          return [
            { label: "Followers", value: graph1?.total_follows || "0" },
            { label: "Likes", value: graph1?.total_fans || "0" },
            { label: "Page Views", value: graph1?.total_views_total || "0" },
          ];
        case "Impressions":
          return [
            {
              label: "Impressions",
              value: graph1?.total_impressions || "0",
            },
            {
              label: "Likes",
              value: graph1?.total_fans || "0",
            },
            {
              label: "Page Views",
              value: graph1?.total_views_total || "0",
            },
          ];
        case "Shares":
          return [
            { label: "Shares", value: data?.graph2?.total_shares || "0" },
            {
              label: "Post",
              value: data?.graph2?.total_posts || "0",
            },
            {
              label: "Comments",
              value: data?.graph2?.total_comments || "0",
            },
          ];
        case "Interactions":
          return [
            {
              label: "Interactions",
              value: data?.graph2?.total_interactions || "0",
            },
            {
              label: "Posts",
              value: data?.graph2?.total_posts || "0",
            },
            {
              label: "Likes",
              value: data?.graph2?.total_likes || "0",
            },
          ];
        default:
          return [
            { label: "Followers", value: graph1?.total_follows || "0" },
            {
              label: "Likes",
              value: graph1?.total_fans || "0",
            },
            {
              label: "Page Views",
              value: graph1?.total_views_total || "0",
            },
          ];
      }
    }

    if (platformLC === "linkedin") {
      const graph1Data = data?.graph1?.data || {};

      switch (tab) {
        case "Followers":
          return [
            {
              label: "Followers",
              value: graph1Data?.followers?.total_followers || "0",
            },
            {
              label: "Impressions",
              value: graph1Data?.impressions?.total_impressions || "0",
            },
            {
              label: "Posts",
              value: graph1Data?.posts?.total || "0",
            },
          ];
        case "Impressions":
          return [
            {
              label: "Impressions",
              value: graph1Data?.impressions?.total_impressions || "0",
            },
            {
              label: "Followers",
              value: graph1Data?.followers?.total_followers || "0",
            },
            {
              label: "Posts",
              value: graph1Data?.posts?.total || "0",
            },
          ];
        default:
          return [
            { label: "Followers", value: data?.graph1?.total_follows || "0" },
            {
              label: "Likes",
              value: data?.graph1?.total_fans || "0",
            },
            {
              label: "Page Views",
              value: data?.graph1?.total_views_total || "0",
            },
          ];
      }
    }

    if (platformLC === "youtube") {
      const graph2Data = data?.graph2?.data || {};
      const totalData = graph2Data?.total || {};

      switch (tab) {
        case "Subscribers":
          return [
            {
              label: "Subscribers Gained",
              value: totalData?.subscribersGained || "0",
            },
            {
              label: "Subscribers Lost",
              value: totalData?.subscribersLost || "0",
            },
            {
              label: "Views",
              value: totalData?.views || "0",
            },
          ];
        case "Views":
          return [
            { label: "Views", value: totalData?.views || "0" },
            { label: "Likes", value: totalData?.likes || "0" },
            { label: "Shares", value: totalData?.shares || "0" },
          ];
        case "Like,Comments":
          return [
            { label: "Likes", value: totalData?.likes || "0" },
            { label: "Views", value: totalData?.views || "0" },
            { label: "Comments", value: totalData?.comments || "0" },
          ];
        default:
          return [
            { label: "Views", value: totalData?.views || "0" },
            { label: "Likes", value: totalData?.likes || "0" },
            { label: "Comments", value: totalData?.comments || "0" },
          ];
      }
    }

    if (platformLC === "thread") {
      const graph2Data = data?.graph2?.data || {};
      const totalData = graph2Data?.total || {};

      switch (tab) {
        case "Views":
          return [
            {
              label: "Views",
              value: data?.graph5?.data?.total_views || "0",
            },
          ];
          return [
            { label: "Likes", value: totalData?.likes || "0" },
            { label: "Views", value: totalData?.views || "0" },
            { label: "Comments", value: totalData?.comments || "0" },
          ];
        default:
          return [
            { label: "Views", value: totalData?.views || "0" },
            { label: "Likes", value: totalData?.likes || "0" },
            { label: "Comments", value: totalData?.comments || "0" },
          ];
      }
    }

    if (platformLC === "pinterest") {
      const graph2Data = data?.graph2?.data || {};
      const totalData = graph2Data?.total || {};

      switch (tab) {
        case "Impressions":
          return [
            {
              label: "Impressions",
              value: data?.graph2?.IMPRESSION || "0",
            },
            {
              label: "Pin Count",
              value: data?.graph1?.data?.pin_count || "0",
            },
            {
              label: "Followers",
              value: data?.graph1?.data?.follower_count || "0",
            },
          ];
        case "Engagement":
          return [
            {
              label: "Engagement",
              value: data?.graph2?.ENGAGEMENT || "0",
            },
            {
              label: "Pin Count",
              value: data?.graph1?.data?.pin_count || "0",
            },
            {
              label: "Followers",
              value: data?.graph1?.data?.follower_count || "0",
            },
          ];
        case "PinClicks":
          return [
            {
              label: "Pin Clicks",
              value: data?.graph2?.PIN_CLICK || "0",
            },
            {
              label: "Impressions",
              value: data?.graph2?.IMPRESSION || "0",
            },
            {
              label: "Followers",
              value: data?.graph1?.data?.follower_count || "0",
            },
          ];
          return [
            { label: "Likes", value: totalData?.likes || "0" },
            { label: "Views", value: totalData?.views || "0" },
            { label: "Comments", value: totalData?.comments || "0" },
          ];
        default:
          return [
            { label: "Views", value: totalData?.views || "0" },
            { label: "Likes", value: totalData?.likes || "0" },
            { label: "Comments", value: totalData?.comments || "0" },
          ];
      }
    }

    // Default case for any other platform
    return [
      { label: "Impressions", value: data?.graph1?.total_impressions || "0" },
      { label: "Following", value: "0" },
      { label: "Total Content", value: "0" },
    ];
  };

  // Get growth chart data based on platform
  const getGrowthChartData = (platform, tab, data) => {
    if (!data) return { chartData: [], metrics: [] };

    const platformLC = platform.toLowerCase();

    if (platformLC === "linkedin") {
      switch (tab) {
        case "Followers":
          // Convert LinkedIn follower data for chart display
          const followerData = data?.graph1?.data?.followers?.graph_data || [];
          const followerChartData = followerData.map((item) => {
            const date = Object.keys(item)[0];
            const value = item[date];
            return {
              date,
              followers: value,
            };
          });

          return {
            chartData: followerChartData,
            metrics: [
              {
                key: "followers",
                name: "Followers",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };

        case "Impressions":
          // Convert LinkedIn impressions data for chart display
          const impressionData =
            data?.graph1?.data?.impressions?.graph_data || [];
          const impressionChartData = impressionData.map((item) => {
            const date = Object.keys(item)[0];
            const value = item[date];
            return {
              date,
              impressions: value,
            };
          });

          return {
            chartData: impressionChartData,
            metrics: [
              {
                key: "impressions",
                name: "Impressions",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };

        default:
          return {
            chartData: [],
            metrics: [
              {
                key: "followers",
                name: "Followers",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
      }
    } else if (platformLC === "facebook") {
      switch (tab) {
        case "Followers":
          return {
            chartData: data?.graph1?.data || [],
            metrics: [
              {
                key: "follows",
                name: "Followers",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Impressions":
          return {
            chartData: data?.graph1?.data || [],
            metrics: [
              {
                key: "impressions",
                name: "Impressions",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Shares":
          return {
            chartData: data?.graph2?.data || [],
            metrics: [
              {
                key: "shares",
                name: "Shares",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Interactions":
          return {
            chartData: data?.graph2?.data || [],
            metrics: [
              {
                key: "interactions",
                name: "Interactions",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        default:
          return {
            chartData: data?.graph1?.data || [],
            metrics: [
              {
                key: "follows",
                name: "Followers",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
      }
    } else if (platformLC === "instagram") {
      switch (tab) {
        case "Followers":
          // Convert follower data for chart display
          const followerData =
            data?.graph1?.data?.graph_data?.followers_insights || {};
          const followerChartData = Object.entries(followerData).map(
            ([date, value]) => ({
              date,
              followers: value,
            })
          );

          return {
            chartData: followerChartData,
            metrics: [
              {
                key: "followers",
                name: "Followers",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };

        case "Impressions":
          const impressionData =
            data?.graph1?.data?.graph_data?.impressions_counts || {};
          const impressionChartData = Object.entries(impressionData).map(
            ([date, value]) => ({
              date,
              impressions: value, // Using "impressions" as the key that the chart will display
            })
          );

          return {
            chartData: impressionChartData,
            metrics: [
              {
                key: "impressions", // This key must match the property name in chartData objects
                name: "Impressions",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };

        case "Reach":
          const reachData = data?.graph1?.data?.graph_data?.reach_counts || {};
          const reachChartData = Object.entries(reachData).map(
            ([date, value]) => ({
              date,
              reach: value, // Using "reach" as the key that the chart will display
            })
          );

          return {
            chartData: reachChartData,
            metrics: [
              {
                key: "reach", // This key must match the property name in chartData objects
                name: "Reach",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };

        case "Visits":
          // Convert visits data for chart display
          const visitData =
            data?.graph1?.data?.graph_data?.profile_visits_counts || {};
          const visitChartData = Object.entries(visitData).map(
            ([date, value]) => ({
              date,
              visits: value, // Using "visits" as the key that the chart will display
            })
          );

          return {
            chartData: visitChartData,
            metrics: [
              {
                key: "visits", // This key must match the property name in chartData objects
                name: "Visits",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };

        default:
          // Default to post counts
          const postCounts = data?.graph1?.data?.graph_data?.post_counts || {};
          const chartData = Object.entries(postCounts).map(([date, value]) => ({
            date,
            posts: value,
          }));

          return {
            chartData,
            metrics: [
              {
                key: "posts",
                name: "Posts",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
      }
    } else if (platformLC === "linkedin" || platformLC === "linkedin") {
      switch (tab) {
        case "Followers":
          return {
            chartData: data?.graph1?.data?.followers?.graph_data || [],
            metrics: [
              {
                key: "total_followers",
                name: "Followers",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Retweets":
          return {
            chartData: data?.graph1?.data || [],
            metrics: [
              {
                key: "retweets",
                name: "Retweets",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Mentions":
          return {
            chartData: data?.graph1?.data || [],
            metrics: [
              {
                key: "mentions",
                name: "Mentions",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Engagement":
          return {
            chartData: data?.graph1?.data || [],
            metrics: [
              {
                key: "engagement",
                name: "Engagement",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        default:
          return {
            chartData: data?.graph1?.data || [],
            metrics: [
              {
                key: "impressions",
                name: "Impressions",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
      }
    } else if (platformLC === "youtube") {
      switch (tab) {
        case "Subscribers":
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "subscribersGained",
                name: "Subscribers Gained",
                color: "#563D39",
                gradientId: "fadeUv",
              },
              {
                key: "subscribersLost",
                name: "Subscribers Lost",
                color: "#7A564F",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Views":
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "views",
                name: "Views",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Like,Comments":
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "likes",
                name: "Likes",
                color: "#563D39",
                gradientId: "fadeUv",
              },
              {
                key: "comments",
                name: "Comments",
                color: "#7A564F",
                gradientId: "fadeUv",
              },
            ],
          };
        default:
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "views",
                name: "Views",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
      }
    } else if (platformLC === "thread") {
      switch (tab) {
        case "Views":
          return {
            chartData: data?.graph5?.data?.views || [],
            metrics: [
              {
                key: "value",
                name: "Views",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "likes",
                name: "Likes",
                color: "#563D39",
                gradientId: "fadeUv",
              },
              {
                key: "comments",
                name: "Comments",
                color: "#7A564F",
                gradientId: "fadeUv",
              },
            ],
          };
        default:
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "views",
                name: "Views",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
      }
    } else if (platformLC === "pinterest") {
      switch (tab) {
        case "Impressions":
          return {
            chartData: data?.graph2?.daily_data || [],
            metrics: [
              {
                key: "IMPRESSION",
                name: "Impressions",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "Engagement":
          return {
            chartData: data?.graph2?.daily_data || [],
            metrics: [
              {
                key: "ENGAGEMENT",
                name: "Engagement",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
        case "PinClicks":
          return {
            chartData: data?.graph2?.daily_data || [],
            metrics: [
              {
                key: "PIN_CLICK",
                name: "Pin Clicks",
                color: "#563D39",
                gradientId: "fadeUv",
              },
              {
                key: "OUTBOUND_CLICK",
                name: "Outbound Clicks",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "likes",
                name: "Likes",
                color: "#563D39",
                gradientId: "fadeUv",
              },
              {
                key: "comments",
                name: "Comments",
                color: "#7A564F",
                gradientId: "fadeUv",
              },
            ],
          };
        default:
          return {
            chartData: data?.graph2?.data?.analytics || [],
            metrics: [
              {
                key: "views",
                name: "Views",
                color: "#563D39",
                gradientId: "fadeUv",
              },
            ],
          };
      }
    } else {
      // Default case for any other platform
      return {
        chartData: data?.graph1?.data || [],
        metrics: [
          {
            key: "impressions",
            name: "Impressions",
            color: "#563D39",
            gradientId: "fadeUv",
          },
          {
            key: "follows",
            name: "Follows",
            color: "#9C6B56",
            gradientId: "fadePv",
          },
        ],
      };
    }
  };

  const shouldShowDonutCharts = (platform) => {
    const platformLC = platform.toLowerCase();
    return (
      platformLC === "instagram" ||
      platformLC === "linkedin" ||
      platformLC === "thread"
    );
  };

  const getDonutChartData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    if (platformLC === "instagram") {
      // Show Age, City, Gender, and Country for Instagram
      return {
        age: data?.graph2?.data?.age || [],
        city: data?.graph2?.data?.city || [],
        gender: data?.graph2?.data?.gender || [],
        country: data?.graph2?.data?.country || [],
      };
    } else if (platformLC === "linkedin") {
      // Show all other donut charts for LinkedIn
      return {
        industry: data?.graph2?.data?.industry || [],
        companySize: data?.graph2?.data?.company_size || [],
        jobTitle: data?.graph2?.data?.job_title || [],
        seniority: data?.graph2?.data?.seniority || [],
      };
    }
    return null;
  };

  const getCountryDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "linkedin":
        const countries =
          data?.graph2?.data?.followers?.graph_data?.country ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: countries.map((country, index) => ({
            color: colorPalette[index % colorPalette.length],
            value: country?.persantage_value,
          })),
          legend: countries.map((country, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: country?.country_name,
          })),
        };
        const instagramData = data?.graph2?.data || {};
        const contentTypes = [
          { type: "Image", value: instagramData?.image_posts || 0 },
          { type: "Video", value: instagramData?.video_posts || 0 },
          { type: "Carousel", value: instagramData?.carousel_posts || 0 },
          { type: "Reel", value: instagramData?.reel_posts || 0 },
        ].filter((item) => item.value > 0);

        return {
          data: contentTypes.map((type, index) => ({
            color: ["#603B31", "#A48179", "#BFA6A0", "#8C5E54"][index],
            value: type.value,
          })),
          legend: contentTypes.map((type, index) => ({
            color: ["#603B31", "#A48179", "#BFA6A0", "#8C5E54"][index],
            label: type.type,
          })),
        };

      default:
        return {
          data: [
            { color: "#603B31", value: 70 },
            { color: "#A48179", value: 30 },
          ],
          legend: [
            { color: "#603B31", label: "Image" },
            { color: "#A48179", label: "Carousel" },
          ],
        };
    }
  };

  const getseniorityDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "linkedin":
        const senioritys =
          data?.graph2?.data?.followers?.graph_data?.seniority ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: senioritys.map((seniority, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: seniority?.persantage_value,
          })),
          legend: senioritys.map((seniority, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: seniority?.seniority_name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };
  const getAreaDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "linkedin":
        const areas = data?.graph2?.data?.followers?.graph_data?.area ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: areas.map((area, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: area?.persantage_value,
          })),
          legend: areas.map((area, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: area?.area_name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getIndustryDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "linkedin":
        const industrys =
          data?.graph2?.data?.followers?.graph_data?.industry ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: industrys.map((industry, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: industry?.persantage_value,
          })),
          legend: industrys.map((industry, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: industry?.industry_name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };
  const getFunctionDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "linkedin":
        const functions =
          data?.graph2?.data?.followers?.graph_data?.function ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: functions.map((functionn, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: functionn?.persantage_value,
          })),
          legend: functions.map((functionn, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: functionn?.function_name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getCompanyDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "linkedin":
        const companys =
          data?.graph2?.data?.followers?.graph_data?.company_size ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: companys.map((company, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: company?.persantage_value,
          })),
          legend: companys.map((company, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: company?.function_name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getAgeDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "instagram":
        const ages = data?.graph2?.data?.age ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: ages.map((age, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: age?.persanatge,
          })),
          legend: ages.map((age, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: age?.name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getCityDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "instagram":
        const cities = data?.graph2?.data?.city ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        // Process cities to group those with 2% or less into "Other"
        const processedCities = [];
        let otherCitiesSum = 0;

        cities.forEach((city) => {
          if (city.persanatge > 2) {
            processedCities.push(city);
          } else {
            otherCitiesSum += city.persanatge;
          }
        });

        // Add "Other" category if there are cities to group
        if (otherCitiesSum > 0) {
          processedCities.push({
            name: "Other",
            persanatge: otherCitiesSum,
          });
        }

        return {
          data: processedCities.map((city, index) => ({
            color: colorPalette[index % colorPalette.length],
            value: city?.persanatge,
          })),
          legend: processedCities.map((city, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: city?.name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getCountryyDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "instagram":
        const countrys = data?.graph2?.data?.country ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: countrys.map((country, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: country?.persanatge,
          })),
          legend: countrys.map((country, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: country?.name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };
  const getGenderDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "instagram":
        const genders = data?.graph2?.data?.gender ?? [];

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: genders.map((gender, index) => ({
            color: colorPalette[index % colorPalette.length], // loop through colors if more countries than colors
            value: gender?.persanatge,
          })),
          legend: genders.map((gender, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: gender?.name,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getThreadCountryDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "thread":
        const countryObj = data?.graph1?.data?.[1]?.country || {};
        const countryArray = Object.entries(countryObj).map(
          ([code, count]) => ({
            code,
            count,
          })
        );

        countryArray.sort((a, b) => b.count - a.count);

        // Calculate total for percentage normalization
        const total = countryArray.reduce(
          (sum, country) => sum + country.count,
          0
        );

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: countryArray.map((country, index) => ({
            color: colorPalette[index % colorPalette.length],
            value: total > 0 ? (country.count / total) * 100 : 0,
          })),
          legend: countryArray.map((country, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: country.code,
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getThreadCityDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "thread":
        const cityObj = data?.graph2?.data?.[1]?.city || {};
        const cityArray = Object.entries(cityObj).map(([name, count]) => ({
          name,
          count,
        }));

        cityArray.sort((a, b) => b.count - a.count);

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        // Given the large number of cities, you might want to limit the display
        // Let's show top 9 cities and group the rest as "Others"
        const topCities = cityArray.slice(0, 9);
        const otherCities = cityArray.slice(9);

        let finalData = [...topCities];

        // If there are other cities, add an "Others" entry
        if (otherCities.length > 0) {
          const otherCount = otherCities.reduce(
            (sum, city) => sum + city.count,
            0
          );
          finalData.push({
            name: "Others",
            count: otherCount,
          });
        }

        return {
          data: finalData.map((city, index) => ({
            color: colorPalette[index % colorPalette.length],
            value: city.count,
          })),
          legend: finalData.map((city, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: city.name, // Corrected: using city.name instead of cityArray.name
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getThreadAgeDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "thread":
        const ageObj = data?.graph3?.data?.[1]?.age || {};
        const ageArray = Object.entries(ageObj).map(([range, count]) => ({
          range,
          count,
        }));

        // Sort by age range (assuming chronological order is desired)
        ageArray.sort((a, b) => {
          // Extract the first number from each range for sorting
          const aStart = parseInt(a.range.split("-")[0]);
          const bStart = parseInt(b.range.split("-")[0]);
          return aStart - bStart;
        });

        // Calculate total for percentage normalization
        const total = ageArray.reduce((sum, age) => sum + age.count, 0);

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
          "#C9B4AD", // light almond
          "#A96F64", // reddish clay
          "#D9C0B9", // light blush
          "#7A4B41", // dark terracotta
          "#E2D0CA", // soft ivory rose
        ];

        return {
          data: ageArray.map((age, index) => ({
            color: colorPalette[index % colorPalette.length],
            value: total > 0 ? (age.count / total) * 100 : 0,
          })),
          legend: ageArray.map((age, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: age.range, // Age range like "25-34"
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  const getThreadGenderDonutData = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "thread":
        const genderObj = data?.graph4?.data?.[1]?.gender || {};
        const genderArray = Object.entries(genderObj).map(([code, count]) => ({
          code,
          count,
        }));

        // Sort by count in descending order
        genderArray.sort((a, b) => b.count - a.count);

        // Calculate total for percentage normalization
        const total = genderArray.reduce(
          (sum, gender) => sum + gender.count,
          0
        );

        const colorPalette = [
          "#603B31", // deep brown
          "#D1AEA6", // pale rose
          "#A48179", // muted mauve
          "#BFA6A0", // dusty taupe
          "#8C5E54", // cocoa
        ];

        // Map gender codes to readable labels
        const genderLabels = {
          M: "Male",
          F: "Female",
          U: "Unspecified",
        };

        return {
          data: genderArray.map((gender, index) => ({
            color: colorPalette[index % colorPalette.length],
            value: total > 0 ? (gender.count / total) * 100 : 0,
          })),
          legend: genderArray.map((gender, index) => ({
            color: colorPalette[index % colorPalette.length],
            label: genderLabels[gender.code] || gender.code, // Convert code to readable label
          })),
        };
      default:
        return {
          data: [
            { color: "#603B31", value: 30 },
            { color: "#A48179", value: 70 },
          ],
          legend: [
            { color: "#603B31", label: "Organic (Views)" },
            { color: "#A48179", label: "Promoted (Views)" },
          ],
        };
    }
  };

  // Get metrics from analytics data - platform specific
  const getMetricsForPlatform = (platform, data) => {
    const platformLC = platform.toLowerCase();

    switch (platformLC) {
      case "facebook":
        return [
          { label: "Fans", value: data?.graph1?.total_fans || "0" },
          {
            label: "impressions",
            value: data?.graph1?.total_impressions || "0",
          },
          {
            label: "Views",
            value: data?.graph1?.total_views_total || "0",
          },
          {
            label: "Engagement",
            value: data?.graph2?.total_engagements || "0",
          },
          { label: "Comments", value: data?.graph2?.total_comments || "0" },
          {
            label: "Paid Impressions",
            value: data?.graph2?.total_paid_impressions || "0",
          },
        ];
      case "instagram":
        return [
          {
            label: "Following",
            value: data?.graph1?.data?.graph_data?.following_count || "0",
          },
          {
            label: "Posts",
            value: data?.graph1?.data?.graph_data?.total_posts || "0",
          },
          {
            label: "Post in Date Range",
            value:
              data?.graph1?.data?.graph_data?.total_posts_in_date_range || "0",
          },
          {
            label: "Reach in Date Range",
            value: data?.graph1?.data?.graph_data?.total_reach_in_date_range,
          },
          {
            label: "Saved in Date Range",
            value: data?.graph1?.data?.graph_data?.total_saved_in_date_range,
          },
          {
            label: "Shares in Date Range",
            value: data?.graph1?.data?.graph_data?.total_shares_in_date_range,
          },
        ];
      case "youtube":
        return [
          {
            label: "Likes",
            value: data?.graph2?.data?.total?.likes,
          },
          {
            label: "Shares",
            value: data?.graph2?.data?.total?.shares,
          },
          {
            label: "Comments",
            value: data?.graph2?.data?.total?.comments,
          },
          {
            label: "Avg. View Duration",
            value: data?.graph2?.data?.total?.averageViewDuration,
          },
          {
            label: "Est. Minutes Watched",
            value: data?.graph2?.data?.total?.estimatedMinutesWatched,
          },
          {
            label: "Total Views",
            value: data?.graph2?.data?.total?.views,
          },
        ];
      case "twitter":
      case "x":
        return [
          { label: "Followers", value: data?.metrics?.followers || "5.4k" },
          {
            label: "Daily Followers",
            value: data?.metrics?.dailyFollowers || "32",
          },
          {
            label: "Retweet Rate",
            value: data?.metrics?.retweetRate || "2.1%",
          },
          { label: "Following", value: data?.metrics?.following || "1.2k" },
          {
            label: "Tweets Per Day",
            value: data?.metrics?.tweetsPerDay || "8",
          },
          {
            label: "Likes Per Tweet",
            value: data?.metrics?.likesPerTweet || "45",
          },
        ];
      case "linkedin":
        return [
          {
            label: "Total Click Counts",
            value: data?.graph3?.data?.graph_data?.total_click_count,
          },
          {
            label: "Total Comments",
            value: data?.graph3?.data?.graph_data?.total_comment_count,
          },
          {
            label: "Total Engagement",
            value: data?.graph3?.data?.graph_data?.total_engagement,
          },
          {
            label: "Total Impressions",
            value: data?.graph3?.data?.graph_data?.total_impression_count,
          },
          {
            label: "Total Like Count",
            value: data?.graph3?.data?.graph_data?.total_like_count,
          },
          {
            label: "Total Share Count",
            value: data?.graph3?.data?.graph_data?.total_share_count,
          },
        ];
      case "pinterest":
        return [
          {
            label: "Pin Clicks",
            value: data?.graph2?.IMPRESSION,
          },
          {
            label: "Impressions",
            value: data?.graph2?.IMPRESSION || "0",
          },
          {
            label: "Followers",
            value: data?.graph1?.data?.follower_count || "0",
          },
          {
            label: "Pin Count",
            value: data?.graph1?.data?.pin_count || "0",
          },
          {
            label: "Monthly Views",
            value: data?.graph1?.data?.monthly_views || "0",
          },
          {
            label: "Following",
            value: data?.graph1?.data?.following_count || "0",
          },
        ];
      case "thread":
        return [
          {
            label: "Views",
            value: data?.graph5?.data?.total_views,
          },
        ];
      default:
        return [];
    }
  };

  // Get stats and data for the platform
  const stats = getStatsForPlatform(
    platformName.toLowerCase(),
    activeTab,
    platformData
  );

  useEffect(() => {
    console.log("PlatformData =========> ", platformData);
    console.log("PlatformName =========> ", platformName);
  });
  const growthChartConfig = useMemo(() => {
    return getGrowthChartData(platformName, activeTab, platformData);
  }, [platformName, activeTab, platformData]);
  const countryDonutConfig = getCountryDonutData(platformName, platformData);
  const areaDonutConfig = getAreaDonutData(platformName, platformData);
  const seniorityDonutConfig = getseniorityDonutData(
    platformName,
    platformData
  );
  const industryDonutConfig = getIndustryDonutData(platformName, platformData);
  const functionDonutConfig = getFunctionDonutData(platformName, platformData);
  const companyDonutConfig = getCompanyDonutData(platformName, platformData);
  const ageDonutConfig = getAgeDonutData(platformName, platformData);
  const countryyDonutConfig = getCountryyDonutData(platformName, platformData);
  const cityDonutConfig = getCityDonutData(platformName, platformData);
  const genderDonutConfig = getGenderDonutData(platformName, platformData);
  const threadCountryDonutConfig = getThreadCountryDonutData(
    platformName,
    platformData
  );
  const threadCityDonutConfig = getThreadCityDonutData(
    platformName,
    platformData
  );

  const threadAgeDonutData = getThreadAgeDonutData(platformName, platformData);

  const threadGenderDonutData = getThreadGenderDonutData(
    platformName,
    platformData
  );
  const metrics = getMetricsForPlatform(platformName, platformData);

  const handleDateRangeChange = (startDate, endDate) => {
    if (onDateRangeChange) {
      onDateRangeChange(startDate, endDate);
    }
  };

  return (
    <div className="rounded-lg p-2 sm:p-4 md:p-6 h-full w-full overflow-y-auto font-Ubuntu">
      <>
        {/* Header Section */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          <h1 className="text-lg sm:text-xl md:text-2xl font-medium text-[#373E3E]">
            {platformName}
          </h1>
        </div>

        {/* Tabs and Date Range Section */}
        <div className="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4 mb-4 sm:mb-6 border-b pb-4 sm:pb-5">
          <div className="flex flex-wrap bg-[#F7F6F5] border border-[#E0E0E0] rounded-[8px] w-full sm:w-auto overflow-x-auto">
            {platformTabs[platformName.toLowerCase()]?.map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`flex-1 min-w-[100px] px-3 sm:px-6 md:px-10 py-2 sm:py-3 rounded-[8px] text-xs sm:text-sm md:text-base whitespace-nowrap ${
                  activeTab === tab
                    ? "bg-[#563D39] opacity-80 text-[#FDFDFD]"
                    : "text-[#A9ABAD]"
                }`}
              >
                {tab}
              </button>
            ))}
          </div>

          {/* Date Range Selector */}
          <div className="w-full sm:w-auto">
            <DateRangeSelector
              onDateRangeChange={handleDateRangeChange}
              dateRange={dateRange}
            />
          </div>
        </div>

        {/* Stats Cards Section */}
        {isLoading ? (
          <div className="flex justify-center items-center">
            <div className="absolute top-0 ">
              <Loader />
            </div>
          </div>
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6 mb-6 sm:mb-8">
              {stats.map((stat) => (
                <StatCard key={stat.label} {...stat} />
              ))}
            </div>

            {/* Growth Chart Section */}
            <div className="mb-6 bg-white pt-4 sm:pt-5 pb-[2px] rounded-[12px] overflow-hidden">
              <h2 className="text-lg sm:text-xl md:text-2xl font-semibold text-[#563D39] mb-4 sm:mb-6 md:mb-10 ml-3 sm:ml-4">
                Growth
              </h2>

              <div className="w-full h-[200px] sm:h-[250px] md:h-[300px] lg:h-[350px] px-2 sm:px-4">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart
                    data={growthChartConfig.chartData}
                    margin={{ top: 10, right: 10, left: 10, bottom: 5 }}
                  >
                    <defs>
                      {growthChartConfig.metrics.map((metric) => (
                        <linearGradient
                          key={metric.gradientId}
                          id={metric.gradientId}
                          x1="0"
                          y1="0"
                          x2="0"
                          y2="1"
                        >
                          <stop
                            offset="0%"
                            stopColor={metric.color}
                            stopOpacity={1}
                          />
                          <stop
                            offset="100%"
                            stopColor="#FFFFFF"
                            stopOpacity={0}
                          />
                        </linearGradient>
                      ))}
                    </defs>

                    <CartesianGrid stroke="#ccc" strokeOpacity={0.3} />
                    <XAxis
                      dataKey="date"
                      axisLine={{ strokeWidth: 1 }}
                      tickLine={false}
                      tick={{ fontSize: 10 }}
                      interval="preserveStartEnd"
                      minTickGap={20}
                      tickFormatter={(date) => {
                        const formats = ["YYYY-MM-DD", "DD-MM-YYYY"];
                        let parsed = null;

                        for (let format of formats) {
                          const tryParsed = dayjs(date, format, true);
                          if (tryParsed.isValid()) {
                            parsed = tryParsed;
                            break;
                          }
                        }

                        return parsed ? parsed.format("D-M") : "";
                      }}
                    />
                    <YAxis
                      padding={{ bottom: 20 }}
                      axisLine={{ strokeWidth: 1 }}
                      tickLine={false}
                      tick={{ fontSize: 10 }}
                    />
                    <Tooltip
                      contentStyle={{
                        fontSize: "12px",
                        padding: "8px",
                        borderRadius: "8px",
                        border: "1px solid #E0E0E0",
                      }}
                    />
                    <Legend
                      wrapperStyle={{
                        paddingTop: "10px",
                        fontSize: "12px",
                      }}
                      verticalAlign="bottom"
                      height={36}
                    />

                    {growthChartConfig.metrics.map((metric) => (
                      <Area
                        key={metric.key}
                        type="monotone"
                        dataKey={metric.key}
                        name={metric.name}
                        stroke={metric.color}
                        fill={`url(#${metric.gradientId})`}
                        strokeWidth={2}
                      />
                    ))}
                  </AreaChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Detailed Metrics Section */}
            <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-6 gap-2 sm:gap-3 md:gap-4 bg-white shadow-sm py-3 sm:py-4 md:py-6 rounded-[8px] mb-6 sm:mb-8 divide-x divide-[#E5E7EB] overflow-x-auto">
              {metrics.map((metric, index) => (
                <div
                  key={index}
                  className="flex flex-col justify-center items-center px-2 sm:px-3 md:px-4 min-w-[120px]"
                >
                  <p className="text-xs sm:text-sm md:text-base text-[#373E3E] font-normal text-center">
                    {metric.label}
                  </p>
                  <p className="text-sm sm:text-base md:text-lg lg:text-xl font-bold mt-1">
                    {metric.value}
                  </p>
                </div>
              ))}
            </div>
          </>
        )}

        {/* Donut Charts Section */}
        {shouldShowDonutCharts(platformName) && (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
            {/* ... existing donut charts code with responsive classes ... */}
          </div>
        )}
      </>
    </div>
  );
}

export default Analytics;
