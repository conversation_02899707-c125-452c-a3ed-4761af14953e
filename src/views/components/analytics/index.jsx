import React, { useState, useEffect } from "react";
import Sidebar from "./Sidebar";
import Analytics from "./Analytics.jsx";
import ConnectPlatform from "./ConnectPlatform";
import moment from "moment";
import NoData from "./NoData.jsx";
import Loader from "../../../helpers/UI/Loader.jsx";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";
import Spinner from "../../../helpers/UI/Spinner.jsx";

const Index = () => {
  const [activeChannel, setActiveChannel] = useState(null);
  const [selectedPlatformName, setSelectedPlatformName] = useState("");
  const [platformData, setPlatformData] = useState(null);
  const [isPlatformConnected, setIsPlatformConnected] = useState(true);
  const [isDataAvailable, setIsDataAvailable] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const [sidebarReady, setSidebarReady] = useState(false);
  const [channelsLoaded, setChannelsLoaded] = useState(false);
  const { selectedBrand } = useBrand();

  const [dateRange, setDateRange] = useState({
    startDate: moment().subtract(6, "days").format("YYYY-MM-DD"),
    endDate: moment().format("YYYY-MM-DD"),
  });

  const handlePlatformChange = (platform, name, isConnected) => {
    setIsLoading(true);
    setPlatformData(null);
    setIsDataAvailable(true);
    // Update states
    setActiveChannel(platform);
    setSelectedPlatformName(name);
    setIsPlatformConnected(isConnected);
  };

  const handlePlatformDataChange = (data) => {
    // First determine if the data is available before updating any state
    const hasData = !(
      data === null ||
      activeChannel === "vimeo" ||
      Object.values(data).every(
        (val) =>
          !val ||
          (Array.isArray(val) && val.length === 0) ||
          (typeof val === "object" && Object.keys(val).length === 0)
      )
    );

    // Update all states together at once
    setPlatformData(data);
    setIsDataAvailable(hasData);

    setIsLoading(false);
  };

  const handleDateRangeChange = (startDate, endDate) => {
    setDateRange({
      startDate: moment(startDate).format("YYYY-MM-DD"),
      endDate: moment(endDate).format("YYYY-MM-DD"),
    });
  };

  const handleSidebarChannelsLoaded = () => {
    setChannelsLoaded(true);
  };

  const handleSidebarMount = () => {
    setSidebarReady(true);
  };

  // Check if we should show the main content
  const shouldShowMainContent = sidebarReady && channelsLoaded && activeChannel;

  // Special handling for Vimeo
  useEffect(() => {
    if (activeChannel === "vimeo" && isPlatformConnected) {
      setPlatformData({});
      setIsDataAvailable(false);
      setIsLoading(false);
    }
  }, [activeChannel, selectedBrand, isPlatformConnected]);

  useEffect(() => {
    // Reset all relevant states when brand changes
    setActiveChannel(null);
    setChannelsLoaded(false);
    setSidebarReady(false);
  }, [selectedBrand]);

  return (
    <div className="flex flex-col md:flex-row h-[calc(100vh-120px)] bg-[#F9F9F9] p-2 sm:p-4 md:p-6 mt-[10px] ms-[10px] sm:ms-[20px] md:ms-[40px] me-[10px] sm:me-[20px] md:me-[40px] relative">
      {/* Sidebar */}
      <div className="w-full md:w-auto md:block flex-shrink-0 transition-all duration-300 ease-in-out top-0 mb-4 md:mb-0">
        <Sidebar
          key={selectedBrand?.id}
          activeChannel={activeChannel}
          setActiveChannel={setActiveChannel}
          onPlatformChange={handlePlatformChange}
          onPlatformDataChange={handlePlatformDataChange}
          dateRange={dateRange}
          onMount={handleSidebarMount}
          onChannelsLoaded={handleSidebarChannelsLoaded}
        />
      </div>

      <div className="flex-grow flex flex-col md:ml-4">
        {!sidebarReady || !channelsLoaded ? (
          <div className="flex justify-center items-center h-full">
            <div className="flex flex-col items-center">
              <Spinner />
            </div>
          </div>
        ) : !activeChannel ? (
          <div className="flex justify-center items-center h-full">
            <span className="text-gray-500">Initializing platform data...</span>
          </div>
        ) : isLoading ? (
          <div className="flex justify-center items-center h-full">
            <Spinner />
          </div>
        ) : !isPlatformConnected ? (
          <div className="w-full">
            <ConnectPlatform platformId={activeChannel} />
          </div>
        ) : platformData === null ? (
          <div className="flex justify-center items-center h-full">
            <Spinner />
          </div>
        ) : !isDataAvailable ? (
          <div className="w-full">
            <NoData platformName={selectedPlatformName} />
          </div>
        ) : (
          <Analytics
            platformName={selectedPlatformName}
            dateRange={dateRange}
            onDateRangeChange={handleDateRangeChange}
            platformData={platformData}
          />
        )}
      </div>
    </div>
  );
};

export default Index;
