import React, { useEffect, useState } from "react";
import Icon_01 from "../../../assets/images/Analytics/icon_01.svg";
import Icon_02 from "../../../assets/images/Analytics/icon_02.svg";
import Icon_03 from "../../../assets/images/Analytics/icon_03.svg";
import Icon_04 from "../../../assets/images/Analytics/icon_04.svg";
import Icon_05 from "../../../assets/images/Analytics/icon_05.svg";
import Icon_06 from "../../../assets/images/Analytics/icon_06.svg";

const RotatingAvatars = () => {
  // Avatar images (using placeholder images with different colors matching the design)
  const avatars = [
    {
      icon: Icon_01,
      bgColor: "#F99B9D",
      position: "top",
    },
    {
      icon: Icon_02,
      bgColor: "#A5E1AD",
      position: "left",
    },
    {
      icon: Icon_03,
      bgColor: "#A5D8E0",
      position: "right",
    },
    {
      icon: Icon_04,
      bgColor: "#C5B6E0",
      position: "bottom",
    },
  ];

  const innerAvatars = [
    {
      icon: Icon_05,
      position: "left",
    },
    {
      icon: Icon_06,
      position: "right",
    },
  ];

  const [outerRotation, setOuterRotation] = useState(0);
  const [innerRotation, setInnerRotation] = useState(0);
  const [outerRadius, setOuterRadius] = useState(140);
  const [innerRadius, setInnerRadius] = useState(70);

  useEffect(() => {
    const updateRadius = () => {
      if (window.innerWidth <= 640) {
        setOuterRadius(100);
        setInnerRadius(50);
      } else if (window.innerWidth <= 768) {
        setOuterRadius(80);
        setInnerRadius(40);
      } else if (window.innerWidth <= 1440) {
        setOuterRadius(90);
        setInnerRadius(35);
      } else {
        setOuterRadius(140);
        setInnerRadius(70);
      }
    };

    // Set initial radius
    updateRadius();

    // Add resize listener
    window.addEventListener("resize", updateRadius);
    return () => window.removeEventListener("resize", updateRadius);
  }, []);

  useEffect(() => {
    const rotateInterval = setInterval(() => {
      setOuterRotation((prev) => (prev + 0.2) % 360); // Slow, smooth rotation clockwise for outer circle
      setInnerRotation((prev) => (prev - 0.3) % 360); // Slightly faster, counterclockwise for inner circle
    }, 16); // ~60fps for smooth animation

    return () => clearInterval(rotateInterval);
  }, []);

  return (
    <div className="flex justify-center items-center w-full h-96">
      <div className="relative w-full max-w-md h-full flex justify-center items-center">
        {/* Outer circular axis */}
        <div
          className="absolute rounded-full border border-gray-200"
          style={{ width: outerRadius * 2, height: outerRadius * 2 }}
        />

        {/* Inner circular axis */}
        <div
          className="absolute rounded-full border border-gray-200"
          style={{ width: innerRadius * 2, height: innerRadius * 2 }}
        />

        {/* Outer Rotating Avatars */}
        <div
          className="absolute"
          style={{
            transform: `rotate(${outerRotation}deg)`,
            transition: "transform 0.01s linear",
            willChange: "transform",
            width: outerRadius * 2,
            height: outerRadius * 2,
          }}
        >
          {avatars.map((avatar, index) => {
            const angle = index * 90;
            const angleInRadians = (angle * Math.PI) / 180;

            // Position at exact edge of circle
            const x = Math.cos(angleInRadians) * outerRadius;
            const y = Math.sin(angleInRadians) * outerRadius;

            return (
              <div
                key={`outer-${index}`}
                className="absolute"
                style={{
                  transform: `rotate(-${outerRotation}deg)`,
                  width: "64px",
                  height: "64px",
                  marginLeft: "-32px", // Half the width
                  marginTop: "-32px", // Half the height
                  left: `${outerRadius + x}px`,
                  top: `${outerRadius + y}px`,
                }}
              >
                <div className="w-full h-full rounded-full flex justify-center items-center overflow-hidden">
                  <img src={avatar.icon} alt={`Avatar ${index}`} />
                </div>
              </div>
            );
          })}
        </div>

        {/* Inner Rotating Avatars */}
        <div
          className="absolute"
          style={{
            transform: `rotate(${innerRotation}deg)`,
            transition: "transform 0.01s linear",
            willChange: "transform",
            zIndex: 5,
            width: innerRadius * 2,
            height: innerRadius * 2,
          }}
        >
          {innerAvatars.map((avatar, index) => {
            const angle = index * 180;
            const angleInRadians = (angle * Math.PI) / 180;

            const x = Math.cos(angleInRadians) * innerRadius;
            const y = Math.sin(angleInRadians) * innerRadius;

            return (
              <div
                key={`inner-${index}`}
                className="absolute"
                style={{
                  width: "40px",
                  height: "40px",
                  marginLeft: "-20px",
                  marginTop: "-20px",
                  left: `${innerRadius + x}px`,
                  top: `${innerRadius + y}px`,
                  transform: `rotate(${-innerRotation - angle}deg)`,
                }}
              >
                <div
                  className="w-full h-full rounded-full flex justify-center items-center overflow-hidden"
                  style={{
                    transform: `rotate(${innerRotation + angle}deg)`,
                  }}
                >
                  <img src={avatar.icon} alt={`Inner Avatar ${index}`} />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default RotatingAvatars;
