import React from "react";
import noDataFound from "../../../assets/images/Analytics/DataNotFound.svg";

const NoData = ({ platformName }) => {
  return (
    <div className="bg-white min-h-[85vh] w-full p-4 sm:p-6 font-Ubuntu rounded-[12px] ms-[20px] flex items-center justify-center">
      <div className="flex flex-col items-center justify-center p-8 text-center">
        <div className="mb-4 relative">
          <img src={noDataFound} alt="No data" className="w-80 h-80" />
        </div>

        <h3 className="text-[26px] font-medium text-[#563D39] mb-1">
          No Data Available
        </h3>

        <p className="text-[20px] font-normal text-[#563D39]">
          Nothing has been added yet.
        </p>
      </div>
    </div>
  );
};

export default NoData;
