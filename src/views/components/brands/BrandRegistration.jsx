import React, { useContext, useRef, useState } from "react";
import { CustomTextField } from "../custom/CustomTextField";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import { IntlContext } from "../../../App";
import Upload from "../../../assets/images/svg_icon/UploadImage.svg";
import { setApiMessage } from "../../../helpers/context/toaster";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";

const BrandRegistration = ({ GetBrands, onSuccess, setActiveView }) => {
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState(null);
  const subscriptionId = localStorage.getItem("subscriptionId");

  // Add this hook to refresh brands in navbar
  const { setBrands } = useBrand();

  const initialValues = {
    brandName: "",
    email: "",
    domain: "",
    selectedFile: null,
  };

  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const validationSchema = Yup.object().shape({
    brandName: Yup.string().required("Brand Name is required"),
    email: Yup.string()
      .email("Please enter a valid email address.")
      .required("Email is required"),
  });

  const fileInputRef = useRef(null);

  const handleDivClick = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);

      // Create a preview URL for the image
      const fileReader = new FileReader();
      fileReader.onload = () => {
        setPreviewUrl(fileReader.result);
      };
      fileReader.readAsDataURL(file);
    }
  };

  const handleRemoveLogo = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // Add function to refresh navbar brands
  const refreshNavbarBrands = async () => {
    try {
      const response = await apiInstance.get(URL.GET_BRANDS);
      const fetchedBrands = response.data.data || [];
      setBrands(fetchedBrands);
    } catch (error) {
      console.error("Error refreshing navbar brands:", error);
    }
  };

  const handleSubmit = async (values, { setSubmitting, resetForm }) => {
    setSubmitting(true);
    try {
      const formData = new FormData();
      formData.append("name", values.brandName);
      formData.append("email", values.email);
      formData.append("domain", values.domain);
      if (selectedFile) {
        formData.append("logo_file", selectedFile);
      }

      const response = await apiInstance.post(URL.REGISTER_BRAND, formData, {
        headers: {
          subscription: subscriptionId,
          "Content-Type": "multipart/form-data",
        },
      });

      // Check if response status is successful
      if (response.status === 200 || response.status === 201) {
        setApiMessage("success", "Brand registered successfully");
        await GetBrands();
        setActiveView("default");
        resetForm();
        setSelectedFile(null);
        setPreviewUrl(null);
        refreshNavbarBrands();
        onSuccess();

        if (fileInputRef.current) {
          fileInputRef.current.value = "";
        }
      } else {
        console.error("Registration failed:", response.data);
      }
    } catch (error) {
      console.error("Error:", error?.response?.data || error.message);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="max-w-[1920px] mx-auto bg-white border-[1px] border-[#E0E0E0] rounded-[8px] mt-8 min-h-screen overflow-y-auto">
      <div className="bg-[#F5F2F1] p-6 rounded-tr-md rounded-tl-md mb-8">
        <h1 className="text-[28px] font-bold text-[#563D39] mb-2">
          {localesData?.USER_WEB?.BRANDS?.BRAND_REGISTRATION ||
            "Brand Registration"}
        </h1>
        <p className="text-base font-normal pr-[100px] xl:pr-[200px] text-[#5B6871] leading-[24px]">
          {localesData?.USER_WEB?.BRANDS?.BRANDFORM_TEXT}
        </p>
      </div>

      <Formik
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
      >
        {({ isSubmitting }) => (
          <Form>
            <div className="flex flex-col items-start gap-1 mb-8">
              <div className="w-[135px] h-[135px] border-2 border-dashed ms-[40px] bg-[#F8F8F8] border-gray-400 flex items-center justify-center rounded-[16px] mb-2 relative">
                {previewUrl ? (
                  <div className="relative w-full h-full group">
                    <img
                      src={previewUrl}
                      alt="Brand Logo"
                      className="w-full h-full object-cover rounded-[16px]"
                    />
                    <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-0 hover:bg-opacity-30 transition-all">
                      <div className="hidden group-hover:flex flex-col gap-2">
                        <label
                          htmlFor="brand-logo-upload"
                          className="bg-white text-[#563D39] px-2 py-1 rounded cursor-pointer text-xs hover:bg-[#F5F2F1]"
                        >
                          {localesData?.USER_WEB?.BRANDS?.CHANGE || "Change"}
                        </label>
                        <button
                          type="button"
                          onClick={handleRemoveLogo}
                          className="bg-red-50 text-red-500 px-2 py-1 rounded text-xs hover:bg-red-100"
                        >
                          {localesData?.USER_WEB?.BRANDS?.REMOVE || "Remove"}
                        </button>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center text-center gap-2">
                    <img src={Upload} alt="Upload Logo" />
                  </div>
                )}

                <input
                  type="file"
                  className="absolute inset-0 opacity-0 cursor-pointer w-full h-full"
                  id="brand-logo-upload"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept="image/*"
                />
              </div>

              <span className="text-xs text-gray-600 ms-[40px]">
                {previewUrl
                  ? "Current logo"
                  : localesData?.USER_WEB?.BRANDS?.UPLOAD_YOUR_LOGO ||
                    "Upload your logo"}
              </span>

              {previewUrl && (
                <div className="flex items-center gap-2 ms-[40px] mt-1">
                  <button
                    type="button"
                    onClick={handleRemoveLogo}
                    className="text-xs text-red-500 cursor-pointer underline"
                  >
                    {localesData?.USER_WEB?.BRANDS?.REMOVE || "Remove"}
                  </button>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 max-w-4xl gap-4 mb-4 mx-[40px]">
              <div className="relative">
                <fieldset className="border border-gray-300 rounded-md px-3 pt-2 pb-3">
                  <legend className="text-sm px-1 text-gray-700">
                    <span className="px-1">
                      {localesData?.USER_WEB?.BRANDS?.BRAND_NAME ||
                        "Brand Name"}
                    </span>
                  </legend>
                  <Field
                    type="text"
                    name="brandName"
                    className="w-full border-none p-0 focus:ring-0"
                    placeholder=""
                  />
                </fieldset>
                <ErrorMessage
                  name="brandName"
                  component="p"
                  className="text-red-500 text-xs mt-1.5"
                />
              </div>

              <div className="relative">
                <fieldset className="border border-gray-300 rounded-md px-3 pt-2 pb-3">
                  <legend className="text-sm px-1 text-gray-700">
                    <span className="px-1">
                      {localesData?.USER_WEB?.BRANDS?.EMAIL || "Email"}
                    </span>
                  </legend>
                  <Field
                    type="email"
                    name="email"
                    className="w-full border-none p-0 focus:ring-0"
                    placeholder=""
                  />
                </fieldset>
                <ErrorMessage
                  name="email"
                  component="p"
                  className="text-red-500 text-xs mt-1.5"
                />
              </div>
            </div>

            <div className="mb-6 max-w-[440px] mx-[40px]">
              <fieldset className="border border-gray-300 rounded-md px-3 pt-2 pb-3">
                <legend className="text-sm px-1 text-gray-700">
                  <span className="px-1">
                    {localesData?.USER_WEB?.BRANDS?.OFFICIAL_DOMAIN ||
                      "Official Domain"}
                  </span>
                </legend>
                <Field
                  type="text"
                  name="domain"
                  className="w-full border-none p-0 focus:ring-0"
                  placeholder=""
                />
              </fieldset>
            </div>

            <div className="flex justify-center items-center mt-[70px] pb-8 pt-4">
              <button
                type="submit"
                disabled={isSubmitting}
                className="bg-[#563D39] md:w-[280px] w-[calc(100%-80px)] h-[40px] hover:bg-[#3f2f2b] text-white py-2 px-4 rounded-[10px] font-medium transition flex justify-center items-center sticky bottom-4 z-10 shadow-lg"
              >
                {isSubmitting ? "Registering..." : "Register Brand"}
              </button>
            </div>
          </Form>
        )}
      </Formik>
    </div>
  );
};

export default BrandRegistration;
