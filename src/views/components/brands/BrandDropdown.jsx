import React, { useState, useEffect, useRef } from "react";
import { URL } from "../../../helpers/constant/Url";
import { useBrand } from "../../../helpers/context/BrandContext.jsx";

// Platform logos imports (match the ones in PaginatedBrandList)
import dummyProfile from "../../../assets/images/dummtprofile.png";

const BrandDropdown = ({ currentItems, onSelect, platformConfig }) => {
  // Flag to track if dropdown is visible
  const [isVisible, setIsVisible] = useState(false);
  const observer = useRef(null);
  const dropdownRef = useRef(null);
  const { selectedBrand } = useBrand();

  // Initialize Intersection Observer when component mounts
  useEffect(() => {
    observer.current = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setIsVisible(true);
          // Disconnect once visible
          observer.current.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (dropdownRef.current) {
      observer.current.observe(dropdownRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, []);

  // LazyImage component for handling image loading
  const LazyImage = ({ src, alt, className, fallback }) => {
    const [imageSrc, setImageSrc] = useState(null);
    const imgRef = useRef(null);
    const imageObserver = useRef(null);

    useEffect(() => {
      if (!isVisible) return;

      imageObserver.current = new IntersectionObserver(
        (entries) => {
          if (entries[0].isIntersecting) {
            setImageSrc(src);
            imageObserver.current.disconnect();
          }
        },
        { threshold: 0.1 }
      );

      if (imgRef.current) {
        imageObserver.current.observe(imgRef.current);
      }

      return () => {
        if (imageObserver.current) {
          imageObserver.current.disconnect();
        }
      };
    }, [src, isVisible]);

    return (
      <img
        ref={imgRef}
        src={imageSrc || fallback}
        alt={alt}
        className={className}
        loading="lazy"
      />
    );
  };

  const SocialIcon = ({ platform, index }) => {
    const config = platformConfig[platform];
    return config ? (
      <span
        className={`inline-flex items-center justify-center w-8 h-8 rounded-full p-1 ${
          index !== 0 ? "-ml-[14px]" : ""
        }`}
        style={{ zIndex: 10 + index }}
      >
        <LazyImage
          src={config.icon}
          alt={platform}
          className="w-8 h-8"
          fallback="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='%23cccccc' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z'/%3E%3C/svg%3E"
        />
      </span>
    ) : null;
  };

  return (
    <ul
      ref={dropdownRef}
      className="p-2 max-h-[300px] overflow-y-auto w-full sm:min-w-[300px] max-w-[100%]"
    >
      {currentItems.map((brand, index) => (
        <li
          key={brand.id || index}
          className={`bg-white border-gray-200 p-3 flex items-start sm:items-center gap-4 flex-wrap border hover:shadow-md transition cursor-pointer w-full
            ${
              selectedBrand?.id === brand.id
                ? "border-[#563D39]"
                : "border-gray-200"
            }
            ${index === 0 ? "rounded-t-lg" : ""}
            ${index === currentItems.length - 1 ? "rounded-b-lg" : ""}`}
          onClick={() => onSelect(brand)}
        >
          <LazyImage
            src={brand.logo ? `${URL.SOCKET_URL}${brand.logo}` : null}
            alt="Brand Logo"
            className="h-10 w-10 min-w-[2.5rem] rounded-full border object-cover"
            fallback={dummyProfile}
          />
          <div className="flex-1 min-w-0">
            <h3 className="text-base font-medium text-gray-900 truncate">
              {brand.name}
            </h3>
            <p className="text-sm text-gray-500 mb-1 truncate">{brand.email}</p>
            <div className="flex flex-wrap gap-1">
              {(brand.social_links || [])
                .filter((link) => link.user_status)
                .map((link, i) => (
                  <SocialIcon key={i} platform={link.platform} index={i} />
                ))}
            </div>
          </div>
        </li>
      ))}
    </ul>
  );
};

export default BrandDropdown;
