import React, { useState, useEffect } from "react";
import siteConstant from "../../../helpers/constant/siteConstant";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import { setApiMessage } from "../../../helpers/context/toaster";
import LoadingSpinner from "../../../helpers/UI/LoadingSpinner";

function Telegram_mobile({ onClose, onSuccess }) {
  const [selectedCountry, setSelectedCountry] = useState("India");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [hashdata, setHashdata] = useState(null);
  const [showOtpPopup, setShowOtpPopup] = useState(false);
  const [otpValues, setOtpValues] = useState(["", "", "", "", ""]);
  const [resendTimer, setResendTimer] = useState(29);
  const [isVerifyingOtp, setIsVerifyingOtp] = useState(false);
  const BrandId = localStorage.getItem("BrandId");

  // Timer effect for resend countdown
  useEffect(() => {
    let interval = null;
    if (showOtpPopup && resendTimer > 0) {
      interval = setInterval(() => {
        setResendTimer((timer) => timer - 1);
      }, 1000);
    } else if (resendTimer === 0) {
      clearInterval(interval);
    }
    return () => clearInterval(interval);
  }, [showOtpPopup, resendTimer]);

  // Country data with codes and flags
  const countryData = {
    Afghanistan: { code: "+93", flag: "🇦🇫" },
    Albania: { code: "+355", flag: "🇦🇱" },
    Algeria: { code: "+213", flag: "🇩🇿" },
    Argentina: { code: "+54", flag: "🇦🇷" },
    Australia: { code: "+61", flag: "🇦🇺" },
    Austria: { code: "+43", flag: "🇦🇹" },
    Bangladesh: { code: "+880", flag: "🇧🇩" },
    Belgium: { code: "+32", flag: "🇧🇪" },
    Brazil: { code: "+55", flag: "🇧🇷" },
    Canada: { code: "+1", flag: "🇨🇦" },
    China: { code: "+86", flag: "🇨🇳" },
    Denmark: { code: "+45", flag: "🇩🇰" },
    Egypt: { code: "+20", flag: "🇪🇬" },
    Finland: { code: "+358", flag: "🇫🇮" },
    France: { code: "+33", flag: "🇫🇷" },
    Germany: { code: "+49", flag: "🇩🇪" },
    Greece: { code: "+30", flag: "🇬🇷" },
    India: { code: "+91", flag: "🇮🇳" },
    Indonesia: { code: "+62", flag: "🇮🇩" },
    Iran: { code: "+98", flag: "🇮🇷" },
    Iraq: { code: "+964", flag: "🇮🇶" },
    Ireland: { code: "+353", flag: "🇮🇪" },
    Israel: { code: "+972", flag: "🇮🇱" },
    Italy: { code: "+39", flag: "🇮🇹" },
    Japan: { code: "+81", flag: "🇯🇵" },
    Jordan: { code: "+962", flag: "🇯🇴" },
    Kenya: { code: "+254", flag: "🇰🇪" },
    Kuwait: { code: "+965", flag: "��" },
    Malaysia: { code: "+60", flag: "🇲🇾" },
    Mexico: { code: "+52", flag: "🇲🇽" },
    Netherlands: { code: "+31", flag: "🇳🇱" },
    "New Zealand": { code: "+64", flag: "🇳🇿" },
    Nigeria: { code: "+234", flag: "🇳🇬" },
    Norway: { code: "+47", flag: "🇳�" },
    Pakistan: { code: "+92", flag: "🇵🇰" },
    Philippines: { code: "+63", flag: "🇵🇭" },
    Poland: { code: "+48", flag: "🇵🇱" },
    Portugal: { code: "+351", flag: "🇵🇹" },
    Qatar: { code: "+974", flag: "🇶🇦" },
    Russia: { code: "+7", flag: "🇷🇺" },
    "Saudi Arabia": { code: "+966", flag: "🇸🇦" },
    Singapore: { code: "+65", flag: "🇸🇬" },
    "South Africa": { code: "+27", flag: "�🇦" },
    "South Korea": { code: "+82", flag: "🇰🇷" },
    Spain: { code: "+34", flag: "🇪🇸" },
    "Sri Lanka": { code: "+94", flag: "🇱🇰" },
    Sweden: { code: "+46", flag: "🇸🇪" },
    Switzerland: { code: "+41", flag: "🇨🇭" },
    Thailand: { code: "+66", flag: "🇹🇭" },
    Turkey: { code: "+90", flag: "🇹🇷" },
    UAE: { code: "+971", flag: "🇦🇪" },
    UK: { code: "+44", flag: "🇬🇧" },
    Ukraine: { code: "+380", flag: "🇺🇦" },
    USA: { code: "+1", flag: "🇺🇸" },
    Vietnam: { code: "+84", flag: "🇻🇳" },
  };

  // Handle country change
  const handleCountryChange = (e) => {
    setSelectedCountry(e.target.value);
    setPhoneNumber(""); // Clear phone number when country changes
  };

  // Handle phone number input - only allow numbers
  const handlePhoneNumberChange = (e) => {
    const value = e.target.value;
    const numbersOnly = value.replace(/[^0-9]/g, ""); // Remove non-numeric characters
    setPhoneNumber(numbersOnly);
  };

  // Handle Send OTP button click
  const handleSendOTP = async () => {
    if (!phoneNumber.trim()) {
      setApiMessage("error", "Please enter a phone number");
      return;
    }

    setIsLoading(true);
    try {
      const formData = new FormData();
      const fullPhoneNumber = `${countryData[selectedCountry].code}${phoneNumber}`;
      formData.append("phone_number", fullPhoneNumber);

      const response = await apiInstance.post(URL.TELEGRAM, formData, {
        headers: {
          brand: BrandId,
        },
      });

      if (response?.data?.status) {
        setHashdata(response?.data.phone_code_hash);
        setApiMessage(
          "success",
          response?.data?.message || "OTP sent successfully!"
        );
        // Show OTP popup only when API returns success
        setShowOtpPopup(true);
        setResendTimer(29); // Reset timer
      } else {
        setApiMessage("error", response?.data?.message || "Failed to send OTP");
      }
    } catch (error) {
      console.error("Telegram API Error:", error);
      setApiMessage(
        "error",
        error?.message || "An error occurred while sending OTP"
      );
    } finally {
      setIsLoading(false);
    }
  };

  // Handle OTP input change
  const handleOtpChange = (index, value) => {
    if (value.length <= 1 && /^\d*$/.test(value)) {
      const newOtpValues = [...otpValues];
      newOtpValues[index] = value;
      setOtpValues(newOtpValues);

      // Auto-focus next input
      if (value && index < 4) {
        const nextInput = document.getElementById(`otp-${index + 1}`);
        if (nextInput) nextInput.focus();
      }
    }
  };

  // Handle OTP input keydown
  const handleOtpKeyDown = (index, e) => {
    if (e.key === "Backspace" && !otpValues[index] && index > 0) {
      const prevInput = document.getElementById(`otp-${index - 1}`);
      if (prevInput) prevInput.focus();
    }
  };

  // Handle OTP verification
  const handleVerifyOtp = async () => {
    const otpCode = otpValues.join("");
    if (otpCode.length !== 5) {
      setApiMessage("error", "Please enter complete OTP");
      return;
    }

    if (!hashdata) {
      setApiMessage("error", "Phone code hash is missing. Please resend OTP.");
      return;
    }

    setIsVerifyingOtp(true);
    try {
      const formData = new FormData();
      const fullPhoneNumber = `${countryData[selectedCountry].code}${phoneNumber}`;
      formData.append("phone_number", fullPhoneNumber);
      formData.append("code", otpCode);
      formData.append("phone_code_hash", hashdata);

      const response = await apiInstance.post(URL.TELEGRAM_SIGNIN, formData, {
        headers: {
          brand: BrandId,
        },
      });

      if (response?.data?.status) {
        setApiMessage(
          "success",
          response?.data?.message || "Telegram connected successfully!"
        );
        setShowOtpPopup(false);
        // Reset states
        setOtpValues(["", "", "", "", ""]);
        setHashdata(null);
        setPhoneNumber("");
        // Call success callback and close dialog
        if (onSuccess) onSuccess();
        if (onClose) onClose();
      } else {
        setApiMessage(
          "error",
          response?.data?.message || "Failed to verify OTP"
        );
      }
    } catch (error) {
      console.error("OTP Verification Error:", error);
      setApiMessage(
        "error",
        error?.response?.data?.message ||
          error?.message ||
          "Failed to verify OTP"
      );
    } finally {
      setIsVerifyingOtp(false);
    }
  };

  // Handle resend OTP
  const handleResendOtp = () => {
    if (resendTimer === 0) {
      handleSendOTP();
    }
  };

  // Handle edit phone number
  const handleEditPhoneNumber = () => {
    setShowOtpPopup(false);
    setOtpValues(["", "", "", "", ""]);
    setHashdata(null);
  };

  return (
    <div className="min-h-[500px] bg-white flex flex-col items-center px-6 py-8 font-Ubuntu relative">
      {/* Close Button */}
      {onClose && (
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
          onClick={onClose}
        >
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
            <path
              d="M18 6L6 18M6 6L18 18"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
      )}

      {/* Telegram Logo */}
      <div className="">
        <div className="w-24 h-24 bg-[#0088cc] rounded-full flex items-center justify-center">
          <img
            src={siteConstant.SOCIAL_ICONS.TELEGRAM}
            alt=""
            className="h-14 w-14"
          />
        </div>
      </div>

      {/* Title */}
      <h1 className="text-2xl font-medium text-gray-900 my-4">Telegram</h1>

      {!showOtpPopup ? (
        <>
          {/* Subtitle */}
          <p className="text-gray-500 text-center mb-12 px-4 leading-relaxed">
            Please confirm your country code and enter your phone number.
          </p>

          {/* Country Selection */}
          <div className="w-full max-w-sm mb-6 ">
            <label className="block text-gray-600 text-sm mb-2">Country</label>
            <div className="relative">
              <select
                className="w-full h-12 px-4 py-3 border border-gray-200 rounded-lg appearance-none bg-white focus:outline-none focus:border-blue-400 pr-10"
                value={selectedCountry}
                onChange={handleCountryChange}
              >
                {Object.keys(countryData).map((country) => (
                  <option key={country} value={country}>
                    {countryData[country].flag} {country}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
                <svg
                  className="w-4 h-4 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* Mobile Number Input */}
          <div className="w-full max-w-sm mb-12">
            <label className="block text-gray-600 text-sm mb-2">
              Mobile Number
            </label>
            <div className="flex">
              <div className="flex items-center px-3 py-3 border border-gray-200 border-r-0 rounded-l-lg bg-gray-50">
                <span className="text-sm">
                  {countryData[selectedCountry].flag}{" "}
                  {countryData[selectedCountry].code}
                </span>
              </div>
              <input
                type="tel"
                placeholder="Enter Mobile Number"
                className="flex-1 px-4 py-3 border border-gray-200 rounded-r-lg focus:outline-none focus:border-blue-400"
                value={phoneNumber}
                onChange={handlePhoneNumberChange}
              />
            </div>
          </div>

          {/* Send OTP Button */}
          <button
            onClick={handleSendOTP}
            disabled={isLoading || !phoneNumber.trim()}
            className={`w-full max-w-sm py-4 rounded-lg font-medium text-lg transition duration-300 ${
              isLoading || !phoneNumber.trim()
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-[#0088cc] hover:bg-[#0077b3] text-white"
            }`}
          >
            {isLoading ? (
              <LoadingSpinner
                text="Sending OTP..."
                spinnerSize="h-4 w-4"
                textColor="text-white"
              />
            ) : (
              "Send OTP"
            )}
          </button>
        </>
      ) : (
        <>
          {/* OTP Verification Popup */}
          <p className="text-gray-500 text-center mb-8 px-4 leading-relaxed">
            We have sent you a code on Telegram. Please check your Telegram app
            in your device.
          </p>

          {/* Phone Number Display with Edit Button */}
          <div className="flex items-center justify-center mb-8">
            <span className="text-gray-700 font-medium">
              {countryData[selectedCountry].code} {phoneNumber}
            </span>
            <button
              onClick={handleEditPhoneNumber}
              className="ml-3 text-[#0088cc] hover:text-[#0077b3] font-medium flex items-center"
            >
              <svg
                className="w-4 h-4 mr-1"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
              Edit
            </button>
          </div>

          {/* OTP Input Fields */}
          <div className="flex justify-center space-x-3 mb-6">
            {otpValues.map((value, index) => (
              <input
                key={index}
                id={`otp-${index}`}
                type="text"
                maxLength="1"
                value={value}
                onChange={(e) => handleOtpChange(index, e.target.value)}
                onKeyDown={(e) => handleOtpKeyDown(index, e)}
                className="w-12 h-12 text-center text-xl font-semibold border-2 border-gray-200 rounded-lg focus:outline-none focus:border-[#0088cc] bg-[#0088cc] text-white"
              />
            ))}
          </div>

          {/* Resend Timer */}
          <div className="text-center mb-8">
            <p className="text-gray-500 mb-2">
              {resendTimer > 0 ? (
                `Resend code in ${resendTimer}s`
              ) : (
                <button
                  onClick={handleResendOtp}
                  className="text-[#0088cc] hover:text-[#0077b3] font-medium"
                >
                  Resend code
                </button>
              )}
            </p>
          </div>

          {/* Verify OTP Button */}
          <button
            onClick={handleVerifyOtp}
            disabled={isVerifyingOtp || otpValues.join("").length !== 5}
            className={`w-full max-w-sm py-4 rounded-lg font-medium text-lg transition duration-300 ${
              isVerifyingOtp || otpValues.join("").length !== 5
                ? "bg-gray-400 cursor-not-allowed"
                : "bg-[#0088cc] hover:bg-[#0077b3] text-white"
            }`}
          >
            {isVerifyingOtp ? (
              <LoadingSpinner
                text="Verifying OTP..."
                spinnerSize="h-4 w-4"
                textColor="text-white"
              />
            ) : (
              "Verify OTP"
            )}
          </button>
        </>
      )}
    </div>
  );
}

export default Telegram_mobile;
