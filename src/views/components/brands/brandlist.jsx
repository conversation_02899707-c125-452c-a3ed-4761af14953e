import { Dialog } from "@mui/material";
import React, { useContext, useState, useEffect, useCallback } from "react";
import debounce from "lodash.debounce";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import Loader from "../../../helpers/UI/Loader";
import RoleManagement from "../Role-management";
import { IntlContext } from "../../../App";
import siteConstant from "../../../helpers/constant/siteConstant";

const BrandList = ({
  open,
  onClose,
  setBrandId,
  brandId,
  userId,
  fetchUsers,
}) => {
  const [search, setSearch] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedBrands, setSelectedBrands] = useState([]);
  const [Isrole, setIsrole] = useState(false);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const debouncedHandleSearch = useCallback(
    debounce((searchText) => {
      if (searchText.trim() !== "") {
        setLoading(true);
        apiInstance
          .get(`${URL.GET_BRANDS}?search=${searchText}`)
          .then((response) => {
            setSuggestions(response.data.data || []);
          })
          .catch((error) => {
            console.error("Error searching brands:", error);
            setSuggestions([]);
          })
          .finally(() => {
            setLoading(false);
          });
      } else {
        setSuggestions([]);
      }
    }, 300),
    []
  );

  useEffect(() => {
    debouncedHandleSearch(search);
  }, [search, debouncedHandleSearch]);

  const handleSelectBrand = (brand) => {
    // Check if the brand is already selected
    if (!selectedBrands.some((item) => item.id === brand.id)) {
      const updatedBrands = [...selectedBrands, brand];
      setSelectedBrands(updatedBrands);
      setBrandId(updatedBrands.map((b) => b.id));
    }
    setSearch("");
    setSuggestions([]);
  };

  const handleRemoveBrand = (brandId) => {
    const updatedBrands = selectedBrands.filter(
      (brand) => brand.id !== brandId
    );
    setSelectedBrands(updatedBrands);
    setBrandId(updatedBrands.map((b) => b.id));
  };

  const handleOpenDialog = () => {
    if (selectedBrands.length > 0) {
      setIsrole(true);
    }
  };

  // Modified to close both this dialog and any child dialogs
  const handleCloseDialog = () => {
    setIsrole(false);
    onClose(); // This will close the BrandList dialog and signal to AddUser to close
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        fullWidth
        maxWidth="md"
        BackdropProps={{ style: { backgroundColor: "transparent" } }}
        PaperProps={{
          style: { boxShadow: "none" },
        }}
      >
        <div className="flex flex-col items-center bg-white rounded-[12px] py-8 px-4 md:px-36 relative font-Ubuntu w-full max-w-full h-[450px] mx-auto">
          <h2 className="text-xl text-black font-semibold text-center mb-2">
            {localesData?.USER_WEB?.BRANDS?.BRANDS_LIST || "Brand List"}
          </h2>

          <button
            onClick={onClose}
            className="absolute right-6 top-6 text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#A9ABAD"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          <p className="text-[#A9ABAD] font-bold text-[14px] sm:text-[16px] text-center mb-6">
            {localesData?.USER_WEB?.BRANDS?.SELECT_BRANDS ||
              "Select brands to assign to the user"}
          </p>

          <div className="w-full mb-4">
            <fieldset className="w-full border border-gray-300 rounded-[12px] px-3 py-1 transition-all focus-within:rounded-none focus-within:rounded-tl-[12px] focus-within:rounded-tr-[12px] focus-within:border-gray-500">
              <legend className="text-sm font-medium text-gray-700 px-1">
                {localesData?.USER_WEB?.BRANDS?.SEARCH_BRANDS ||
                  "Search brands"}
              </legend>
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder=" "
                className="w-full bg-transparent border-none p-0 text-sm text-gray-900 placeholder-transparent focus:outline-none focus:ring-0 mb-[6px]"
              />
            </fieldset>
          </div>

          {selectedBrands.length > 0 && (
            <div className="w-full mb-4">
              {selectedBrands.map((brand) => (
                <div
                  key={brand.id}
                  className="w-full p-3 flex items-center gap-3 mb-2 justify-between"
                >
                  <div className="flex items-center gap-3">
                    {brand.logo ? (
                      <img
                        src={`${URL.SOCKET_URL}/${brand.logo}`}
                        alt={brand.name}
                        className="h-10 w-10 rounded-full p-1"
                      />
                    ) : (
                      <div className="h-10 w-10 rounded-full p-1 flex items-center justify-center bg-gray-100">
                        {brand.name?.charAt(0).toUpperCase() || "B"}
                      </div>
                    )}
                    <div>
                      <p className="text-sm font-medium text-gray-800">
                        {brand.name}
                      </p>
                      <p className="text-xs text-gray-500">{brand.domain}</p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleRemoveBrand(brand.id)}
                    className="text-[#B42318] hover:text-red-700 text-sm font-medium"
                  >
                    {localesData?.USER_WEB?.BRANDS?.REMOVE || "Remove"}
                  </button>
                </div>
              ))}
            </div>
          )}

          {loading && <Loader />}

          {!loading && suggestions.length > 0 && (
            <ul className="w-full -mt-[17px] border border-gray-300 rounded-bl-[12px] rounded-br-[12px] max-h-[200px] overflow-y-auto">
              {suggestions.map((brand) => (
                <li
                  key={brand.id}
                  onClick={() => handleSelectBrand(brand)}
                  className="flex items-center gap-3 px-4 py-2 hover:bg-gray-100 cursor-pointer transition-colors"
                >
                  {brand.logo ? (
                    <img
                      src={`${URL.SOCKET_URL}/${brand.logo}`}
                      alt={brand.name}
                      className="h-10 w-10 rounded-full p-1"
                    />
                  ) : (
                    <div className="h-10 w-10 rounded-full border-[2px] p-1 border-[#674941] flex items-center justify-center bg-gray-100">
                      {brand.name?.charAt(0).toUpperCase() || "B"}
                    </div>
                  )}
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      {brand.name}
                    </p>
                    <p className="text-xs text-gray-500">{brand.domain}</p>
                  </div>
                </li>
              ))}
            </ul>
          )}

          {!loading && search && suggestions.length === 0 && (
            <p className="text-Red text-md font-normal mt-2 text-center">
              {localesData?.USER_WEB?.BRANDS?.NO_MATCHING_RESULTS_FOUND ||
                "No brands found. Try another search!"}
            </p>
          )}

          <div className="absolute flex justify-center items-center bottom-6 px-8 w-full">
            <button
              type="submit"
              className="w-[60%] sm:w-[40%] bg-[#563D39] text-white py-3 rounded-[12px] font-semibold hover:bg-opacity-90 transition-all"
              onClick={handleOpenDialog}
              disabled={selectedBrands.length === 0}
            >
              {localesData?.USER_WEB?.BRANDS?.ADD_ROLE || "Add Role"}
            </button>
          </div>
        </div>
      </Dialog>

      {Isrole && (
        <RoleManagement
          open={Isrole}
          onClose={handleCloseDialog}
          setBrandId={setBrandId}
          brandId={brandId}
          userId={userId}
          fetchUsers={fetchUsers}
        />
      )}
    </>
  );
};

export default BrandList;
