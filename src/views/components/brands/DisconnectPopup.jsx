// import Facebook from "src/assets/images/Analytics/facebook.svg";
import Facebook from "../../../assets/images/Analytics/facebook.svg";
import PinterestLogo from "../../..//assets/images/Analytics/pinterest.svg";
import VimoLogo from "../../..//assets/images/Analytics/vimeo.svg";
import LinkedInLogo from "../../..//assets/images/Analytics/linkedIn.svg";
import TwitterLogo from "../../..//assets/images/Analytics/X.svg";
import TiktokLogo from "../../..//assets/images/Analytics/tiktok.svg";
import RedditLogo from "../../..//assets/images/Analytics/reddit.svg";
import ThreadsLogo from "../../..//assets/images/Analytics/thread.svg";
import InstagramLogo from "../../..//assets/images/Analytics/instagram.svg";
import YoutubeLogo from "../../..//assets/images/Analytics/youtube.svg";
import TumblrLogo from "../../..//assets/images/Analytics/tumblr.svg";
import XLogo from "../../..//assets/images/Analytics/X.svg";
import { Dialog } from "@mui/material";
import { IntlContext } from "../../../App";
import { useContext } from "react";
import TelegramLogo from "../../..//assets/images/Analytics/telegram.svg";

export default function DisconnectDialog({
  open,
  onClose,
  platform,
  onConfirm,
}) {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  if (!open || !platform) return null;

  const config = platformConfig[platform];

  return (
    <Dialog open={open} onClose={onClose}>
      <div
        className="fixed inset-0 bg-black/50 flex items-center justify-center px-4 font-Ubuntu"
        onClick={onClose}
      >
        <div
          className="bg-white rounded-2xl shadow-xl w-full max-w-sm p-6 relative"
          onClick={(e) => e.stopPropagation()}
        >
          <button
            className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
            onClick={onClose}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path
                d="M18 6L6 18M6 6L18 18"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </button>

          <div className="flex flex-col items-start justify-center text-center">
            <div className="w-[45px] h-[45px] rounded-full flex items-center justify-center mb-4">
              {config?.icon && (
                <img
                  src={config.icon}
                  alt={platform}
                  className="w-[45px] h-[45px]"
                />
              )}
            </div>

            <h2 className="text-[18px] font-bold text-[#000000] mb-1">
              {config?.title || "Disconnect Platform"}
            </h2>

            <div
              className="text-[18px] font-bold mb-1 capitalize mt-[15px]"
              style={{ color: config?.color }}
            >
              {platform}
            </div>

            <p className="text-[#46484a] text-[14px] font-normal leading-[20px] max-w-xs text-left">
              {config?.message ||
                "Are you sure you want to disconnect this platform?"}
            </p>
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <button
              onClick={onClose}
              className="px-5 py-2 text-sm border border-gray-300 text-gray-600 rounded-lg hover:bg-gray-100"
            >
              {config?.cancelText ||
                localesData?.USER_WEB?.BRANDS?.CANCEL ||
                "Cancel"}
            </button>
            <button
              onClick={onConfirm}
              className="px-5 py-2 text-sm bg-[#4B2C27] text-white rounded-lg hover:bg-[#3c221f]"
            >
              {config?.confirmText || "Disconnect"}
            </button>
          </div>
        </div>
      </div>
    </Dialog>
  );
}

const platformConfig = {
  facebook: {
    color: "#0866FF",
    icon: Facebook,
    title: "Disconnect Facebook",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  pinterest: {
    color: "#E60023",
    icon: PinterestLogo,
    title: "Disconnect Pinterest",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  vimeo: {
    color: "#1EB8EB",
    icon: VimoLogo,
    title: "Disconnect Vimeo",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  linkedin: {
    color: "#0A66C2",
    icon: LinkedInLogo,
    title: "Disconnect LinkedIn",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  twitter: {
    color: "#100E0F",
    icon: TwitterLogo,
    title: "Disconnect X (Twitter)",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  tiktok: {
    color: "#000000",
    icon: TiktokLogo,
    title: "Disconnect TikTok",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  reddit: {
    color: "#FC471E",
    icon: RedditLogo,
    title: "Disconnect Reddit",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  threads: {
    color: "#000000",
    icon: ThreadsLogo,
    title: "Disconnect Threads",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  thread: {
    color: "#000000",
    icon: ThreadsLogo,
    title: "Disconnect Threads",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  instagram: {
    color: "#C30FB2",
    icon: InstagramLogo,
    title: "Disconnect Instagram",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  youtube: {
    color: "#FF0302",
    icon: YoutubeLogo,
    title: "Disconnect YouTube",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  tumblr: {
    color: "#35465C",
    icon: TumblrLogo,
    title: "Disconnect Tumblr",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  telegram: {
    color: "#0088CC",
    icon: TelegramLogo,
    title: "Disconnect Telegram",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
  x: {
    color: "#35465C",
    icon: XLogo,
    title: "Disconnect X",
    message:
      "Are you sure you want to disconnect this platfrom from your account?",
    confirmText: "Disconnect",
    cancelText: "Cancel",
  },
};
