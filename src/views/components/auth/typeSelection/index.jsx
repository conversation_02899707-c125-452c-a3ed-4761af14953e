import React, { useEffect, useState } from "react";
import { URL } from "../../../../helpers/constant/Url";
import { setApiMessage } from "../../../../helpers/context/toaster";
import apiInstance from "../../../../helpers/Axios/axiosINstance";

const Typepage = () => {
  const [userType, setUserType] = useState("");
  const [industryType, setIndustryType] = useState("");
  const [userTypes, setUserTypes] = useState([]);
  const [industries, setIndustries] = useState([]);
  const [submitting, setSubmitting] = useState(false);

  const GetIndustry = async () => {
    try {
      const response = await apiInstance.get(URL.GET_INDUSTRY);

      if (response?.data?.status) {
        setIndustries(response.data.data.industries || []);
        setUserTypes(response.data.data.user_types || []);
      }
    } catch (error) {
      console.error("Error fetching industry data:", error.response || error);
    }
  };

  useEffect(() => {
    GetIndustry();
  }, []);

  const handleSubmit = async () => {
    setSubmitting(true);
    try {
      const response = await apiInstance.post(
        URL.USER_TYPE_SELECTION,
        {
          type: userType,
          industry: industryType,
        },
        {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("UserToken")}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (response?.data?.status) {
        setApiMessage("success", response.data.message);
        setTimeout(() => {
          window.location.href = "/sign-in";
        }, 1000);
      } else {
        alert("Failed to save selection. Please try again.");
      }
    } catch (error) {
      console.error("Error submitting data:", error.response || error);
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen font-Ubuntu mx-2">
      <div className="flex flex-col w-full max-w-lg mx-auto bg-white rounded-lg border-2 overflow-hidden py-6">
        <div className="p-8 text-center">
          <p className="text-lg text-gray-700">Tell us something more</p>
          <h1 className="text-2xl font-bold mt-1 text-Red">About You</h1>
        </div>

        <div className="px-6 py-4 flex flex-col gap-4">
          <div className="relative">
            <select
              className="w-full p-3 border border-gray-300 rounded-lg appearance-none bg-white text-gray-700 focus:ring-2 focus:ring-amber-900 focus:outline-none focus:border-none"
              value={userType}
              onChange={(e) => setUserType(e.target.value)}
            >
              <option value="" disabled>
                Types of User
              </option>
              {userTypes.map((type) => (
                <option key={type.id} value={type.type_name}>
                  {type.type_name}
                </option>
              ))}
            </select>
          </div>

          <div className="relative">
            <select
              className="w-full p-3 border border-gray-300 rounded-lg appearance-none bg-white text-gray-700 focus:ring-2 focus:ring-amber-900 focus:outline-none focus:border-none"
              value={industryType}
              onChange={(e) => setIndustryType(e.target.value)}
            >
              <option value="" disabled>
                Types of Industry
              </option>
              {industries.map((industry) => (
                <option key={industry.id} value={industry.industry_name}>
                  {industry.industry_name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="p-4 mt-4 flex justify-center">
          <button
            className={`rounded-xl text-[16px] w-1/2 px-8 py-2.5 font-semibold ${
              userType && industryType
                ? "bg-Red text-white"
                : "bg-amber-800 text-gray-200 cursor-not-allowed"
            }`}
            disabled={!userType || !industryType || submitting}
            onClick={handleSubmit}
          >
            {submitting ? "Submitting..." : "Next"}
          </button>
        </div>
      </div>
    </div>
  );
};

export default Typepage;
