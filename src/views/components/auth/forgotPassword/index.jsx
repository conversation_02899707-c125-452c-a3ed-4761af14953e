import React, { useContext, useState } from "react";
import { useNavi<PERSON>, Link } from "react-router-dom";
import { Field, Formik } from "formik";
import * as Yup from "yup";
import { URL } from "../../../../helpers/constant/Url";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { setApiMessage } from "../../../../helpers/context/toaster";
import { IntlContext } from "../../../../App";
import LoadingSpinner from "../../../../helpers/UI/LoadingSpinner";
import Arrow from "../../../../assets/images/ForgotPass/Arrow.svg";

// Import background images from Login page
import X from "../../../../assets/images/Login/X.svg";
import facebook_Bottom from "../../../../assets/images/Login/Facebook_Bottom.svg";
import facebookk from "../../../../assets/images/Login/Facebookk.svg";
import Instagram_Top from "../../../../assets/images/Login/Instagram_Top.svg";
import Instagram from "../../../../assets/images/Login/Instagram.svg";
import Pinterest_Right from "../../../../assets/images/Login/Pinterest_Right.svg";
import Pinterest from "../../../../assets/images/Login/Pinterest.svg";
import Tiktok from "../../../../assets/images/Login/Tiktok.svg";
import X_Right from "../../../../assets/images/Login/X_Right.svg";
import Youtube from "../../../../assets/images/Login/Youtube.svg";
import Email from "../../../../assets/images/Login/Email.svg";

// Import or add the reset password illustration
import resetPasswordIllustration from "../../../../assets/images/ForgotPass/ForgotPass.svg"; // You'll need to add this image
import { CustomTextField } from "../../custom/CustomTextField";

const ForgotPassword = () => {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);

  const inputWrapStyle = "relative mb-5 mt-5";
  const legendStyle =
    "absolute text-sm text-[#1C1B1F] left-3 -top-[10px] bg-white px-1 z-10 mb-4";
  const inputStyle =
    "w-full  pl-14  py-4 mt-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#614036] focus:border-[#614036] focus:outline-none";

  return (
    <div className="min-h-screen bg-white flex items-center justify-center px-4 font-Ubuntu max-w-[90%] mx-auto overflow-y-auto">
      {/* Background floating icons - same as login page */}
      <div className="absolute top-0 left-40">
        <img src={X} alt="X Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[25%] left-0">
        <img
          src={facebookk}
          alt="Facebook Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-16 left-[15%]">
        <img
          src={Instagram}
          alt="Instagram Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-0 left-[50%]">
        <img
          src={facebook_Bottom}
          alt="Facebook Bottom Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-[10%] right-[30%]">
        <img src={X_Right} alt="X Right Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute bottom-[5%] right-[10%]">
        <img
          src={Pinterest_Right}
          alt="Pinterest Right Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-[50%] right-[2%]">
        <img src={Youtube} alt="Youtube Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[5%] right-[5%]">
        <img
          src={Instagram_Top}
          alt="Instagram Top Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute top-[10%] right-[30%]">
        <img src={X_Right} alt="X Right Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[4%] right-[50%]">
        <img src={Tiktok} alt="Tiktok Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[25%] right-[50%]">
        <img
          src={Pinterest}
          alt="Pinterest Logo"
          className="max-w-full h-auto"
        />
      </div>

      <div className="w-full flex flex-col justify-start items-center md:flex-row">
        {/* Left section - form (styled like the image) */}
        <div className="w-full md:w-[50%] max-w-md mx-auto md:ml-[120px]">
          <Link
            to="/sign-in"
            className="flex items-center mb-6 text-[#563D39] hover:nounderline"
          >
            <img src={Arrow} alt="Arrow" className="max-w-full h-auto mr-2" />
            Back to login
          </Link>

          <div className="mb-6">
            <h1 className="text-4xl font-bold text-[#614036]">
              Forgot Your <span className="font-normal">Password?</span>
            </h1>
            <p className="text-[#AA8882] text-sm mt-3 mb-12 font-normal">
              Don't worry, happens to all of us. Enter your email below to
              receive your password reset link.
            </p>
          </div>

          <Formik
            initialValues={{
              email: "",
            }}
            validationSchema={Yup.object().shape({
              email: Yup.string()
                .email(
                  localesData?.USER_WEB?.MESSAGES?.INVALID_EMAIL_ADDRESS ||
                    "Invalid email address"
                )
                .required(
                  localesData?.USER_WEB?.MESSAGES?.EMAIL_REQUIRED ||
                    "Email is required"
                ),
            })}
            onSubmit={async (requestData, { setSubmitting }) => {
              setLoading(true);
              try {
                const form = new FormData();
                form.append("email", requestData?.email);
                const { status, data } = await apiInstance.post(
                  URL.FORGOT_PASSWORD,
                  form,
                  {
                    headers: {
                      "Content-Type": "multipart/form-data",
                    },
                  }
                );
                if (data) {
                  if (data?.status) {
                    setApiMessage("success", data?.message);
                    navigate("/otp-verify", {
                      state: { email: requestData?.email },
                      replace: true,
                    });
                  } else {
                    setApiMessage("error", data?.message);
                  }
                }
              } catch (error) {
                setApiMessage("error", error?.message);
              } finally {
                setLoading(false);
                setSubmitting(false);
              }
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              touched,
              values,
              isSubmitting,
            }) => (
              <form onSubmit={handleSubmit}>
                <div className="mb-6 relative">
                  <span className="absolute top-1/2 -translate-y-1/2 left-4 flex items-center pointer-events-none z-10">
                    <img src={Email} alt="Email" className="w-5 h-5" />
                  </span>
                  <Field
                    as={CustomTextField}
                    variant="outlined"
                    borderRadius="12px"
                    label="Email"
                    fullWidth
                    type="text"
                    id="email"
                    name="email"
                    placeholder=""
                    value={values.email}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        paddingLeft: "45px",
                        "& fieldset": {
                          borderRadius: "20px",
                        },
                      },
                      "& .MuiOutlinedInput-input": {
                        paddingLeft: "10px !important",
                        color: "#000000 !important", // Force black text
                        fontWeight: "normal !important",
                        "&:-webkit-autofill": {
                          WebkitTextFillColor: "#000000 !important",
                          WebkitBoxShadow:
                            "0 0 0 1000px white inset !important",
                          fontWeight: "normal !important",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        left: "35px",
                        transform: "translate(14px, 16px) scale(1)",
                        "&.MuiInputLabel-shrink": {
                          transform: "translate(5px, -9px) scale(0.75)",
                        },
                      },
                    }}
                  />
                </div>

                <div className="flex justify-center items-center mt-16">
                  <button
                    type="submit"
                    className={`w-[50%] py-3 rounded-[12px] font-medium transition duration-300 ${
                      loading
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-[#614036] hover:bg-[#7a5046] text-white"
                    }`}
                    disabled={loading}
                  >
                    {loading ? (
                      <LoadingSpinner
                        text="Loading..."
                        spinnerSize="h-4 w-4"
                        textColor="text-white"
                      />
                    ) : (
                      "Submit"
                    )}
                  </button>
                </div>
              </form>
            )}
          </Formik>
        </div>

        {/* Right section - illustration (from the image) */}
        <div className="hidden md:block w-[50%]">
          <div className="flex justify-center items-center h-full">
            <img
              src={resetPasswordIllustration}
              alt="Reset password illustration"
              className="max-w-full h-auto"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
