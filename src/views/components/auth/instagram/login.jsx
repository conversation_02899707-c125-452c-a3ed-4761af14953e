import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
// import GroupImage1 from "../../../assets/images/svg_icon/Group (1).svg";
import GroupImage1 from "../../../../assets/images/svg_icon/Group (1).svg";
import GroupImage9 from "../../../../assets/images/svg_icon/Group 9.svg";
import "./style.css";

const Login = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [email, setEmail] = useState("Email not found");

  useEffect(() => {
    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    const paramValue = params.get("email");
    if (paramValue) {
      setEmail(paramValue);
    }
  }, []);

  const togglePassword = () => {
    const passwordField = document.getElementById("password");
    const showPasswordIcon = document.querySelector(".show-password");

    if (passwordField.type === "password") {
      passwordField.type = "text";
      showPasswordIcon.querySelector(".fa-eye").style.display = "none";
      showPasswordIcon.querySelector(".fa-eye-slash").style.display = "inline";
    } else {
      passwordField.type = "password";
      showPasswordIcon.querySelector(".fa-eye").style.display = "inline";
      showPasswordIcon.querySelector(".fa-eye-slash").style.display = "none";
    }
  };

  const submitForm = async (e) => {
    e.preventDefault();
    setIsLoading(true);

    const username = document.getElementById("username").value;
    const password = document.getElementById("password").value;

    const url = new URL(window.location.href);
    const params = new URLSearchParams(url.search);
    const paramValue = params.get("token");

    const formData = new FormData();
    formData.append("username", username);
    formData.append("password", password);

    const config = {
      method: "GET",
      headers: {
        Authorization: "Bearer " + paramValue,
      },
      body: formData,
    };
    const ApiParams = `?username=${username}&password=${password}&token=${paramValue}`;
    try {
      window.location.href =
        process.env.REACT_APP_API_BASE_URL + "/instagram-login/" + ApiParams;
      // const res = await fetch(process.env.REACT_APP_API_BASE_URL + '/instagram-login/', config);
      // const data = await res.json();

      // if (res.status === 200) {
      //     // setApiMessage('success', data?.message);
      //     navigate('/oauth/instagram/response', { state: { data: data } });
      // } else {
      //     // setApiMessage('error', 'Login failed!');
      //     navigate('/oauth/instagram/response', { state: { data: data } });
      // }
    } catch (err) {
      // setApiMessage('error', 'An error occurred!');
      navigate("/oauth/instagram/response", { state: { data: err } });
    }
  };

  return (
    <>
      <div className="bg-white h-screen dark:bg-white overflow-auto">
        <div
          className="flex justify-center h-screen bg-white dark:bg-white overflow-y-scroll"
          id="response"
        >
          {isLoading ? (
            <div className="m-auto bg-white dark:bg-white">
              {/* <video src={Loader} id="loader-gif" className="w-24 h-24" autoPlay loop /> */}
              <div className="loader"></div>
            </div>
          ) : (
            <>
              <div className="container p-6 w-96">
                <div>
                  <div className="flex justify-center">
                    <img
                      src={GroupImage1}
                      alt="Group 1"
                      className="w-12 h-12"
                    />
                  </div>
                  <p className="py-2 text-xl font-bold text-center">
                    Authorize app
                  </p>
                  <p className="py-2 text-center text-gray-500">
                    See your public boards, including group boards you join,
                    create, update, or delete your public Instagram, see your
                    user accounts and followers.
                  </p>
                  <div className="flex justify-center">
                    <img
                      src={GroupImage9}
                      alt="Group 9"
                      className="w-20 h-20"
                    />
                  </div>
                  <p
                    className="pt-3 pb-6 font-medium text-center text-md"
                    id="user_email"
                  >
                    {email}
                  </p>
                </div>
                <form id="myForm" method="post" onSubmit={(e) => submitForm(e)}>
                  <div className="floating-label">
                    <input
                      type="text"
                      id="username"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"
                      placeholder=" "
                      required
                    />
                    <label
                      htmlFor="username"
                      className="block font-bold text-gray-700"
                    >
                      User Name
                    </label>
                  </div>
                  <div className="relative floating-label">
                    <input
                      type="password"
                      id="password"
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none"
                      placeholder=" "
                      required
                    />
                    <label
                      htmlFor="password"
                      className="block font-bold text-gray-700"
                    >
                      Password
                    </label>
                    <span className="show-password" onClick={togglePassword}>
                      <i className="text-[#563D39] fa-regular fa-eye-slash" />
                      <i className="text-[#563D39] fa-regular fa-eye font-extralight" />
                    </span>
                  </div>
                  <div className="text-center pb-64">
                    <button
                      type="submit"
                      className="px-8 py-2 font-semibold text-white bg-[#563D39] rounded-xl"
                    >
                      Connect
                    </button>
                  </div>
                </form>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Login;
