import React, { useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";


const InstagramResponse = () => {
    const [data, setData] = useState()
    const location = useLocation()
    const navigate = useNavigate()


    useEffect(() => {
        const locationState = location?.state;
        if (Object.keys(locationState?.data).length) {
            setData(locationState?.data)
        } else {
            navigate('/oauth/instagram')
        }
    }, [])
    return (
        <>
            {JSON.stringify(data)}
        </>
    )
}

export default InstagramResponse