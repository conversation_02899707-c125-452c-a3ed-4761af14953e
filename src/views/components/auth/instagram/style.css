.bg {
  /* background-image: url('./images/bg-image.svg'); */
  background-repeat: no-repeat;
  background-size: 100%;
}

.container {
  font-family: "Ubuntu", sans-serif;
  /* background-image: url('./images/bg-image.svg');
    background-repeat: no-repeat;
    background-size: 100%; */
}

.floating-label {
  position: relative;
  margin-bottom: 1.5rem;
}

.floating-label input:focus~label,
.floating-label input:not(:placeholder-shown)~label {
  transform: translateY(-1.5rem) translateX(1rem);
  font-size: 0.9rem;
  padding: 0 0.25rem;
  font-family: "Ubuntu", sans-serif;
}

.floating-label input:focus~label {
  color: gray;
  font-family: "Ubuntu", sans-serif;
}

.floating-label input {
  padding-top: 1rem;
  padding-right: 2.5rem;
  border-radius: 21px;
  font-family: "Ubuntu", sans-serif;
}

.floating-label label {
  position: absolute;
  top: 0.75rem;
  left: 0.75rem;
  pointer-events: none;
  transition: all 0.2s ease;
  color: #6b7280;
  background-color: white;
  font-weight: 500;
}

.show-password {
  cursor: pointer;
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280;
  z-index: 10;
}

.show-password i {
  transition: transform 0.2s ease;
}

.show-password .fa-eye-slash {
  display: none;
}

.show-password.active .fa-eye {
  display: none;
}

.show-password.active .fa-eye-slash {
  display: inline;
}

.loader {
  width: 35px;
  aspect-ratio: 1;
  --_g: no-repeat radial-gradient(circle closest-side, #563D39 90%, #d50a0a00);
  background:
    var(--_g) 0 0,
    var(--_g) 0 100%,
    var(--_g) 100% 100%;
  background-size: 40% 40%;
  animation: l11 1s infinite linear;
}

@keyframes l11 {
  25% {
    background-position: 100% 0, 0 100%, 100% 100%
  }

  50% {
    background-position: 100% 0, 0 0, 100% 100%
  }

  75% {
    background-position: 100% 0, 0 0, 0 100%
  }

  100% {
    background-position: 100% 100%, 0 0, 0 100%
  }
}

@media (max-width: 640px) {
  .container {
    width: 90%;
    font-family: "Ubuntu", sans-serif;
  }

  .bg {
    margin-top: 5px;
  }
}