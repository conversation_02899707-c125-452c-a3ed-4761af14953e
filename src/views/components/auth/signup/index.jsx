import React, { useState, useContext } from "react";
import { <PERSON> } from "react-router-dom";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { URL } from "../../../../helpers/constant/Url";
import { setApiMessage } from "../../../../helpers/context/toaster";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import logo from "../../../../assets/images/logo.svg";
import siteConstant from "../../../../helpers/constant/siteConstant";
import { IntlContext } from "../../../../App";

// Social media icons
import X from "../../../../assets/images/Login/X.svg";
import facebook_Bottom from "../../../../assets/images/Login/Facebook_Bottom.svg";
import facebookk from "../../../../assets/images/Login/Facebookk.svg";
import Instagram_Top from "../../../../assets/images/Login/Instagram_Top.svg";
import Instagram from "../../../../assets/images/Login/Instagram.svg";
import Pinterest_Right from "../../../../assets/images/Login/Pinterest_Right.svg";
import Pinterest from "../../../../assets/images/Login/Pinterest.svg";
import Tiktok from "../../../../assets/images/Login/Tiktok.svg";
import X_Right from "../../../../assets/images/Login/X_Right.svg";
import Youtube from "../../../../assets/images/Login/Youtube.svg";
import Email from "../../../../assets/images/Login/Email.svg";
import Lock from "../../../../assets/images/Login/Lock.svg";
import User from "../../../../assets/images/Sign_up/User.svg";
import ShowPass from "../../../../assets/images/ForgotPass/SeePass.svg";
import HidePass from "../../../../assets/images/ForgotPass/HidePass.svg";

// Import signup illustration image
import signUpIllustration from "../../../../assets/images/Sign_up/vector.svg";
import { CustomTextField } from "../../../../views/components/custom/CustomTextField";

const SignUp = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const validationSchema = Yup.object().shape({
    name: Yup.string()
      .required(
        localesData?.USER_WEB?.MESSAGES?.NAME_REQUIRED || "Name is required."
      )
      .min(
        3,
        localesData?.USER_WEB?.MESSAGES?.NAME_LENGTH ||
          "Name must be between 3 and 15 characters"
      )
      .max(
        20,
        localesData?.USER_WEB?.MESSAGES?.NAME_LENGTH ||
          "Name must be between 3 and 20 characters"
      )
      .matches(
        /^[A-Za-z\s]{2,50}$/,
        localesData?.USER_WEB?.MESSAGES?.NAME_PATTERN ||
          "Name can only contain letters and spaces, and must be 2-50 characters long."
      ),
    username: Yup.string()
      .required(
        localesData?.USER_WEB?.MESSAGES?.USERNAME_REQUIRED ||
          "Username cannot be empty"
      )
      .min(
        3,
        localesData?.USER_WEB?.MESSAGES?.USERNAME_LENGTH ||
          "Username must be between 3 and 15 characters"
      )
      .max(
        15,
        localesData?.USER_WEB?.MESSAGES?.USERNAME_LENGTH ||
          "Username must be between 3 and 15 characters"
      )
      .matches(
        /^(?!_)[a-z0-9_]+(?<!_)$/,
        localesData?.USER_WEB?.MESSAGES?.USERNAME_PATTERN ||
          "Only lowercase letters, numbers, and underscores are allowed. Username cannot start or end with an underscore"
      ),
    email: Yup.string()
      .email(
        localesData?.USER_WEB?.MESSAGES?.INVALID_EMAIL_ADDRESS ||
          "Invalid email address"
      )
      .required(
        localesData?.USER_WEB?.MESSAGES?.EMAIL_REQUIRED || "Email is required"
      ),
    password: Yup.string()
      .required(
        localesData?.USER_WEB?.MESSAGES?.PASSWORD_REQUIRED ||
          "Password is required."
      )
      .matches(
        /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[A-Za-z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{6,}$/,
        localesData?.USER_WEB?.MESSAGES?.PASSWORD_PATTERN ||
          "Password must have at least one lowercase, one uppercase letter, one number, one special character, and be at least 6 characters long."
      ),
    agreeToPolicy: Yup.boolean()
      .oneOf(
        [true],
        localesData?.USER_WEB?.MESSAGES?.AGREE_POLICY ||
          "You must agree to the terms and conditions"
      )
      .required(
        localesData?.USER_WEB?.MESSAGES?.AGREE_POLICY ||
          "You must agree to the terms and conditions"
      ),
  });

  const initialValues = {
    name: "",
    username: "",
    email: "",
    password: "",
    agreeToPolicy: false,
  };

  const handleSubmit = async (values, { setSubmitting }) => {
    setIsLoading(true);
    setError(null);

    try {
      const formDataToSend = new FormData();
      formDataToSend.append("name", values.name);
      formDataToSend.append("username", values.username);
      formDataToSend.append("email", values.email);
      formDataToSend.append("password", values.password);

      const response = await apiInstance.post(URL.SIGNUP, formDataToSend);
      if (response?.data?.data?.token) {
        setApiMessage("success", response?.data.message);
        localStorage.setItem("UserToken", response.data.data.token);
        setTimeout(() => {
          window.location.href = "/type-selection";
        }, 1000);
      }
    } catch (error) {
      const errorMessage = error?.message;
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setSubmitting(false);
    }
  };

  const handleTermsClick = () => {
    window.open("https://flowkar.com/terms", "_blank");
  };

  const handlePrivacyClick = () => {
    window.open("https://flowkar.com/privacy-policy", "_blank");
  };

  const inputWrapStyle = "relative mb-5 mt-5";
  const legendStyle =
    "absolute text-sm text-[#1C1B1F] left-3 top-[2px] bg-white px-1 z-10 mb-4";
  const inputStyle =
    "w-full pl-[51px] py-4 mt-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#614036] focus:border-[#614036] focus:outline-none";

  return (
    <div className="h-screen overflow-y-auto">
      <div className="relative min-h-screen bg-white flex flex-col items-center px-4 font-Ubuntu max-w-[90%] mx-auto mb-2">
        <div className="logo absolute top-10 md:top-[80px] lg:top-[100px] xl:top-[150px] md:left-[137px] left-6 right-0 sm:h-36 sm:w-36 h-[115px] w-[115px] sm:mt-4 mt-2 z-50">
          <img src={siteConstant.SOCIAL_ICONS.FLOWKARLOGO} alt="logo" />
        </div>
        <div className="pointer-events-none absolute inset-0 z-0 ">
          <div className="absolute top-0 left-40 ">
            <img src={X} alt="X Logo" className="max-w-full h-auto" />
          </div>
          <div className="absolute top-[25%] left-0 ">
            <img
              src={facebookk}
              alt="Facebook Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-16 left-[15%] ">
            <img
              src={Instagram}
              alt="Instagram Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-0 left-[50%] ">
            <img
              src={facebook_Bottom}
              alt="Facebook Bottom Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-[10%] right-[30%] ">
            <img
              src={X_Right}
              alt="X Right Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-[5%] right-[10%] ">
            <img
              src={Pinterest_Right}
              alt="Pinterest Right Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute bottom-[50%] right-[2%] ">
            <img
              src={Youtube}
              alt="Youtube Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute top-[5%] right-[5%] ">
            <img
              src={Instagram_Top}
              alt="Instagram Top Logo"
              className="max-w-full h-auto"
            />
          </div>
          <div className="absolute top-[10%] right-[30%] ">
            <img src={X_Right} alt="X Top Logo" className="max-w-full h-auto" />
          </div>
          <div className="absolute top-[4%] right-[50%] ">
            <img src={Tiktok} alt="Tiktok Logo" className="max-w-full h-auto" />
          </div>
          <div className="absolute top-[25%] right-[50%] ">
            <img
              src={Pinterest}
              alt="Pinterest Logo"
              className="max-w-full h-auto"
            />
          </div>
          {/* Show only a few icons on mobile for less clutter */}
          <div className="absolute top-2 left-2 ">
            <img src={X} alt="X Logo" className="w-10 h-10" />
          </div>
          <div className="absolute bottom-2 right-2 ">
            <img src={Instagram} alt="Instagram Logo" className="w-10 h-10" />
          </div>
        </div>
        {/* Form and illustration container, z-10, relative */}
        <div className="relative z-10 w-full flex flex-col justify-center items-center md:flex-row md:-ml-7 mt-28 sm:mt-28 md:mt-36 lg:mt-[165px]  xl:mt-56">
          <div className="w-full md:w-[50%] max-w-md mx-auto md:ml-[133px] bg-white bg-opacity-90 rounded-xl p-4 md:p-0 shadow md:shadow-none">
            <h2 className="text-3xl text-[#614036] mb-2 z-50">
              <span className="font-bold">Sign</span> Up
            </h2>
            <p className="text-[#AA8882] mb-8 z-50">
              Let's get you all set up so you can access your personal account.
            </p>
            <Formik
              initialValues={initialValues}
              validationSchema={validationSchema}
              onSubmit={handleSubmit}
            >
              {({ errors, touched, isSubmitting }) => (
                <Form className="space-y-6">
                  {/* Name and Username fields */}
                  <div className="flex gap-5">
                    {/* Name */}
                    <div className="flex-1 relative">
                      <span className="absolute top-[18px] left-4 flex items-center pointer-events-none z-10">
                        <img src={User} alt="User" className="w-5 h-5" />
                      </span>
                      <Field name="name">
                        {({ field, form: { touched, errors } }) => (
                          <CustomTextField
                            {...field}
                            variant="outlined"
                            borderRadius="12px"
                            label={localesData?.USER_WEB?.NAME || "Name"}
                            fullWidth
                            type="text"
                            id="name"
                            placeholder=""
                            error={touched.name && Boolean(errors.name)}
                            helperText={touched.name && errors.name}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                paddingLeft: "45px",
                                "& fieldset": {
                                  borderRadius: "20px",
                                },
                                "&:hover fieldset": {},
                                "&.Mui-focused fieldset": {},
                              },
                              "& .MuiOutlinedInput-input": {
                                paddingLeft: "10px !important",
                                color: "#000000 !important",
                                fontWeight: "normal !important",
                                "&:-webkit-autofill": {
                                  WebkitTextFillColor: "#000000 !important",
                                  WebkitBoxShadow:
                                    "0 0 0 1000px white inset !important",
                                  fontWeight: "normal !important",
                                },
                              },
                              "& .MuiInputLabel-root": {
                                left: "35px",
                                transform: "translate(14px, 16px) scale(1)",
                                color: "#000000  !important",
                                "&.MuiInputLabel-shrink": {
                                  transform: "translate(5px, -9px) scale(0.75)",
                                },
                                "&.Mui-focused": {
                                  color: "#000000  !important",
                                },
                              },
                            }}
                          />
                        )}
                      </Field>
                    </div>

                    {/* Username */}
                    <div className="flex-1 relative">
                      <span className="absolute top-[18px] left-4 flex items-center pointer-events-none z-10">
                        <img src={User} alt="User" className="w-5 h-5" />
                      </span>
                      <Field name="username">
                        {({ field, form: { touched, errors } }) => (
                          <CustomTextField
                            {...field}
                            variant="outlined"
                            borderRadius="12px"
                            label={
                              localesData?.USER_WEB?.USERNAME || "Username"
                            }
                            fullWidth
                            type="text"
                            id="username"
                            placeholder=""
                            error={touched.username && Boolean(errors.username)}
                            helperText={touched.username && errors.username}
                            sx={{
                              "& .MuiOutlinedInput-root": {
                                paddingLeft: "45px",
                                "& fieldset": {
                                  borderRadius: "20px",
                                },
                                "&:hover fieldset": {},
                                "&.Mui-focused fieldset": {},
                              },
                              "& .MuiOutlinedInput-input": {
                                paddingLeft: "10px !important",
                                color: "#000000 !important",
                                fontWeight: "normal !important",
                                "&:-webkit-autofill": {
                                  WebkitTextFillColor: "#000000 !important",
                                  WebkitBoxShadow:
                                    "0 0 0 1000px white inset !important",
                                  fontWeight: "normal !important",
                                },
                              },
                              "& .MuiInputLabel-root": {
                                left: "35px",
                                transform: "translate(14px, 16px) scale(1)",
                                color: "#000000  !important",
                                "&.MuiInputLabel-shrink": {
                                  transform: "translate(5px, -9px) scale(0.75)",
                                },
                                "&.Mui-focused": {
                                  color: "#000000  !important",
                                },
                              },
                            }}
                          />
                        )}
                      </Field>
                    </div>
                  </div>

                  {/* Email field */}
                  <div className="relative">
                    <span className="absolute top-[18px] left-4 flex items-center pointer-events-none z-10">
                      <img src={Email} alt="Email" className="w-5 h-5" />
                    </span>
                    <Field name="email">
                      {({ field, form: { touched, errors } }) => (
                        <CustomTextField
                          {...field}
                          variant="outlined"
                          borderRadius="12px"
                          label={localesData?.USER_WEB?.EMAIL || "Email"}
                          fullWidth
                          type="email"
                          id="email"
                          placeholder=""
                          error={touched.email && Boolean(errors.email)}
                          helperText={touched.email && errors.email}
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              paddingLeft: "45px",
                              "& fieldset": {
                                borderRadius: "20px",
                              },
                              "&:hover fieldset": {},
                              "&.Mui-focused fieldset": {},
                            },
                            "& .MuiOutlinedInput-input": {
                              paddingLeft: "10px !important",
                              color: "#000000 !important",
                              fontWeight: "normal !important",
                              "&:-webkit-autofill": {
                                WebkitTextFillColor: "#000000 !important",
                                WebkitBoxShadow:
                                  "0 0 0 1000px white inset !important",
                                fontWeight: "normal !important",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              left: "35px",
                              transform: "translate(14px, 16px) scale(1)",
                              color: "#000000  !important",
                              "&.MuiInputLabel-shrink": {
                                transform: "translate(5px, -9px) scale(0.75)",
                              },
                              "&.Mui-focused": {
                                color: "#000000  !important",
                              },
                            },
                          }}
                        />
                      )}
                    </Field>
                  </div>

                  {/* Password field */}
                  <div className="relative">
                    <span className="absolute top-[18px] left-4 flex items-center pointer-events-none z-10">
                      <img src={Lock} alt="Lock" className="w-5 h-5" />
                    </span>
                    <Field name="password">
                      {({ field, form: { touched, errors } }) => (
                        <CustomTextField
                          {...field}
                          variant="outlined"
                          borderRadius="12px"
                          label={localesData?.USER_WEB?.PASSWORD || "Password"}
                          fullWidth
                          type={showPassword ? "text" : "password"}
                          id="password"
                          placeholder=""
                          error={touched.password && Boolean(errors.password)}
                          helperText={touched.password && errors.password}
                          sx={{
                            "& .MuiOutlinedInput-root": {
                              paddingLeft: "45px",
                              paddingRight: "45px",
                              "& fieldset": {
                                borderRadius: "20px",
                              },
                              "&:hover fieldset": {},
                              "&.Mui-focused fieldset": {},
                            },
                            "& .MuiOutlinedInput-input": {
                              paddingLeft: "10px !important",
                              paddingRight: "10px !important",
                              color: "#000000 !important",
                              fontWeight: "normal !important",
                              "&:-webkit-autofill": {
                                WebkitTextFillColor: "#000000 !important",
                                WebkitBoxShadow:
                                  "0 0 0 1000px white inset !important",
                                fontWeight: "normal !important",
                              },
                            },
                            "& .MuiInputLabel-root": {
                              left: "35px",
                              transform: "translate(14px, 16px) scale(1)",
                              color: "#000000  !important",
                              "&.MuiInputLabel-shrink": {
                                transform: "translate(5px, -9px) scale(0.75)",
                              },
                              "&.Mui-focused": {
                                color: "#000000  !important",
                              },
                            },
                          }}
                        />
                      )}
                    </Field>
                    <button
                      type="button"
                      className="absolute top-[18px] right-4 flex items-center z-10"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      <img
                        src={showPassword ? ShowPass : HidePass}
                        alt="Toggle Password"
                        className="h-[20px] w-[20px]"
                      />
                    </button>
                  </div>

                  {error && (
                    <p className="text-md text-red-500 font-medium pt-1 pb-2">
                      {error}
                    </p>
                  )}

                  <div className="my-4 flex flex-col items-start justify-center gap-2">
                    <div className="flex items-center justify-center gap-2">
                      <Field
                        type="checkbox"
                        name="agreeToPolicy"
                        className="h-4 w-4  text-[#614036] border-gray-300 rounded focus:ring-[#614036]"
                      />
                      <span className="text-gray-700 text-sm">
                        {localesData?.USER_WEB?.AGREE_TO_ALL ||
                          "I agree to all the"}
                        <span
                          className="font-medium cursor-pointer"
                          onClick={() => handleTermsClick()}
                        >
                          {" "}{localesData?.USER_WEB?.TERMS || "Terms"}
                        </span>{" "}
                        {localesData?.USER_WEB?.AND || "and"}
                        <span
                          className="font-medium cursor-pointer"
                          onClick={() => handlePrivacyClick()}
                        >
                          {" "}{localesData?.USER_WEB?.PRIVACY_POLICIES ||
                            "Privacy Policies"}
                        </span>
                      </span>
                    </div>
                    {touched.agreeToPolicy && errors.agreeToPolicy && (
                      <div className="text-red-500 text-sm mt-1">
                        {errors.agreeToPolicy}
                      </div>
                    )}
                  </div>

                  <div className="flex justify-center items-center">
                    <button
                      type="submit"
                      disabled={isSubmitting || isLoading}
                      className="w-[50%] bg-[#614036] text-white py-3 rounded-[12px] hover:bg-[#4a3026] transition-colors duration-200 mt-6"
                    >
                      {isLoading
                        ? localesData?.USER_WEB?.SIGNING_UP || "Signing up..."
                        : localesData?.USER_WEB?.SIGN_UP || "Sign Up"}
                    </button>
                  </div>
                </Form>
              )}
            </Formik>

            <div className="mt-6 text-center">
              <p className="text-gray-700">
                {localesData?.USER_WEB?.ALREADY_HAVE_ACCOUNT ||
                  "Already have an account?"}
                <Link
                  to="/sign-in"
                  className="text-[#614036] font-medium hover:underline"
                >
                  {localesData?.USER_WEB?.LOGIN || "Login"}
                </Link>
              </p>
            </div>
          </div>
          <div className="w-[50%]">
            <div className="flex justify-center items-center h-full">
              <img
                src={signUpIllustration}
                alt="Sign Up illustration"
                className="max-w-full h-auto hidden md:block"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUp;
