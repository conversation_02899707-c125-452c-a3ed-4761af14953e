import React, { useContext, useEffect, useState } from "react";
import { useNavigate, Link, useLocation } from "react-router-dom";
import { Formik, Form, Field } from "formik";
import * as Yup from "yup";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { URL } from "../../../../helpers/constant/Url";
import { setApiMessage } from "../../../../helpers/context/toaster";
import { CustomTextField } from "../../custom/CustomTextField";
import { InputAdornment } from "@mui/material";
import { Visibility, VisibilityOff } from "@mui/icons-material";
import { IntlContext } from "../../../../App";
import LoadingSpinner from "../../../../helpers/UI/LoadingSpinner";

// Import background images from Login/Signup pages
import X from "../../../../assets/images/Login/X.svg";
import facebook_Bottom from "../../../../assets/images/Login/Facebook_Bottom.svg";
import facebookk from "../../../../assets/images/Login/Facebookk.svg";
import Instagram_Top from "../../../../assets/images/Login/Instagram_Top.svg";
import Instagram from "../../../../assets/images/Login/Instagram.svg";
import Pinterest_Right from "../../../../assets/images/Login/Pinterest_Right.svg";
import Pinterest from "../../../../assets/images/Login/Pinterest.svg";
import Tiktok from "../../../../assets/images/Login/Tiktok.svg";
import X_Right from "../../../../assets/images/Login/X_Right.svg";
import Youtube from "../../../../assets/images/Login/Youtube.svg";
import ShowPass from "../../../../assets/images/ForgotPass/SeePass.svg";
import HidePass from "../../../../assets/images/ForgotPass/HidePass.svg";
import Lock from "../../../../assets/images/Login/Lock.svg";
// Import illustration for reset password
import resetPasswordIllustration from "../../../../assets/images/ForgotPass/ResetPass.svg";
import Arrow from "../../../../assets/images/ForgotPass/Arrow.svg";

const ResetPassword = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const [token, setToken] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [loading, setLoading] = useState(false);

  const locationState = location?.state;

  const toggleShowPassword = () => {
    setShowPassword(!showPassword);
  };
  const toggleShowConfirmPassword = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  useEffect(() => {
    if (!locationState?.token) {
      navigate("/forgot-password");
    } else {
      setToken(locationState?.token);
    }
  }, []);

  const inputWrapStyle = "relative mb-5 mt-5";
  const legendStyle =
    "absolute text-sm text-[#1C1B1F] left-3 -top-[10px] bg-white px-1 z-10 mb-4";
  // Adjusted input style to reduce the left padding
  const inputStyle =
    "w-full -ml-5 py-4 mt-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-[#614036] focus:border-[#614036]";

  // Style for the lock icon container with balanced spacing
  const lockIconStyle = "flex items-center justify-center w-8 h-8";

  return (
    <div className="min-h-screen bg-white flex items-center justify-center px-4 font-Ubuntu max-w-[90%] mx-auto">
      {/* Background floating icons - same as OTPVerify */}
      <div className="absolute top-0 left-40">
        <img src={X} alt="X Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[25%] left-0">
        <img
          src={facebookk}
          alt="Facebook Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-16 left-[15%]">
        <img
          src={Instagram}
          alt="Instagram Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-0 left-[50%]">
        <img
          src={facebook_Bottom}
          alt="Facebook Bottom Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-[10%] right-[30%]">
        <img src={X_Right} alt="X Right Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute bottom-[5%] right-[10%]">
        <img
          src={Pinterest_Right}
          alt="Pinterest Right Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-[50%] right-[2%]">
        <img src={Youtube} alt="Youtube Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[5%] right-[5%]">
        <img
          src={Instagram_Top}
          alt="Instagram Top Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute top-[10%] right-[30%]">
        <img src={X_Right} alt="X Right Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[4%] right-[50%]">
        <img src={Tiktok} alt="Tiktok Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[25%] right-[50%]">
        <img
          src={Pinterest}
          alt="Pinterest Logo"
          className="max-w-full h-auto"
        />
      </div>

      <div className="w-full flex flex-col justify-start items-center md:flex-row">
        {/* Left section - Reset Password form */}
        <div className="w-full md:w-[50%] max-w-md mx-auto md:ml-[120px]">
          <div className="mb-6">
            <h1 className="text-4xl font-bold text-[#614036]">
              Reset <span className=" font-normal">Password</span>
            </h1>
            <p className="text-[#AA8882] text-sm mt-3 mb-12 font-normal">
              Your previous password has been reset. Please set a new password
              for your account.
            </p>
          </div>

          <Formik
            initialValues={{ password: "", confirmPassword: "" }}
            validationSchema={Yup.object({
              password: Yup.string()
                .required(localesData?.USER_WEB?.MESSAGES?.PASSWORD_REQUIRED)
                .matches(
                  /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[A-Za-z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{6,}$/,
                  "Password must have at least one lowercase, one uppercase letter, one number, one special character, and be at least 6 characters long."
                ),
              confirmPassword: Yup.string()
                .required(
                  localesData?.USER_WEB?.MESSAGES?.CONFIRM_PASSWORD_REQUIRED
                )
                .oneOf(
                  [Yup.ref("password"), null],
                  localesData?.USER_WEB?.MESSAGES?.PASSWORD_MUST_MATCH
                ),
            })}
            onSubmit={async (values, { setSubmitting }) => {
              setLoading(true);
              try {
                const form = new FormData();
                form.append("new_password", values?.password);
                const { status, data } = await apiInstance.post(
                  URL.RESET_PASSWORD,
                  form,
                  {
                    headers: {
                      "Content-Type": "multipart/form-data",
                      Authorization: `Bearer ${token}`,
                    },
                  }
                );
                if (data) {
                  if (data?.status) {
                    setApiMessage("success", data?.message);
                    navigate("/sign-in", { replace: true });
                  } else {
                    setApiMessage("error", data?.message);
                  }
                }
              } catch (error) {
                setApiMessage("error", error?.message);
              } finally {
                setLoading(false);
                setSubmitting(false);
              }
            }}
          >
            {({
              errors,
              handleBlur,
              handleChange,
              handleSubmit,
              touched,
              values,
              isSubmitting,
            }) => (
              <form onSubmit={handleSubmit}>
                <div className="mb-6 relative">
                  <span className="absolute top-[18px] left-4 flex items-center pointer-events-none z-10">
                    <img src={Lock} alt="Lock" className="w-5 h-5" />
                  </span>
                  <Field
                    as={CustomTextField}
                    variant="outlined"
                    borderRadius="12px"
                    label="Password"
                    fullWidth
                    type={showPassword ? "text" : "password"}
                    id="password"
                    name="password"
                    placeholder=""
                    value={values.password}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={touched.password && Boolean(errors.password)}
                    helperText={touched.password && errors.password}
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        paddingLeft: "45px",
                        paddingRight: "45px",
                        "& fieldset": {
                          borderRadius: "20px",
                        },
                      },
                      "& .MuiOutlinedInput-input": {
                        paddingLeft: "10px !important",
                        paddingRight: "10px !important",
                        color: "#000000 !important",
                        fontWeight: "normal !important",
                        "&:-webkit-autofill": {
                          WebkitTextFillColor: "#000000 !important",
                          WebkitBoxShadow:
                            "0 0 0 1000px white inset !important",
                          fontWeight: "normal !important",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        left: "35px",
                        color: "#000000 !important", // Keep label black
                        transform: "translate(14px, 16px) scale(1)",
                        "&.MuiInputLabel-shrink": {
                          transform: "translate(5px, -9px) scale(0.75)",
                          color: "#000000 !important", // Keep shrunk label black
                        },
                        "&.Mui-error": {
                          color: "#000000 !important", // Keep error state label black
                        },
                        "&.Mui-focused": {
                          color: "#000000 !important", // Keep focused label black
                        },
                      },
                      "& .MuiFormHelperText-root": {
                        marginLeft: "35px",
                        marginTop: "4px",
                      },
                    }}
                  />
                  <span className="absolute top-[18px] right-4 flex items-center z-50">
                    <button
                      type="button"
                      className="flex items-center"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <img
                          src={ShowPass}
                          alt=""
                          className="h-[20px] w-[20px]"
                        />
                      ) : (
                        <img
                          src={HidePass}
                          alt=""
                          className="h-[20px] w-[20px]"
                        />
                      )}
                    </button>
                  </span>
                </div>
                <div className="mb-6 relative">
                  <span className="absolute top-[18px] left-4 flex items-center pointer-events-none z-10">
                    <img src={Lock} alt="Lock" className="w-5 h-5" />
                  </span>
                  <Field
                    as={CustomTextField}
                    variant="outlined"
                    borderRadius="12px"
                    label="Confirm Password"
                    fullWidth
                    type={showConfirmPassword ? "text" : "password"}
                    id="confirmPassword"
                    name="confirmPassword"
                    placeholder=""
                    value={values.confirmPassword}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    error={
                      touched.confirmPassword && Boolean(errors.confirmPassword)
                    }
                    helperText={
                      touched.confirmPassword && errors.confirmPassword
                    }
                    sx={{
                      "& .MuiOutlinedInput-root": {
                        paddingLeft: "45px",
                        paddingRight: "45px",
                        "& fieldset": {
                          borderRadius: "20px",
                        },
                      },
                      "& .MuiOutlinedInput-input": {
                        paddingLeft: "10px !important",
                        paddingRight: "10px !important",
                        color: "#000000 !important",
                        fontWeight: "normal !important",
                        "&:-webkit-autofill": {
                          WebkitTextFillColor: "#000000 !important",
                          WebkitBoxShadow:
                            "0 0 0 1000px white inset !important",
                          fontWeight: "normal !important",
                        },
                      },
                      "& .MuiInputLabel-root": {
                        left: "35px",
                        color: "#000000 !important", // Keep label black
                        transform: "translate(14px, 16px) scale(1)",
                        "&.MuiInputLabel-shrink": {
                          transform: "translate(5px, -9px) scale(0.75)",
                          color: "#000000 !important", // Keep shrunk label black
                        },
                        "&.Mui-error": {
                          color: "#000000 !important", // Keep error state label black
                        },
                        "&.Mui-focused": {
                          color: "#000000 !important", // Keep focused label black
                        },
                      },
                      "& .MuiFormHelperText-root": {
                        marginLeft: "35px",
                        marginTop: "4px",
                      },
                    }}
                  />
                  <span className="absolute top-[18px] right-4 flex items-center z-50">
                    <button
                      type="button"
                      className="flex items-center"
                      onClick={() =>
                        setShowConfirmPassword(!showConfirmPassword)
                      }
                    >
                      {showConfirmPassword ? (
                        <img
                          src={ShowPass}
                          alt=""
                          className="h-[20px] w-[20px]"
                        />
                      ) : (
                        <img
                          src={HidePass}
                          alt=""
                          className="h-[20px] w-[20px]"
                        />
                      )}
                    </button>
                  </span>
                </div>

                <div className="flex justify-center items-center">
                  <button
                    type="submit"
                    className={`w-[50%] py-3 rounded-[12px] font-medium transition duration-300 mt-[20px] ${
                      loading
                        ? "bg-gray-400 cursor-not-allowed"
                        : "bg-[#614036] hover:bg-[#7a5046] text-white"
                    }`}
                    disabled={loading}
                  >
                    {loading ? (
                      <LoadingSpinner
                        text="Updating..."
                        spinnerSize="h-4 w-4"
                        textColor="text-white"
                      />
                    ) : (
                      "Reset"
                    )}
                  </button>
                </div>
              </form>
            )}
          </Formik>
        </div>

        {/* Right section - illustration */}
        <div className="hidden md:block w-[50%]">
          <div className="flex justify-center items-center h-full">
            <img
              src={resetPasswordIllustration}
              alt="Reset password illustration"
              className="max-w-full h-auto"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
