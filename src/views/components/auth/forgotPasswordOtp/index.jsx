import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate, Link } from "react-router-dom";
import Arrow from "../../../../assets/images/ForgotPass/Arrow.svg";
import LoadingSpinner from "../../../../helpers/UI/LoadingSpinner";
import { setApiMessage } from "../../../../helpers/context/toaster";

// Import background images from Login/Signup pages
import X from "../../../../assets/images/Login/X.svg";
import facebook_Bottom from "../../../../assets/images/Login/Facebook_Bottom.svg";
import facebookk from "../../../../assets/images/Login/Facebookk.svg";
import Instagram_Top from "../../../../assets/images/Login/Instagram_Top.svg";
import Instagram from "../../../../assets/images/Login/Instagram.svg";
import Pinterest_Right from "../../../../assets/images/Login/Pinterest_Right.svg";
import Pinterest from "../../../../assets/images/Login/Pinterest.svg";
import Tiktok from "../../../../assets/images/Login/Tiktok.svg";
import X_Right from "../../../../assets/images/Login/X_Right.svg";
import Youtube from "../../../../assets/images/Login/Youtube.svg";

// Import OTP verification illustration
import otpVerificationIllustration from "../../../../assets/images/ForgotPass/VerifyOTP.svg";
import apiInstance from "../../../../helpers/Axios/axiosINstance";
import { URL } from "../../../../helpers/constant/Url";

// Import API utilities

const OTPVerify = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [loadingResend, setLoadingResend] = useState(false);
  const [otp, setOtp] = useState(["", "", "", "", "", ""]); // 6 digits
  const [email, setEmail] = useState("");
  const [timer, setTimer] = useState(60); // 60 seconds timer
  const [canResend, setCanResend] = useState(false);
  const inputRefs = [
    useRef(),
    useRef(),
    useRef(),
    useRef(),
    useRef(),
    useRef(),
  ];

  // Timer effect
  useEffect(() => {
    let interval;
    if (timer > 0) {
      interval = setInterval(() => {
        setTimer((prevTimer) => prevTimer - 1);
      }, 1000);
    } else {
      setCanResend(true);
    }
    return () => clearInterval(interval);
  }, [timer]);

  // Function to set API response messages
  // const setApiMessage = (type, message) => {
  //   // Implement your message display logic here
  //   // This could be a toast notification or alert
  //   if (type === "error") {
  //     setApiMessage(`Error: ${message}`);
  //   } else if (type === "success") {
  //     setApiMessage(`Success: ${message}`);
  //   }
  // };

  useEffect(() => {
    // Get email from location state
    if (location.state && location.state.email) {
      setEmail(location.state.email);
    } else {
      // Redirect to forgot password if no email is provided
      navigate("/forgot-password", { replace: true });
    }

    // Focus on first input when component mounts
    inputRefs[0].current?.focus();
  }, [location, navigate]);

  const handleInputChange = (index, value) => {
    // Allow only numbers
    if (!/^\d*$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value;
    setOtp(newOtp);

    // Auto focus next input when current is filled
    if (value && index < 5) {
      inputRefs[index + 1].current?.focus();
    }
  };

  const handleKeyDown = (index, e) => {
    // Move to previous input on backspace if current is empty
    if (e.key === "Backspace" && !otp[index] && index > 0) {
      inputRefs[index - 1].current?.focus();
    }
  };

  const handlePaste = (e) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData("text/plain").trim();

    // Check if pasted content is a 6-digit number
    if (/^\d{6}$/.test(pastedData)) {
      const digits = pastedData.split("");
      setOtp(digits);

      // Focus on the last input
      inputRefs[5].current?.focus();
    }
  };

  const handleResend = async () => {
    if (!canResend) return;

    setLoadingResend(true);
    try {
      const form = new FormData();
      form.append("email", email);

      const { status, data } = await apiInstance.post(
        URL.FORGOT_PASSWORD,
        form,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );

      if (data) {
        if (data?.status) {
          setApiMessage("success", data?.message);
          // Reset timer and disable resend
          setTimer(60);
          setCanResend(false);
        } else {
          setApiMessage("error", data?.message);
        }
      }
    } catch (error) {
      setApiMessage("error", error?.message);
    } finally {
      setLoadingResend(false);
    }
  };

  const handleVerify = async () => {
    // Check if all fields are filled
    if (otp.some((digit) => digit === "")) {
      setApiMessage("Please enter the complete verification code");
      return;
    }

    setLoading(true);

    try {
      const form = new FormData();
      form.append("email", email);
      form.append("user_otp", otp.join("")); // Join all digits to create the complete OTP

      const { status, data } = await apiInstance.post(URL.OTP_VERIFY, form, {
        headers: {
          "Content-Type": "multipart/form-data",
        },
      });

      console.log("data", data);

      if (data) {
        if (data?.status) {
          setApiMessage("success", data?.message);
          navigate("/reset-password", {
            state: { token: data?.token },
            replace: true,
          });
        } else {
          setApiMessage("error", data?.message);
        }
      }
    } catch (error) {
      setApiMessage("error", error?.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center px-4 font-Ubuntu max-w-[90%] mx-auto">
      {/* Background floating icons - same as login/signup pages */}
      <div className="absolute top-0 left-40">
        <img src={X} alt="X Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[25%] left-0">
        <img
          src={facebookk}
          alt="Facebook Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-16 left-[15%]">
        <img
          src={Instagram}
          alt="Instagram Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-0 left-[50%]">
        <img
          src={facebook_Bottom}
          alt="Facebook Bottom Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-[10%] right-[30%]">
        <img src={X_Right} alt="X Right Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute bottom-[5%] right-[10%]">
        <img
          src={Pinterest_Right}
          alt="Pinterest Right Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute bottom-[50%] right-[2%]">
        <img src={Youtube} alt="Youtube Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[5%] right-[5%]">
        <img
          src={Instagram_Top}
          alt="Instagram Top Logo"
          className="max-w-full h-auto"
        />
      </div>
      <div className="absolute top-[10%] right-[30%]">
        <img src={X_Right} alt="X Right Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[4%] right-[50%]">
        <img src={Tiktok} alt="Tiktok Logo" className="max-w-full h-auto" />
      </div>
      <div className="absolute top-[25%] right-[50%]">
        <img
          src={Pinterest}
          alt="Pinterest Logo"
          className="max-w-full h-auto"
        />
      </div>

      <div className="w-full flex flex-col justify-start items-center md:flex-row">
        {/* Left section - OTP form (styled like the image) */}
        <div className="w-full md:w-[50%] max-w-md mx-auto md:ml-[120px]">
          <Link
            to="/sign-in"
            className="flex items-center mb-6 text-[#563D39] hover:no-underline"
          >
            <img src={Arrow} alt="Arrow" className="max-w-full h-auto mr-2" />
            Back to login
          </Link>

          <div className="mb-10">
            <h1 className="text-4xl font-normal text-[#614036]">
              Enter <span className="font-bold">Code</span>
            </h1>
            <p className="text-[#AA8882] text-sm mt-3 mb-6 font-normal">
              Please enter the verification code that we have sent on your
              registered email.
            </p>
          </div>

          <div className="mb-4">
            <div className="flex space-x-2 justify-center md:justify-start">
              {otp.map((digit, index) => (
                <input
                  key={index}
                  ref={inputRefs[index]}
                  type="text"
                  maxLength={1}
                  value={digit}
                  onChange={(e) => handleInputChange(index, e.target.value)}
                  onKeyDown={(e) => handleKeyDown(index, e)}
                  onPaste={index === 0 ? handlePaste : undefined}
                  className="w-12 h-12 text-center text-lg font-medium border border-gray-300 rounded-lg focus:ring-2 focus:ring-[#614036] focus:border-[#614036] focus:outline-none"
                />
              ))}
            </div>
          </div>

          <div className="flex justify-center md:justify-start text-center mb-12">
            <p className="text-sm text-gray-600">
              Didn't receive a code?{" "}
              <button
                onClick={handleResend}
                disabled={!canResend || loadingResend}
                className={`font-medium ${
                  canResend && !loadingResend
                    ? "text-[#614036] hover:text-[#563D39]"
                    : "text-gray-400 cursor-not-allowed"
                }`}
              >
                {loadingResend
                  ? "Resending..."
                  : canResend
                  ? "Resend"
                  : `Resend in ${timer}s`}
              </button>
            </p>
          </div>

          <div className="flex justify-center items-center">
            <button
              onClick={handleVerify}
              className={`w-[50%] py-3 rounded-[12px] font-medium transition duration-300 ${
                loading
                  ? "bg-gray-400 cursor-not-allowed"
                  : "bg-[#614036] hover:bg-[#7a5046] text-white"
              }`}
              disabled={loading}
            >
              {loading ? (
                <LoadingSpinner
                  text="Verifying..."
                  spinnerSize="h-4 w-4"
                  textColor="text-white"
                />
              ) : (
                "Verify"
              )}
            </button>
          </div>
        </div>

        {/* Right section - illustration (from the image) */}
        <div className="hidden md:block w-[50%]">
          <div className="flex justify-center items-center h-full">
            <img
              src={otpVerificationIllustration}
              alt="OTP verification illustration"
              className="max-w-full h-auto"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default OTPVerify;
