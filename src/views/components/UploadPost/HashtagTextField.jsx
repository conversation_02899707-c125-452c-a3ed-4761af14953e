import React, {
  useState,
  useRef,
  useEffect,
  useContext,
  useCallback,
} from "react";
import { useFormikContext } from "formik";
import { FormControl, MenuItem, InputLabel } from "@mui/material";

import { SocketContext } from "../../../helpers/context/socket";
import { debounce } from "lodash";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import { URL } from "../../../helpers/constant/Url";
import { CustomTextField } from "../custom/CustomTextField";

let endValue = "",
  startValue,
  passer = false;

const HashtagTextField = ({
  suggestionData,
  setActiveInput,
  activeInput,
  onChange,
  removeText,
  selectedIDs,
  setselectedIDs,
  compareDataForRemove,
  handleRemovedNames,
  mainDataDis,
}) => {
  const [focused, setFocused] = useState(false);
  const [suggestions, setSuggestions] = useState(suggestionData || []);
  const textareaRef = useRef(null);
  const socket = useContext(SocketContext);
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const { values, setFieldValue } = useFormikContext();
  const [myData, setmyData] = useState();

  const debouncedHandleChange = useCallback(
    debounce((inputValue) => {
      const match = inputValue.match(/@(\w*)$/);
      if (match?.[1]) {
        socket?.emit("search_user", {
          Authorization: token,
          search_text: match[1],
        });
      } else {
        setSuggestions([]);
      }
    }, 300),
    [socket, token]
  );
  function handleRemovedNames(value) {
    let identyFireIndex = [];
    for (var i = 0; i < compareDataForRemove?.length; i++) {
      compareDataForRemove[i].name = compareDataForRemove[i].name?.trimEnd();
    }

    for (var i = 0; i < compareDataForRemove?.length; i++) {
      for (var j = 0; j < value?.length; j++) {
        if (
          compareDataForRemove[i]?.name[0] == value[j] &&
          value[j - 1] == "@"
        ) {
          for (var k = 0; k < compareDataForRemove[i].name.length; k++) {
            if (compareDataForRemove[i].name[k] == value[j + k]) {
              if (
                k == compareDataForRemove[i].name.length - 1 &&
                !identyFireIndex.includes(compareDataForRemove[i])
              ) {
                if (value[j + k + 2] == undefined || value[j + k + 1] == " ")
                  identyFireIndex[identyFireIndex.length] =
                    compareDataForRemove[i];
                removeText(identyFireIndex);
              }
            } else {
              removeText(identyFireIndex);
              break;
            }
          }
        } else {
          removeText(identyFireIndex);
        }
      }
    }
  }

  const handleChange = useCallback(
    (e) => {
      const inputValue = e.target.value;

      handleRemovedNames(inputValue);

      onChange(inputValue);
      debouncedHandleChange(inputValue);
    },
    [debouncedHandleChange, onChange]
  );

  const handleSelectSuggestion = (suggestion) => {
    if (selectedIDs.includes(suggestion.id)) {
      return;
    }
    const updatedValue = values?.description?.replace(
      /@\w*$/,
      `@${suggestion.name} `
    );
    setselectedIDs((prevIds) => [...prevIds, suggestion.id]);
    onChange(updatedValue, suggestion);
    setSuggestions([]);
    handleRemovedNames();
  };

  useEffect(() => {
    const handleReceiveSuggestions = (response) => {
      setmyData(response.data);
      setSuggestions(response?.status && response?.data ? response.data : []);
    };

    socket?.on("search_user", handleReceiveSuggestions);
    return () => {
      socket?.off("search_user", handleReceiveSuggestions);
    };
  }, [socket]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = "45px";
    }
    handleRemovedNames(values.description);
  }, [values.description]);

  const showSuggestions =
    activeInput === "hashtag" &&
    suggestions.length > 0 &&
    values.description.includes("@") &&
    values.description.match(/@(\w+)$/)?.[1];

  return (
    <FormControl fullWidth variant="outlined" style={{ position: "relative" }}>
      <InputLabel
        htmlFor="hashtag-textfield"
        className="description-inp font-Ubuntu"
        style={{
          position: "absolute",
          background: "white",
          padding: focused
            ? "0px 10px 0px 10px"
            : !values.description.length > 0
            ? "0px 10px 20px 10px"
            : "0px 10px 0px 10px",
          top: focused || values.description.length > 0 ? "-8px" : "50%",
          left: "12px",
          color: "#674941",
          fontFamily: "Ubuntu, sans-serif",
          transform:
            focused || values.description.length > 0
              ? "none"
              : "translateY(-50%)",
          fontSize: focused || values.description.length > 0 ? "11px" : "14px",
          fontWeight: focused || values.description.length > 0 ? "600" : "",
        }}
      >
        Tag People
      </InputLabel>
      <CustomTextField
        id="hashtag-textfield"
        value={values.description}
        onChange={handleChange}
        inputRef={textareaRef}
        multiline
        rows={1}
        variant="outlined"
        fullWidth
        onFocus={(e) => {
          setFocused(true);
          setActiveInput("hashtag");
          handleRemovedNames(e.target.value);
        }}
        onBlur={() => setFocused(false)}
        sx={{
          "& .MuiInputBase-input": {
            color: "black",
            caretColor: "black",
            fontFamily: "Ubuntu, sans-serif",
            padding: "10px",
          },
          "& fieldset": {
            borderColor: focused ? "#4582c3" : "#D4D6D8",
          },
          "&:hover fieldset": {
            borderColor: "#4582c3",
          },
          "&.Mui-focused fieldset": {
            borderColor: "#4582c3",
          },
        }}
      />

      {showSuggestions && (
        <div
          style={{
            position: "absolute",
            top: "100%",
            left: 0,
            width: "100%",
            backgroundColor: "white",
            zIndex: 99,
            maxHeight: "150px",
            overflowY: "auto",
            border: "1px solid #ccc",
            borderRadius: "4px",
          }}
        >
          {suggestions.map((suggestion, index) => (
            <MenuItem
              key={index}
              onClick={() => handleSelectSuggestion(suggestion)}
              disabled={selectedIDs.includes(suggestion.id)}
              className="sm:fixed lg:hidden font-Ubuntu"
              sx={{
                display: "flex",
                alignItems: "center",

                "&:hover": {
                  backgroundColor: "#ffe9e9",
                },
              }}
            >
              <img
                src={
                  suggestion.profile
                    ? `${URL.SOCKET_URL}${suggestion.profile}`
                    : siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                }
                alt={suggestion.name}
                className={`h-10 w-10 rounded-[14px] border-[2px] p-1 border-[#674941 ]`}
              />
              <div
                className="ml-2"
                style={{ display: "flex", flexDirection: "column" }}
              >
                <p style={{ margin: 0, fontWeight: "bold", fontSize: "14px" }}>
                  {suggestion.name}
                </p>
                <p style={{ margin: 0, color: "gray", fontSize: "11px" }}>
                  @{suggestion.username}
                </p>
              </div>
            </MenuItem>
          ))}
        </div>
      )}
    </FormControl>
  );
};

export default HashtagTextField;
