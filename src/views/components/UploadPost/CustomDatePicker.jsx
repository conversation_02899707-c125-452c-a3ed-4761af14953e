// VERSION 1: Date Only Picker (for birth dates, etc.)
import React, { useState } from "react";
import { TextField } from "@mui/material";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";

const CustomDatePicker = ({ label, maxDate, value, onChange }) => {
  const [selectedDate, setSelectedDate] = useState(value || null);

  const handleDateChange = (newValue) => {
    setSelectedDate(newValue);
    onChange({ target: { name: "dateOfBirth", value: newValue } });
  };

  // Set maxDate to current date if not provided
  const effectiveMaxDate = maxDate || dayjs();

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DatePicker
        label={label}
        value={selectedDate}
        onChange={handleDateChange}
        maxDate={effectiveMaxDate}
        disableFuture={true} // Prevent selecting future dates
        format="YYYY-MM-DD"
        slotProps={{
          textField: {
            variant: "outlined",
            fullWidth: true,
            id: "dateOfBirth",
            name: "dateOfBirth",
            InputLabelProps: { shrink: true },
            inputProps: {
              readOnly: true,
              style: {
                backgroundColor: "#f5f5f5",
                color: "#333",
                padding: "10px",
                borderRadius: "20px",
              },
            },
          },
          popper: {
            placement: "bottom-start",
          },
        }}
      />
    </LocalizationProvider>
  );
};

// VERSION 2: DateTime Picker (if you need time restrictions too)
/*
import { DateTimePicker } from "@mui/x-date-pickers/DateTimePicker";

const CustomDateTimePicker = ({ label, maxDate, value, onChange }) => {
  const [selectedDateTime, setSelectedDateTime] = useState(value || null);

  const handleDateTimeChange = (newValue) => {
    setSelectedDateTime(newValue);
    onChange({ target: { name: "dateOfBirth", value: newValue } });
  };

  // Set max to current date and time
  const now = dayjs();

  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <DateTimePicker
        label={label}
        value={selectedDateTime}
        onChange={handleDateTimeChange}
        maxDateTime={now} // Prevents selecting future date/time
        format="YYYY-MM-DD HH:mm"
        slotProps={{
          textField: {
            variant: "outlined",
            fullWidth: true,
            InputLabelProps: { shrink: true },
            inputProps: {
              readOnly: true,
              style: {
                backgroundColor: "#f5f5f5",
                color: "#333",
                padding: "10px",
                borderRadius: "20px",
              },
            },
          },
          popper: {
            placement: "bottom-start",
          },
        }}
      />
    </LocalizationProvider>
  );
};
*/

export default CustomDatePicker;