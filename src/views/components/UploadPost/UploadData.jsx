// import React, { useRef, useState } from 'react';
// import { Dialog } from '@headlessui/react';
// import Slider from 'react-slick';
// import 'slick-carousel/slick/slick.css';
// import 'slick-carousel/slick/slick-theme.css';
// import postimg from '../../assets/images/postimg1.svg';
// import pofileicon from '../../assets/images/recentimg.png';
// import { Button, TextField } from '@mui/material';
// import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined';
// import KeyboardArrowRightIcon from '@mui/icons-material/KeyboardArrowRight';
// import { useNavigate } from 'react-router-dom';

// const UploadPost = ({ open, onClose }) => {
//   const fileInputRef = useRef(null);
//   const navigate = useNavigate();

//   const handleBackdropClick = (e) => {
//     if (e.target === e.currentTarget) {
//       onClose();
//     }
//   };

//   const handleFileInputClick = () => {
//     fileInputRef.current.click();
//   };

//   const handleFileChange = (event) => {
//     const files = event.target.files;
//     if (files.length > 0) {
//       navigate('/uploaddata');
//     }
//   };

//   const images = [
//     'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRp0klPwYCPP7ckPaa_s5RVk70uszGHCza1Kg&s',
//     'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRp0klPwYCPP7ckPaa_s5RVk70uszGHCza1Kg&s',
//     'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRp0klPwYCPP7ckPaa_s5RVk70uszGHCza1Kg&s',
//     'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRp0klPwYCPP7ckPaa_s5RVk70uszGHCza1Kg&s',
//   ];

//   const settings = {
//     infinite: true,
//     speed: 500,
//     slidesToShow: 1,
//     slidesToScroll: 1,
//   };

//   return (
//     <Dialog open={open} onClose={onClose} className="fixed inset-0 z-10 flex items-center justify-center">
//       <div className="fixed inset-0 bg-gray-600 bg-opacity-75 transition-opacity" aria-hidden="true" onClick={handleBackdropClick}></div>
//       <div className="relative bg-white rounded-3xl overflow-hidden shadow-xl transform transition-all sm:max-w-lg sm:w-full p-8" onClick={(e) => e.stopPropagation()}>
//         <div className="border border-5 border-gray-400 border-dashed rounded-xl p-6 cursor-pointer font-Ubuntu">
//           <div className="bg-white px-4 pt-5 pb-2 sm:p-6 sm:pb-4" onClick={handleFileInputClick}>
//             <div className="sm:flex sm:items-center sm:justify-center">
//               <div className="">
//                 <p className="sm:text-3xl font-bold text-center pb-5 text-gray-900">Create new post</p>
//                 <div className="mt-2 flex flex-col justify-center items-center">
//                   <img src={postimg} alt="Post" className="w-24 h-24" />
//                   <p className="text-sm mt-4 font-semibold">Drag photos and videos here</p>
//                 </div>
//               </div>
//             </div>
//           </div>
//           {/* Handle file input */}
//           <div className="px-4 py-3 sm:px-4 flex justify-center" onClick={handleFileInputClick}>
//             <label htmlFor="upload-input" className="bg-red-500 px-4 py-3 rounded-xl text-white text-sm font-semibold cursor-pointer">
//               Select your Photos
//             </label>
//             <input
//               id="upload-input"
//               type="file"
//               ref={fileInputRef}
//               style={{ display: 'none' }}
//               onChange={handleFileChange}
//               multiple
//             />
//           </div>
//         </div>
//       </div>
//     </Dialog>
//   );
// };

// export default UploadPost;
