import React from "react";

const Highlight = ({ text }) => {
  const highlightText = (text) => {
    const regex = /(@\w+|#\w+)/g;

    const parts = text.split(regex);

    return parts.map((part, index) => {
      if (part.startsWith("#") || part.startsWith("@")) {
        return (
          <span key={index} style={{ color: "blue" }}>
            {part}
          </span>
        );
      }
      return part;
    });
  };

  return (
    <div
      className="whitespace-pre-wrap font-Ubuntu text-sm"
      style={{
        color: "transparent",
        whiteSpace: "pre-wrap",
        overflowWrap: "break-word",
        zIndex: 1,
      }}
    >
      {highlightText(text)}
    </div>
  );
};

export default Highlight;
