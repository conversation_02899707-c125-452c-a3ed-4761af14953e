import React from "react";
import {
  Box,
  Card,
  CardContent,
  CardMedia,
  Typography,
  Container,
  Avatar,
  CardHeader,
  IconButton,
} from "@mui/material";
import FavoriteIcon from "@mui/icons-material/Favorite";
import CommentIcon from "@mui/icons-material/Comment";
import ShareIcon from "@mui/icons-material/Share";
import MoreVertIcon from "@mui/icons-material/MoreVert";

const feeds = [
  {
    id: 1,
    username: "john_doe",
    avatar: "https://via.placeholder.com/150",
    image: "https://via.placeholder.com/600x400",
    caption: "Enjoying the beautiful sunset!",
    likes: 123,
    comments: 45,
  },
  {
    id: 2,
    username: "jane_doe",
    avatar: "https://via.placeholder.com/150",
    image: "https://via.placeholder.com/600x400",
    caption: "Lovely day for a hike!",
    likes: 89,
    comments: 20,
  },
  // Add more feed items here
];

const HomePage = () => {
  return (
    <Container component="main" maxWidth="sm">
      <Box
        sx={{
          marginTop: 8,
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
        }}
      >
        {feeds.map((feed) => (
          <Card key={feed.id} className="mb-4 w-full">
            <CardHeader
              avatar={<Avatar src={feed.avatar} />}
              action={
                <IconButton aria-label="settings">
                  <MoreVertIcon />
                </IconButton>
              }
              title={feed.username}
            />
            <CardMedia
              component="img"
              height="400"
              image={feed.image}
              alt={feed.caption}
            />
            <CardContent>
              <Typography variant="body2" color="text.secondary">
                {feed.caption}
              </Typography>
              <Box
                sx={{ display: "flex", justifyContent: "space-between", mt: 2 }}
              >
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <IconButton aria-label="add to favorites">
                    <FavoriteIcon />
                  </IconButton>
                  <Typography variant="body2">{feed.likes}</Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center" }}>
                  <IconButton aria-label="comment">
                    <CommentIcon />
                  </IconButton>
                  <Typography variant="body2">{feed.comments}</Typography>
                </Box>
                <IconButton aria-label="share">
                  <ShareIcon />
                </IconButton>
              </Box>
            </CardContent>
          </Card>
        ))}
      </Box>
    </Container>
  );
};

export default HomePage;
