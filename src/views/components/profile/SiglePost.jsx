import React, { useContext, useEffect, useState } from "react";
import Slider from "react-slick";
import siteConstant from "../../../helpers/constant/siteConstant";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa";
import { Popover, Typography } from "@mui/material";
import Loader from "../../../helpers/UI/Loader";
import { IntlContext } from "../../../App";
import Spinner from "../../../helpers/UI/Spinner";
import { URL } from "../../../helpers/constant/Url";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { setApiMessage } from "../../../helpers/context/toaster";
import { renderHighlightedText } from "../../../helpers/context/common";
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import "./SinglePost.css";
import { ensureHttps } from "../../../helpers/constant/utils";

function SinglePost({ userData, post, tagData, draftData, activeTab }) {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [loading, setLoading] = useState(false);
  const [singleData, setSingleData] = useState([]);
  const [shareId, setShareId] = useState(null);
  const [showMore, setShowMore] = useState(false);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const postUrl = `https://flowkar.com/get-post/?post_id=${shareId}`;

  const handleCopyLink = () => {
    navigator.clipboard
      .writeText(postUrl)
      .then(() => {
        setApiMessage("success", "Link copied to clipboard");
      })
      .catch((err) => {
        console.error("Failed to copy: ", err);
      });
  };

  const truncateDescription = (text, limit) => {
    if (text.split(" ").length > limit) {
      return text.split(" ").slice(0, limit).join(" ") + "...";
    }
    return text;
  };

  const shareSinglePost = async () => {
    try {
      const form = new FormData();
      form.append("post_id", post.id);
      const response = await apiInstance.post(URL.SHARE_SINGLE_POST_WEB, form);
      setShareId(response.data?.new_id);
    } catch (error) {
      console.error("Error fetching profile:", error);
      setApiMessage("error", error?.message);
    }
  };

  useEffect(() => {
    shareSinglePost();
  }, []);
  const fetchSinglePost = async () => {
    setLoading(true);
    try {
      const { data } = await apiInstance.get(
        `${URL.ANALYTICS_DATA}?post_id=${post.id}`
      );
      setSingleData(data?.data);
    } catch (error) {
      setApiMessage("error", error?.message);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchSinglePost();
  }, []);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const isVideoFile = (fileUrl) => {
    const videoExtensions = ["mp4", "mov", "avi", "mkv"];
    const fileExtension = fileUrl.split(".").pop().toLowerCase();
    return videoExtensions.includes(fileExtension);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const CustomNextArrow = ({ onClick }) => (
    <div
      className="absolute top-1/2 right-[-15px] transform -translate-y-1/2 z-10 cursor-pointer bg-[#efebe9] p-2 rounded-full shadow-lg"
      onClick={onClick}
    >
      <FaChevronRight className="text-[#563D39]" />
    </div>
  );

  const CustomPrevArrow = ({ onClick }) => (
    <div
      className="absolute top-1/2 left-[-19px] transform -translate-y-1/2 z-10 cursor-pointer bg-[#efebe9] p-2 rounded-full shadow-lg"
      onClick={onClick}
    >
      <FaChevronLeft className="text-[#563D39]" />
    </div>
  );

  const sliderSettings = {
    infinite: true,
    dots: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <CustomNextArrow />,
    prevArrow: <CustomPrevArrow />,
    dotsClass: "slick-dots custom-dots",
    appendDots: (dots) => (
      <div
        style={{
          position: "absolute",
          bottom: "-15px",
          display: "flex",
          justifyContent: "center",
          width: "100%",
        }}
      >
        <ul style={{ margin: "0", padding: "0" }}> {dots} </ul>
      </div>
    ),
  };

  // Helper function to render media controls section
  const renderMediaControls = (mediaType = "default") => (
    <div className="flex gap-3 items-center">
      <div className="flex gap-1">
        <img
          src={
            singleData?.is_like
              ? siteConstant.SOCIAL_ICONS.FILLED_LIKE_ICON
              : siteConstant.SOCIAL_ICONS.ANALYTICS_LIKE
          }
          loading="lazy"
          alt={singleData?.is_like ? "Liked Icon" : "Like Icon"}
          className="h-5 w-5 sm:h-6 sm:w-6"
        />
        <p className="text-white">
          {mediaType === "video" ? post?.likes : singleData?.likes ?? 0}
        </p>
      </div>
      <div className="flex gap-1">
        <img
          src={siteConstant.SOCIAL_ICONS.ANALYTICS_COMMENT}
          className="h-5 w-5 sm:h-6 sm:w-6"
          loading="lazy"
        />
        <p className="text-white">
          {mediaType === "video"
            ? post?.comments_count
            : singleData?.comments ?? 0}
        </p>
      </div>
      <div
        className="flex gap-1 cursor-pointer"
        aria-describedby={id}
        onClick={handleClick}
      >
        <img
          src={siteConstant.SOCIAL_ICONS.SHARE_ICON}
          className="h-5 w-5 sm:h-6 sm:w-6"
          loading="lazy"
        />
      </div>
      <Popover
        id={id}
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: "top",
          horizontal: "right",
        }}
        transformOrigin={{
          vertical: "bottom",
          horizontal: "right",
        }}
        PaperProps={{
          sx: {
            mt: -3,
          },
        }}
      >
        <Typography
          sx={{ p: 2 }}
          className="flex items-center gap-4 p-4 bg-gray-100 rounded-lg w-full max-w-[600px]"
        >
          <span
            className="truncate text-sm text-gray-700 flex-1"
            title={postUrl}
          >
            {postUrl}
          </span>
          <button
            onClick={handleCopyLink}
            className="bg-Red text-white flex items-center gap-1 px-3 py-2 text-sm rounded-lg transition"
          >
            <img
              src={siteConstant.SOCIAL_ICONS.COPY_LINK_ICON}
              className="h-4 w-4"
              alt="Copy"
            />
            Copy Link
          </button>
        </Typography>
      </Popover>
    </div>
  );
  const platformAliases = {
    threads: ["thread", "threads", "Thread", "Threads"],
    instagram: ["instagram", "Instagram"],
    facebook: ["facebook", "fb", "Facebook"],
    x: ["X", "x"],
    youtube: ["YouTube", "youtube"],
    linkedin: ["LinkedIn", "linkedin"],
    pinterest: ["Pinterest", "pinterest"],
    vimeo: ["Vimeo", "vimeo"],
    tumblr: ["Tumblr", "tumblr"],
    reddit: ["Reddit", "reddit"],
    tiktok: ["Tiktok", "tiktok"],
  };

  const singleDataKeys = Object.keys(singleData || {}).map((key) =>
    key.toLowerCase()
  );

  const connectedPlatforms = siteConstant.CHANNEL_LIST.filter((channel) => {
    const channelName = channel?.name?.toLowerCase();
    const aliases = platformAliases[channelName] || [channelName];

    return aliases.some((alias) =>
      singleDataKeys.includes(alias.toLowerCase())
    );
  });

  if (!connectedPlatforms) {
    return (
      <div className="text-center overflow-y-hidden p-4">
        <Loader />
      </div>
    );
  }

  const hasVideos =
    post?.files?.length > 0 && post.files.some((file) => isVideoFile(file));
  const videoFiles = hasVideos
    ? post.files.filter((file) => isVideoFile(file))
    : [];
  const imageFiles = hasVideos
    ? post.files.filter((file) => !isVideoFile(file))
    : post.files;

  return (
    <div
      className="p-4 font-Ubuntu bg-[#f5f5f8] overflow-x-hidden overflow-y-auto"
      style={{ padding: "20px" }}
    >
      <div className="flex">
        <div className="flex items-center gap-2">
          <div>
            <img
              src={
                userData?.profile_image ||
                siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
              }
              loading="lazy"
              className="w-[46px] h-[46px] sm:w-[56px] sm:h-[56px] rounded-full p-1 bg-cover"
              alt="Profile"
            />
          </div>
          <div>
            <p className="text-[15px] md:text-[17px] font-semibold text-Red">
              {userData?.name}
            </p>
            <p className="text-[11px] md:text-[13px] text-Red">
              {userData?.username}
            </p>
          </div>
        </div>
      </div>

      <div className="pt-4 relative">
        {/* Video Content */}
        {videoFiles.length > 0 && (
          <>
            {videoFiles.length > 1 ? (
              <Slider {...sliderSettings}>
                {videoFiles.map((videoSrc, index) => (
                  <div key={`video-${index}`} className="relative">
                    <div className="flex justify-center bg-gray-200">
                      <video
                        controls
                        className="w-auto rounded-lg h-[250px] sm:h-[300px] sm:w-auto"
                        preload="metadata"
                        onError={(e) => {
                          console.error("Video playback error:", e);
                          // Try to reload with HTTPS if available
                          const httpsUrl = ensureHttps(videoSrc);
                          if (httpsUrl !== videoSrc) {
                            e.target.src = httpsUrl;
                          }
                        }}
                      >
                        <source src={ensureHttps(videoSrc)} type="video/mp4" />
                        Your browser does not support the video tag.
                      </video>
                    </div>
                    <div className="absolute bottom-16 right-1 bg-[#0c0c0cca] px-2 p-1 rounded-md">
                      {renderMediaControls("video")}
                    </div>
                  </div>
                ))}
              </Slider>
            ) : (
              <div className="relative">
                <div className="flex justify-center bg-gray-200">
                  <video
                    controls
                    className="w-auto rounded-lg h-[250px] sm:h-[300px] sm:w-auto"
                    preload="metadata"
                    onError={(e) => {
                      console.error("Video playback error:", e);
                      // Try to reload with HTTPS if available
                      const httpsUrl = ensureHttps(videoFiles[0]);
                      if (httpsUrl !== videoFiles[0]) {
                        e.target.src = httpsUrl;
                      }
                    }}
                  >
                    <source src={ensureHttps(videoFiles[0])} type="video/mp4" />
                    Your browser does not support the video tag.
                  </video>
                </div>
                <div className="absolute bottom-16 right-1 bg-[#0c0c0cca] px-2 p-1 rounded-md">
                  {renderMediaControls("video")}
                </div>
              </div>
            )}
          </>
        )}

        {/* Image Content */}
        {imageFiles.length > 0 && (
          <>
            {imageFiles.length > 1 ? (
              <Slider {...sliderSettings}>
                {imageFiles.map((img, index) => (
                  <div key={`image-${index}`} className="relative">
                    <div className="flex justify-center bg-gray-50">
                      <img
                        src={img}
                        alt={`Image ${index}`}
                        className="w-auto rounded-lg h-[250px] sm:h-[300px] sm:w-auto"
                        loading="lazy"
                      />
                    </div>
                    <div className="absolute bottom-1 right-1 sm:bottom-4 sm:right-4 bg-[#0c0c0cca] px-2 p-1 rounded-md">
                      {renderMediaControls()}
                    </div>
                  </div>
                ))}
              </Slider>
            ) : (
              <div className="relative">
                <div className="flex justify-center bg-gray-200">
                  <img
                    src={imageFiles[0]}
                    loading="lazy"
                    alt="Single Image"
                    className="rounded-lg h-[250px] sm:h-[300px] w-max"
                  />
                </div>
                <div className="absolute bottom-4 right-4 bg-[#0c0c0cca] px-2 p-1 rounded-md">
                  {renderMediaControls()}
                </div>
              </div>
            )}
          </>
        )}
      </div>

      <div className="mt-4">
        <p className="text-[12px] text-Red font-medium line-clamp-2 sm:line-clamp-5 sm:pt-5 leading-5">
          {post?.description ? (
            <>{renderHighlightedText(post.description)}</>
          ) : (
            "No description available"
          )}
        </p>
      </div>
      <div>
        <p className="text-[18px] md:text-[19px] my-2 text-Red font-bold singlepost-head whitespace-nowrap">
          Connected Social Platforms
        </p>

        {loading ? (
          <Spinner />
        ) : connectedPlatforms?.length === 0 ? (
          <p className="text-center text-Red py-5 text-[13px] md:text-[16px]">
            No connected platforms
          </p>
        ) : (
          <div className="flex space-x-4 2xl:grid-cols-4 gap-4 channel-scrollbar overflow-x-auto my-3 lg:my-0 py-3 px-2">
            {connectedPlatforms?.map((channel) => (
              <div
                key={channel.key}
                className="p-2 sm:p-3 rounded-xl w-[160px] sm:min-w-[180px] flex-shrink-0 bg-white"
              >
                <div className="flex gap-3 sm:gap-4">
                  <div className="bg-gray-50 flex items-center justify-center h-9 w-9 sm:h-10 sm:w-10 rounded-md">
                    <img
                      src={channel.icon}
                      alt={channel.name}
                      className="h-7 w-7 sm:w-8 sm:h-8 rounded-md"
                    />
                  </div>
                  <div>
                    <p className="text-[12px] sm:text-sm text-Red">
                      {channel?.name}
                    </p>
                    <p className="text-[12px] sm:text-[12px] text-Red pt-1">
                      {post?.user?.username}
                    </p>
                  </div>
                </div>
                <div className="flex justify-between my-1.5 sm:my-3">
                  <div>
                    <p className="text-[13px] text-lightyellow py-1 sm:py-2">
                      {localesData?.USER_WEB?.USER_PROFILE?.LIKES}
                    </p>
                    <p className="text-[13px] text-lightyellow">
                      {localesData?.USER_WEB?.USER_PROFILE?.COMMENTS}
                    </p>
                  </div>
                  <div>
                    <p className="text-[13px] text-lightyellow py-1 sm:py-2">
                      {singleData?.[channel.name]?.like
                        ? singleData?.[channel.name]?.like
                        : 0}
                    </p>
                    <p className="text-[13px] text-lightyellow">
                      {singleData?.[channel.name]?.comments
                        ? singleData?.[channel.name]?.comments
                        : 0}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

export default SinglePost;
