import React, { useContext, useState, useRef, useEffect } from "react";
import { IntlContext } from "../../../App";
import {
  Dialog,
  MenuItem,
  Select,
  InputLabel,
  InputAdornment,
  DialogContent,
} from "@mui/material";
import { Formik, Form } from "formik";
import * as Yup from "yup";
import { URL as API_URL, URL } from "../../../helpers/constant/Url";
import { setApiMessage } from "../../../helpers/context/toaster";
import { CustomTextField } from "../custom/CustomTextField";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import profileImage from "../../../assets/images/profile_image.svg";
import { CustomFormControl } from "../custom/CustomFormControl";
import Spinner from "../../../helpers/UI/Spinner";
import { saveToStorage } from "../../../helpers/context/storage";
import DatePicker from "react-datepicker";
import siteConstant from "../../../helpers/constant/siteConstant";
import moment from "moment";
import celender from "../../../assets/images/svg_icon/celender-new.svg";
import ChangeProfile from "../../../assets/images/updateProfile.svg";
import Calendar from "../../../assets/images/calendar.svg";

const getMaxDate = () => {
  const today = new Date();
  today.setFullYear(today.getFullYear() - 18);
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const EditProfileModel = ({
  profile,
  open,
  handleDialogClose,
  fetchProfile,
}) => {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages || {};
  const [loading, setLoading] = useState(false);
  const fileInputRef = useRef(null);
  const [images, setImages] = useState([]);
  const [uploadedFile, setuploadedFile] = useState([]);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const datePickerRef = useRef(null);
  const formikRef = useRef(null);

  useEffect(() => {
    if (open && profile) {
      console.log("Profile data received:", {
        country: profile.country,
        state: profile.state,
        city: profile.city,
      });
    }
  }, [profile, open]);

  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [cities, setCities] = useState([]);
  const [selectedCountryId, setSelectedCountryId] = useState(null);
  const [selectedStateId, setSelectedStateId] = useState(null);
  const [formInitialized, setFormInitialized] = useState(false);

  const maxDate = getMaxDate();

  const fetchData = async (type, forwardingId = null, setData) => {
    try {
      const params = { request_type: type };
      if (forwardingId) params.forwarding_id = forwardingId;

      console.log(
        `Fetching data for type ${type}${
          forwardingId ? ` with id ${forwardingId}` : ""
        }`
      );
      const response = await apiInstance.get("/demography-list/", { params });
      const responseData = response.data.data || [];
      console.log(`Received ${responseData.length} items for type ${type}`);
      setData(responseData);
      return responseData;
    } catch (error) {
      console.error(`Error fetching data (type: ${type}):`, error);
      return [];
    }
  };

  useEffect(() => {
    if (selectedCountryId) {
      console.log(
        "Selected country ID changed, fetching states for:",
        selectedCountryId
      );
      fetchData(2, selectedCountryId, setStates);
    }
  }, [selectedCountryId]);

  useEffect(() => {
    if (selectedStateId) {
      console.log(
        "Selected state ID changed, fetching cities for:",
        selectedStateId
      );
      fetchData(3, selectedStateId, setCities);
    }
  }, [selectedStateId]);

  useEffect(() => {
    const initializeFormData = async () => {
      if (profile && open) {
        console.log("Initializing form with profile data:", profile);

        const countriesData = await fetchData(1, null, setCountries);

        if (profile.country && countriesData.length > 0) {
          const countryObj = countriesData.find(
            (c) => c.name === profile.country
          );
          if (countryObj) {
            console.log("Found country:", countryObj);
            setSelectedCountryId(countryObj.forwarding_id);

            const statesData = await fetchData(
              2,
              countryObj.forwarding_id,
              setStates
            );

            if (profile.state && statesData.length > 0) {
              const stateObj = statesData.find((s) => s.name === profile.state);
              if (stateObj) {
                console.log("Found state:", stateObj);
                setSelectedStateId(stateObj.forwarding_id);

                await fetchData(3, stateObj.forwarding_id, setCities);
              }
            }
          }
        }

        setFormInitialized(true);
      }
    };

    if (open) {
      setFormInitialized(false);
      initializeFormData();
    }
  }, [profile, open]);

  useEffect(() => {
    if (!open) {
      console.log("Modal closed, resetting form state");
      setFormInitialized(false);
      setImages([]);
      setuploadedFile([]);
    }
  }, [open]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target)
      ) {
        setShowDatePicker(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [datePickerRef]);

  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      setImages("");
      handleDialogClose(false);
    }
  };

  const handleFileInputClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (event) => {
    const files = event.target.files;
    if (files.length === 1) {
      const uploadedImages = Array.from(files).map((file) => {
        return {
          url: window.URL.createObjectURL(file),
        };
      });

      setImages(uploadedImages);
      setuploadedFile(files);
    }
  };

  // Custom DatePicker Styles
  const datePickerStyles = {
    position: "absolute",
    zIndex: 1000,
    padding: "10px",
    marginTop: "5px",
    left: "80px",
    width: "100%",
    maxWidth: "320px",
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleDialogClose}
        className="fixed inset-0 z-10 flex items-center justify-center font-Ubuntu"
      >
        <DialogContent className="min-w-[1320px]">
          <div
            className="fixed inset-0 flex justify-center items-center bg-gray-900 bg-opacity-50 overflow-y-auto "
            aria-hidden="true"
            onClick={handleBackdropClick}
          >
            <Formik
              innerRef={formikRef}
              initialValues={{
                name: profile?.name || "",
                userName: profile?.username || "",
                bio: profile?.bio || "",
                dateOfBirth: profile?.dob || "",
                mobileNumber: profile?.mobile || "",
                gender: profile?.gender || "",
                profile_image: profile?.profile_image || "",
                country: profile?.country || "",
                state: profile?.state || "",
                city: profile?.city || "",
              }}
              enableReinitialize={true}
              validationSchema={Yup.object().shape({
                name: Yup.string().required("Name is required"),
                userName: Yup.string().required("User name is required"),
                dateOfBirth: Yup.string().required("Date of birth is required"),
                mobileNumber: Yup.string()
                  .matches(/^[0-9]+$/, "Mobile number must be digits only")
                  .min(10, "Mobile number must be at least 10 digits")
                  .max(10, "Mobile number must be at most 10 digits")
                  .required("Mobile number is required"),
                gender: Yup.string().required("Gender is required"),
                country: Yup.string().required("Country is required"),
                state: Yup.string().required("State is required"),
                city: Yup.string().required("City is required"),
              })}
              onSubmit={async (values, { setSubmitting }) => {
                setLoading(true);
                try {
                  const form = new FormData();
                  form.append("name", values?.name);
                  form.append("username", values?.userName);
                  form.append("bio", values?.bio);
                  form.append("mobile", values?.mobileNumber);
                  form.append("dob", values?.dateOfBirth);
                  form.append("gender", values?.gender);
                  form.append("country", values?.country);
                  form.append("state", values?.state);
                  form.append("city", values?.city);

                  for (let file of uploadedFile) {
                    form.append("profile_image", file || values?.profile_image);
                  }

                  const { status, data } = await apiInstance.post(
                    API_URL.EDIT_PROFILE,
                    form
                  );
                  if (data) {
                    if (data?.status) {
                      setLoading(true);
                      setApiMessage("success", data?.message);
                      fetchProfile();
                      setLoading(false);
                    } else {
                      setApiMessage("error", data?.message);
                    }
                  } else {
                    setApiMessage(
                      "error",
                      localesData?.USER_WEB?.MESSAGES?.EDIT_PROFILE_ERROR
                    );
                  }
                  setSubmitting(false);
                } catch (error) {
                  setApiMessage("error", error?.message);
                } finally {
                  setLoading(false);
                }

                setTimeout(() => {
                  setSubmitting(false);
                  handleDialogClose();
                }, 400);
              }}
            >
              {({
                errors,
                handleBlur,
                handleChange,
                handleSubmit,
                touched,
                values,
                setFieldValue,
                isSubmitting,
              }) => (
                <Form
                  onSubmit={handleSubmit}
                  className="w-full max-w-[1320px] mt-20 m-3 md:m-10"
                >
                  <div className="bg-white rounded-3xl overflow-y-auto shadow-lg p-4">
                    {/* Header with close button */}
                    <div className="relative p-6 ">
                      <div className="flex items-center justify-end">
                        <button
                          type="button"
                          onClick={handleDialogClose}
                          className="text-gray-500 hover:text-gray-700"
                        >
                          <svg
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              d="M18 6L6 18M6 6L18 18"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Profile content area */}
                    <div className="px-4 pb-6 md:p-6 mt-[470px] sm:mt-[300px] md:-mt-16">
                      {/* Profile Image Section */}
                      <div className="flex flex-col md:flex-row justify-center items-center  mb-8 gap-2 ">
                        {/* Profile Image Component with Improved Click Handling */}
                        <div className="flex flex-col items-center relative">
                          <img
                            src={
                              images[0]?.url ||
                              profile?.profile_image ||
                              profileImage
                            }
                            alt="Profile"
                            className="w-20 h-20 rounded-full object-cover mb-2"
                          />
                          {/* Make the change profile icon clickable */}
                          <div
                            className="absolute right-0 bottom-2 z-10 cursor-pointer"
                            onClick={handleFileInputClick}
                          >
                            <img
                              src={ChangeProfile}
                              alt="Change Profile"
                              className="w-[30px] h-[30px]"
                              loading="lazy"
                            />
                          </div>
                          {/* Hidden file input */}
                          <input
                            id="Change-profile"
                            type="file"
                            accept="image/*"
                            ref={fileInputRef}
                            style={{ display: "none" }}
                            onChange={handleFileChange}
                            multiple
                          />
                        </div>
                        <div className="flex justify-start items-center mb-2 ">
                          <p className="text-[#333333] text-[28px] font-bold">
                            Edit your profile
                          </p>
                        </div>
                      </div>

                      {/* Form Grid */}
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 border-[1px] border-[#E0E0E0] rounded-[12px] p-4">
                        {/* First Name */}
                        <div>
                          <label
                            htmlFor="name"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Name
                          </label>
                          <input
                            type="text"
                            id="name"
                            name="name"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                            value={values.name}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                          {touched.name && errors.name && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.name}
                            </div>
                          )}
                        </div>

                        {/* Username */}
                        <div>
                          <label
                            htmlFor="userName"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Username
                          </label>
                          <input
                            type="text"
                            id="userName"
                            name="userName"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-gray-50"
                            value={values.userName}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            readOnly
                          />
                          {touched.userName && errors.userName && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.userName}
                            </div>
                          )}
                        </div>

                        {/* Email */}
                        <div>
                          <label
                            htmlFor="email"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Email*
                          </label>
                          <input
                            type="email"
                            id="email"
                            name="email"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 bg-gray-50"
                            value={profile?.email || ""}
                            readOnly
                          />
                        </div>

                        {/* Phone */}
                        <div>
                          <label
                            htmlFor="mobileNumber"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Phone*
                          </label>
                          <input
                            type="text"
                            id="mobileNumber"
                            name="mobileNumber"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                            value={values.mobileNumber}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                          {touched.mobileNumber && errors.mobileNumber && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.mobileNumber}
                            </div>
                          )}
                        </div>

                        {/* Bio */}
                        <div className="md:col-span-2">
                          <label
                            htmlFor="bio"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Bio
                          </label>
                          <textarea
                            id="bio"
                            name="bio"
                            rows="3"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500"
                            value={values.bio}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          />
                        </div>

                        {/* Date of Birth */}
                        <div>
                          <label
                            htmlFor="dateOfBirth"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Date of birth
                          </label>
                          <div className="relative">
                            <input
                              type="text"
                              id="dateOfBirth"
                              name="dateOfBirth"
                              className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 cursor-pointer"
                              value={
                                values.dateOfBirth
                                  ? moment(values.dateOfBirth).format(
                                      "DD/MM/YYYY"
                                    )
                                  : ""
                              }
                              onClick={() => setShowDatePicker(!showDatePicker)}
                              readOnly
                            />
                            <div
                              className="absolute right-2 top-2 cursor-pointer"
                              onClick={() => setShowDatePicker(!showDatePicker)}
                            >
                              <img
                                src={Calendar}
                                alt="Calendar"
                                className="w-5 h-5 mt-[3px]"
                              />
                            </div>
                          </div>

                          {touched.dateOfBirth && errors.dateOfBirth && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.dateOfBirth}
                            </div>
                          )}

                          {showDatePicker && (
                            <div
                              ref={datePickerRef}
                              style={datePickerStyles}
                              className="z-50"
                            >
                              <DatePicker
                                selected={
                                  values.dateOfBirth
                                    ? new Date(values.dateOfBirth)
                                    : null
                                }
                                onChange={(date) => {
                                  setFieldValue("dateOfBirth", date);
                                  setShowDatePicker(false);
                                }}
                                dateFormat="dd/MM/yyyy"
                                inline
                                showYearDropdown
                                maxDate={new Date(maxDate)}
                                yearDropdownItemNumber={100}
                                scrollableYearDropdown
                                peekNextMonth
                                showMonthDropdown
                              />
                            </div>
                          )}
                        </div>

                        {/* Gender */}
                        <div>
                          <label
                            htmlFor="gender"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Gender
                          </label>
                          <select
                            id="gender"
                            name="gender"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
                            value={values.gender}
                            onChange={handleChange}
                            onBlur={handleBlur}
                          >
                            <option value="">Select gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                            <option value="Other">Other</option>
                          </select>
                          {touched.gender && errors.gender && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.gender}
                            </div>
                          )}
                        </div>

                        {/* Country */}
                        <div>
                          <label
                            htmlFor="country"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            Country
                          </label>
                          <select
                            id="country"
                            name="country"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
                            value={values.country || ""}
                            onChange={(e) => {
                              handleChange(e);
                              const selectedCountry = countries.find(
                                (c) => c.name === e.target.value
                              );
                              if (selectedCountry) {
                                setSelectedCountryId(
                                  selectedCountry.forwarding_id
                                );
                                setFieldValue("state", "");
                                setFieldValue("city", "");
                                setSelectedStateId(null);
                                setCities([]);
                              }
                            }}
                            onBlur={handleBlur}
                          >
                            <option value="">Select country</option>
                            {countries.map((country) => (
                              <option key={country.id} value={country.name}>
                                {country.name}
                              </option>
                            ))}
                          </select>
                          {touched.country && errors.country && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.country}
                            </div>
                          )}
                        </div>

                        {/* State */}
                        <div>
                          <label
                            htmlFor="state"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            State
                          </label>
                          <select
                            id="state"
                            name="state"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
                            value={values.state || ""}
                            onChange={(e) => {
                              handleChange(e);
                              const selectedState = states.find(
                                (s) => s.name === e.target.value
                              );
                              if (selectedState) {
                                setSelectedStateId(selectedState.forwarding_id);
                                setFieldValue("city", "");
                              }
                            }}
                            onBlur={handleBlur}
                            disabled={!values.country}
                          >
                            <option value="">Select state</option>
                            {states.map((state) => (
                              <option key={state.id} value={state.name}>
                                {state.name}
                              </option>
                            ))}
                          </select>
                          {touched.state && errors.state && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.state}
                            </div>
                          )}
                        </div>

                        {/* City */}
                        <div>
                          <label
                            htmlFor="city"
                            className="block text-sm font-normal text-[#808080] mb-1"
                          >
                            City
                          </label>
                          <select
                            id="city"
                            name="city"
                            className="w-full px-3 py-2 border-[1px] border-[#E2E0DB] rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 appearance-none"
                            value={values.city || ""}
                            onChange={handleChange}
                            onBlur={handleBlur}
                            disabled={!values.state}
                          >
                            <option value="">Select city</option>
                            {cities.map((city) => (
                              <option key={city.id} value={city.name}>
                                {city.name}
                              </option>
                            ))}
                          </select>
                          {touched.city && errors.city && (
                            <div className="text-red-600 text-xs mt-1">
                              {errors.city}
                            </div>
                          )}
                        </div>
                      </div>

                      {/* Submit Button */}
                      <div className="mt-8 flex justify-center">
                        <button
                          type="submit"
                          disabled={loading}
                          className="px-8 py-2 h-[40px] w-[280px] bg-[#5c4039] text-white rounded-[12px] font-medium hover:bg-[#4a332e] transition-colors"
                        >
                          {loading ? "Saving..." : "Save"}
                        </button>
                      </div>
                    </div>
                  </div>
                </Form>
              )}
            </Formik>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EditProfileModel;
