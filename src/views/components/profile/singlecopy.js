import React, { useState } from "react";
import Slider from "react-slick";
import siteConstant from "src/helpers/constant/siteConstant";
import { FaChevronLeft, FaChevronRight } from "react-icons/fa"; // Importing icons
import { Button, Popover, Typography } from "@mui/material";
import Loader from "src/helpers/UI/Loader";

function SinglePost({ singleData, post }) {
  const [anchorEl, setAnchorEl] = React.useState(null);
  const [isLike, setIsLike] = useState(true);
  console.log("singleData", singleData);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const socialPlatforms = [
    {
      name: "Twitter",
      icon: siteConstant.SOCIAL_ICONS.TWITTER_ICON,
      key: "twitter",
    },
    {
      name: "Instagram",
      icon: siteConstant.SOCIAL_ICONS.INSTAGRAM_ICON,
      key: "instagram",
    },
    {
      name: "YouTube",
      icon: siteConstant.SOCIAL_ICONS.YOUTUBE_ICON,
      key: "youtube",
    },
    {
      name: "Facebook",
      icon: siteConstant.SOCIAL_ICONS.FACEBOOK_ICON,
      key: "facebook",
    },
    {
      name: "TikTok",
      icon: siteConstant.SOCIAL_ICONS.TIKTOK_ICON,
      key: "tiktok",
    },
    {
      name: "Pinterest",
      icon: siteConstant.SOCIAL_ICONS.PINTEREST_ICON,
      key: "pinterest",
    },
    {
      name: "Vimeo",
      icon: siteConstant.SOCIAL_ICONS.VIMEO_ICON,
      key: "vimeo",
    },
    {
      name: "LinkedIn",
      username: "@anton_demeron",
      icon: siteConstant.SOCIAL_ICONS.LINKEDIN_ICON,
      key: "linkedin",
    },

    {
      name: "Tumblr",
      username: "@anton_demeron",
      icon: siteConstant.SOCIAL_ICONS.TUMBLR_ICON,
      key: "Tumblr",
    },

    {
      name: "Reddit",
      username: "@anton_demeron",
      icon: siteConstant.SOCIAL_ICONS.TUMBLR_ICON,
      key: "Reddit",
    },
  ];

  const handleClose = () => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  const id = open ? "simple-popover" : undefined;

  const CustomNextArrow = ({ onClick }) => (
    <div
      className="absolute top-1/2 right-[-23px] transform -translate-y-1/2 z-10 cursor-pointer bg-[#EFEBE9] p-2 rounded-full shadow-lg"
      onClick={onClick}
    >
      <FaChevronRight className="text-[#563D39]" />
    </div>
  );

  const CustomPrevArrow = ({ onClick }) => (
    <div
      className="absolute top-1/2 left-[-23px] transform -translate-y-1/2 z-10 cursor-pointer bg-[#EFEBE9] p-2 rounded-full shadow-lg"
      onClick={onClick}
    >
      <FaChevronLeft className="text-[#563D39]" />
    </div>
  );

  const sliderSettings = {
    infinite: true,
    dots: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    nextArrow: <CustomNextArrow />,
    prevArrow: <CustomPrevArrow />,
  };

  if (!singleData) {
    return (
      <div className="text-center p-4">
        <Loader />
      </div>
    );
  }

  const renderSocialPlatform = (platform) => {
    const platformData = singleData.data[platform.key];
    if (platformData) {
      return (
        <div className="bg-white p-3 rounded-md shadow-custom-shadow mb-2 lg:mb-0">
          <div className="flex gap-2">
            <div className="bg-gray-50 flex items-center justify-center h-10 w-10 rounded-md">
              <img
                src={platform.icon}
                alt={platform.name}
                className="h-6 w-6"
              />
            </div>
            <div>
              <p className="text-sm">{post?.user?.name}</p>
              <p className="text-[12px] text-gray-400">
                {post?.user?.username}
              </p>
            </div>
          </div>
          <div className="flex justify-between">
            <div>
              <p className="text-[13px] text-[#898CAA] py-4">Likes</p>
              <p className="text-[13px] text-[#898CAA]">Comments</p>
            </div>
            <div>
              <p className="text-[13px] text-[#898CAA] py-4">
                {platformData.like || 0}
              </p>
              <p className="text-[13px] text-[#898CAA]">
                {platformData.comments || 0}
              </p>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  const connectedPlatforms = socialPlatforms.filter(
    (platform) => singleData.data[platform.key]
  );

  return (
    <div className="p-1 font-Ubuntu">
      <div className="flex">
        <div className="flex items-center gap-2">
          <div>
            <img
              src={
                post?.user?.profile_image ||
                siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
              }
              className="w-[56px] h-[56px] rounded-[23px] p-1 border-2 border-Red"
              alt="Profile"
            />
          </div>
          <div>
            <p className="text-[17px] font-semibold"> {post?.user?.name}</p>
            <p className="text-[13px]"> {post?.user?.username}</p>
          </div>
        </div>
      </div>
      {post.type === "video" ? (
        <div className="pt-4">
          <video controls className="w-full h-[300px] rounded-lg ">
            <source src={post.videoUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
          <div className="absolute bottom-4 right-4 flex gap-3 items-center bg-[#0c0c0cca] px-2 p-1 rounded-md">
            <div className="flex gap-1">
              <img
                src={siteConstant.SOCIAL_ICONS.FILLED_LIKE_ICON}
                className="h-6 w-6"
              />
              {post?.likes > 0 ? (
                <img
                  src={siteConstant.SOCIAL_ICONS.FILLED_LIKE_ICON}
                  className="h-6 w-6"
                  alt="Filled Like"
                />
              ) : (
                <img
                  src={siteConstant.SOCIAL_ICONS.LIKE_ICON}
                  className="h-6 w-6"
                  alt="Normal Like"
                />
              )}
              <p className="text-white">{post?.likes}</p>
            </div>
            <div className="flex gap-1">
              <img
                src={siteConstant.SOCIAL_ICONS.COMMENT_ICON}
                className="h-6 w-6"
              />
              <p className="text-white">{post?.comments_count}</p>
            </div>
            <div
              className="flex gap-1"
              aria-describedby={id}
              onClick={handleClick}
            >
              <img
                src={siteConstant.SOCIAL_ICONS.SHARE_ICON}
                className="h-6 w-6"
              />

              <p className="text-white">4</p>
              <Popover
                id={id}
                open={open}
                anchorEl={anchorEl}
                onClose={handleClose}
                anchorOrigin={{
                  vertical: "top",
                  horizontal: "right",
                }}
                transformOrigin={{
                  vertical: "bottom",
                  horizontal: "right",
                }}
                PaperProps={{
                  sx: {
                    mt: -3,
                  },
                }}
              >
                <Typography sx={{ p: 2 }} className="flex items-center gap-1 ">
                  https://Flowkar.com/some_username{" "}
                  <button className="bg-Red text-white flex items-center gap-1 p-2 text-sm rounded-lg">
                    {" "}
                    <img
                      src={siteConstant.SOCIAL_ICONS.COPY_LINK_ICON}
                      className="h-3 w-3 sm:h-5 sm:w-5"
                    />
                    Copy Link
                  </button>
                </Typography>
              </Popover>
            </div>
          </div>
        </div>
      ) : (
        <Slider {...sliderSettings}>
          {singleData.data.files.map((file, index) => (
            <div key={index}>
              <img
                src={file}
                className="w-full h-[300px] rounded-lg object-cover"
                alt={`Slide ${index}`}
              />
            </div>
          ))}
        </Slider>
      )}
      <div className="flex justify-between pt-4">
        {connectedPlatforms.map((platform) => renderSocialPlatform(platform))}
      </div>
    </div>
  );
}

export default SinglePost;
