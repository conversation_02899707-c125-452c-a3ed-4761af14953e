import { Dialog } from "@mui/material";
import React, { useContext, useState } from "react";
import { IntlContext } from "../../../App";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import { setApiMessage } from "../../../helpers/context/toaster";
import { fetchProfile } from "../../../redux/slices/profileFetch";

const DeletePostModel = ({
  open,
  handleDialogClose,
  post,
  fetchPosts,
  postDeleteURL,
  fetchProfile,
}) => {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const isUserDeletion = URL.ADMIN_USER_DELETE === postDeleteURL;
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    setLoading(true);
    try {
      // const isAdmin = postDeleteURL === URL.ADMIN_POST_DELETE
      // const form = new FormData()
      // if (isAdmin) {
      //     form.append('post_id', post?.post_id)
      // } else {
      //     postDeleteURL = postDeleteURL + post?.id
      // }

      const { status, data } = await apiInstance.get(
        isUserDeletion
          ? `${postDeleteURL}?user_id=${post?.id}`
          : `${postDeleteURL}?post_id=${
              postDeleteURL === URL.ADMIN_POST_DELETE ? post?.post_id : post?.id
            }`
      );
      if (data?.status) {
        if (fetchProfile) fetchProfile();
        setApiMessage("success", data?.messages);
        setLoading(false);
        fetchPosts(1);
        handleDialogClose();
      } else {
        setApiMessage("error", data?.messages);
        setLoading(false);
      }
    } catch (error) {
      console.log(error);
      setApiMessage("error", error?.messages);
      setLoading(false);
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleDialogClose}
        className="bg-transparent"
      >
        <div class="bg-white p-7 rounded-xl shadow-lg w-full max-w-md">
          <div class="flex flex-col gap-3 items-center">
            <div class="mb-4">
              <img
                src={intlContext?.socialIcons?.DELETE_POST_CONFIRMATION}
                alt="Delete Image"
                class="w-26 h-26"
              />
            </div>
            <p class="text-[11px] sm:text-sm md:text-lg font-Ubuntu mb-4 whitespace-nowrap">
              {isUserDeletion
                ? localesData?.USER_WEB?.DELETE_USER_CONFIRMATION
                : localesData?.USER_WEB?.DELETE_POST_CONFIRMATION}
            </p>
            <div class="flex space-x-6 sm:space-x-8 md:space-x-12">
              <button
                class="text-Red py-1 sm:py-3 w-20 sm:w-24 md:w-28 lg:w-36 rounded-xl hover:bg-[#EFEBE9]  text-[13px] sm:text-[15px]  font-semibold border border-pink-100"
                onClick={() => handleDialogClose()}
              >
                {localesData?.USER_WEB?.CANCEL}
              </button>
              <button
                class="bg-Red text-white py-1 sm:py-3 w-20 sm:w-24 md:w-28 lg:w-36 text-[13px] sm:text-[15px] rounded-xl  font-semibold"
                onClick={() => handleDelete()}
                disabled={loading}
              >
                {localesData?.USER_WEB?.DELETE}
              </button>
            </div>
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default DeletePostModel;
