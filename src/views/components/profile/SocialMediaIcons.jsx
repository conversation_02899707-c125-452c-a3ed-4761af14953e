import React from "react";
import siteConstant from "../../../helpers/constant/siteConstant";

const SocialMediaIcons = ({ platforms }) => {
  // Check if platforms exist and is an object
  if (!platforms || typeof platforms !== "object") {
    return null;
  }

  // Define icon mapping using your siteConstant.SOCIAL_ICONS
  const iconMap = {
    facebook: {
      component: siteConstant.SOCIAL_ICONS.FACEBOOK_ICON,
      color: "#1877F2",
      backgroundColor: "#E7F3FF",
    },
    instagram: {
      component: siteConstant.SOCIAL_ICONS.INSTAGRAM_ICON,
      color: "#E4405F",
      backgroundColor: "#FDEDF1",
    },
    linkedin: {
      component: siteConstant.SOCIAL_ICONS.LINKEDIN_ICON,
      color: "#0A66C2",
      backgroundColor: "#E6F0FA",
    },
    pinterest: {
      component: siteConstant.SOCIAL_ICONS.PINTEREST_ICON,
      color: "#BD081C",
      backgroundColor: "#F9E9EB",
    },
    youtube: {
      component: siteConstant.SOCIAL_ICONS.YOUTUBE_ICON,
      color: "#FF0000",
      backgroundColor: "#FFEBEB",
    },
    twitter: {
      component: siteConstant.SOCIAL_ICONS.THREADS_ICON,
      color: "#1DA1F2",
      backgroundColor: "#EAF7FE",
    },
    reddit: {
      component: siteConstant.SOCIAL_ICONS.REDDIT_ICON,
      color: "#FF4500",
      backgroundColor: "#FFECE5",
    },
    tumblr: {
      component: siteConstant.SOCIAL_ICONS.TUMBLR_ICON,
      color: "#36465D",
      backgroundColor: "#EBEEF1",
    },
    vimeo: {
      component: siteConstant.SOCIAL_ICONS.VIMEO_ICON,
      color: "#1AB7EA",
      backgroundColor: "#E9F9FE",
    },
    dailymotion: {
      component: siteConstant.SOCIAL_ICONS.DAILYMOTION_ICON,
      color: "#0066DC",
      backgroundColor: "#E5F0FC",
    },
    tiktok: {
      component: siteConstant.SOCIAL_ICONS.TIKTOK_ICON,
      color: "#000000",
      backgroundColor: "#F0F0F0",
    },
    thread: {
      component: siteConstant.SOCIAL_ICONS.THREAD,
      color: "#000000",
      backgroundColor: "#F0F0F0",
    },
    flowkar: {
      component: siteConstant.SOCIAL_ICONS.FLOWKAR_F,
      color: "#563D39",
      backgroundColor: "#F5F3F2",
    },
  };

  return (
    <div className="flex items-center gap-2">
      {Object.entries(platforms).map(([platform, isActive]) => {
        // Only render if the platform is active
        if (!isActive) return null;

        const platformKey = platform.toLowerCase();
        const iconData = iconMap[platformKey];

        if (!iconData) return null;

        const { component: IconComponent, color, backgroundColor } = iconData;

        // Skip if the icon component is not available
        if (!IconComponent) return null;

        return (
          <div
            key={platformKey}
            className="flex items-center justify-center w-8 h-8 rounded-full"
            style={{ backgroundColor }}
          >
            <img
              src={IconComponent}
              alt={`${platform} icon`}
              className="w-8 h-8 flex-shrink-0 rounded-full"
            />
          </div>
        );
      })}
    </div>
  );
};

export default SocialMediaIcons;
