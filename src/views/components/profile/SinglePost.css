/* SinglePost.css */
/* Fix for slider dots positioning */
.slider-container {
position: relative;
margin-bottom: 40px;
}
.media-slider {
position: relative;
}
/* Override the default slick-dots styling */
.slick-dots.custom-dots {
position: absolute;
bottom: -25px;
display: flex !important;
justify-content: center;
width: 100%;
padding: 0;
margin: 0;
list-style: none;
text-align: center;
left: 0;
}
.slick-dots.custom-dots li {
position: relative;
display: inline-block;
width: 10px;
height: 10px;
margin: 0 5px;
padding: 0;
cursor: pointer;
}
.slick-dots.custom-dots li button {
font-size: 0;
line-height: 0;
display: block;
width: 10px;
height: 10px;
padding: 5px;
cursor: pointer;
color: transparent;
border: 0;
outline: none;
background: transparent;
}
.slick-dots.custom-dots li button:before {
font-family: 'slick';
font-size: 10px;
line-height: 20px;
position: absolute;
top: 0;
left: 0;
width: 10px;
height: 10px;
content: '•';
text-align: center;
opacity: .25;
color: #563D39;
}
.slick-dots.custom-dots li.slick-active button:before {
opacity: .75;
color: #563D39;
}
/* Additional styling to ensure controls are visible */
.slick-arrow {
z-index: 10;
}
/* Ensure slider container has proper spacing */
.media-slider .slick-list {
margin-bottom: 10px;
}