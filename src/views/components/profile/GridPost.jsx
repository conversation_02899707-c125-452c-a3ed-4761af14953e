import React, {
  Suspense,
  useContext,
  useEffect,
  useRef,
  useState,
} from "react";
import { useSelector } from "react-redux";
import moment from "moment";
import {
  Dialog,
  DialogContent,
  Skeleton,
  IconButton,
  Menu,
  MenuItem,
  Popover,
  Divider,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { IntlContext } from "../../../App";
import { URL } from "../../../helpers/constant/Url";
import { getFileType } from "../../../helpers/constant/utils";
import { renderHighlightedText } from "../../../helpers/context/common";
import siteConstant from "../../../helpers/constant/siteConstant";
import deleteBin from "../../../assets/images/deleteBin.svg";
import { ensureHttps } from "../../../helpers/constant/utils";
import post_icon from "../../../assets/images/svg_icon/post.svg";
import fleez from "../../../assets/images/fleez.svg";
import tag from "../../../assets/images/tag.svg";
import text_post from "../../../assets/images/svg_icon/text_post.svg";
import Edit from "../../../assets/images/svg_icon/Edit.svg";
import Delete from "../../../assets/images/svg_icon/delete.svg";

// Import the SocialMediaIcons component
import SocialMediaIcons from "./SocialMediaIcons";
import { RiDeleteBin6Line } from "react-icons/ri";
import apiInstance from "../../../helpers/Axios/axiosINstance";

// Add import for EditPostPopup from Planner
import { EditPostPopup } from "../Planner/index.jsx";

const SinglePost = React.lazy(() => import("./SiglePost"));
const DeletePostModel = React.lazy(() => import("./deletePost"));
const DeletePostDraftModel = React.lazy(() => import("./DeletePostDraft"));

// Skeleton component for post loading
const PostSkeleton = () => {
  return (
    <div className="bg-white rounded-xl border-[1px] border-[#E0E0E0] p-3 w-full h-[500px] flex flex-col">
      {/* Header skeleton */}
      <div className="flex items-center gap-3 mb-2 h-12">
        <Skeleton variant="circular" width={32} height={32} />
        <div className="flex-1">
          <Skeleton variant="text" width="70%" height={20} />
          <Skeleton variant="text" width="50%" height={16} />
        </div>
      </div>

      {/* Description skeleton */}
      <div className="h-12 mb-2">
        <Skeleton variant="text" width="90%" height={18} />
        <Skeleton variant="text" width="75%" height={18} />
      </div>

      {/* Media skeleton */}
      <div className="rounded-xl overflow-hidden flex-grow mb-2">
        <Skeleton variant="rectangular" width="100%" height="100%" />
      </div>

      {/* Social Media Icons skeleton */}
      <div className="h-8 mb-2 flex justify-between items-center">
        <div className="flex gap-2">
          <Skeleton variant="circular" width={24} height={24} />
          <Skeleton variant="circular" width={24} height={24} />
          <Skeleton variant="circular" width={24} height={24} />
        </div>
        <Skeleton variant="circular" width={28} height={28} />
      </div>
    </div>
  );
};

const NoPost = ({ localesData }) => {
  return (
    <div className="col-span-full flex flex-col items-center justify-center py-16">
      <div className="text-center">
        <p className="text-lg font-semibold text-gray-800 mb-1">
          {localesData?.USER_WEB?.NO_POST}
        </p>
        <p className="text-sm text-gray-500 mb-2">
          {localesData?.USER_WEB?.BE_THE_FIRST_TO_SHARE_SOMTHING_AWESOME}
        </p>
        <p className="text-md font-semibold text-gray-800">
          {localesData?.USER_WEB?.SHARE_YOUR_FIRST_PHOTO}
        </p>
      </div>
    </div>
  );
};

function GridPost({ userData, fetchPosts, istable, fetchProfile, posts = [] }) {
  const [open, setOpen] = useState(false);
  const [post, setPost] = useState(null);
  const [draftPost, setDraftPost] = useState(null);
  const [openDeleteDialog, setOpenDeleteDialog] = useState(false);
  const [openDeleteDraftDialog, setOpenDeleteDraftDialog] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const [activeTab, setActiveTab] = useState("posts");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [reelsData, setReelsData] = useState([]);
  const [tagData, setTagData] = useState([]);
  const [draftData, setDraftData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loading, setLoading] = useState({
    posts: true,
    reels: true,
    tags: true,
    drafts: true,
  });
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages || {};
  const selectedUser = useSelector((state) => state.profile.userName);
  const [isPlaying, setIsPlaying] = useState(false);
  const videoRef = useRef(null);
  const brandId = localStorage.getItem("BrandId");
  const token = localStorage.getItem("token");
  const [anchorEl, setAnchorEl] = useState(null);
  const [menuPost, setMenuPost] = useState(null);
  // Add state for edit dialog and post
  const [editPost, setEditPost] = useState(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  const getVideoPosts = async () => {
    setLoading((prev) => ({ ...prev, reels: true }));
    try {
      const response = await apiInstance.get(URL.GET_USER_REELS_POST);
      const data = response?.data?.results?.data || [];
      setReelsData(data);
    } catch (error) {
      console.error("Error fetching video posts:", error);
      setReelsData([]);
    } finally {
      setLoading((prev) => ({ ...prev, reels: false }));
    }
  };

  const getDraftPosts = async () => {
    setLoading((prev) => ({ ...prev, drafts: true }));
    try {
      const response = await apiInstance.get(URL.GET_USER_DRAFT_POSTS, {
        headers: {
          brand: brandId,
        },
      });
      const data = response?.data?.results?.data || [];
      setDraftData(data);
    } catch (error) {
      console.error("Error fetching Draft posts:", error);
      setDraftData([]);
    } finally {
      setLoading((prev) => ({ ...prev, drafts: false }));
    }
  };

  const postDraftPosts = async (post_id) => {
    try {
      setIsLoading(true);
      const form = new FormData();
      form.append("post_id", post_id);
      const response = await apiInstance.post(URL.POST_DRAFT_POSTS, form, {
        headers: {
          brand: brandId,
        },
      });
      if (response?.data?.status) {
        if (fetchProfile) fetchProfile();
        fetchPosts(1);
        getDraftPosts();
      } else {
        console.log("error", response?.data?.messages);
      }
    } catch (error) {
      console.log(error);
      console.log("error", error?.messages);
    } finally {
      setIsLoading(false);
    }
  };

  const getTagPosts = async () => {
    setLoading((prev) => ({ ...prev, tags: true }));
    try {
      const response = await apiInstance.get(URL.GET_USER_TAG_POST);
      const data = response?.data?.results?.data || [];
      setTagData(data);
    } catch (error) {
      console.error("Error fetching tagged posts:", error);
      setTagData([]);
    } finally {
      setLoading((prev) => ({ ...prev, tags: false }));
    }
  };

  useEffect(() => {
    getVideoPosts();
    getTagPosts();
    getDraftPosts();

    // This assumes posts are passed as props and already loading elsewhere
    const timer = setTimeout(() => {
      setLoading((prev) => ({ ...prev, posts: false }));
      setIsVisible(true);
    }, 800);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // When tab changes, ensure we show skeletons if data is still loading
    if (activeTab === "video" && loading.reels) {
      setIsVisible(false);
      setTimeout(() => setIsVisible(true), 300);
    } else if (activeTab === "tag" && loading.tags) {
      setIsVisible(false);
      setTimeout(() => setIsVisible(true), 300);
    } else if (activeTab === "posts" && loading.posts) {
      setIsVisible(false);
      setTimeout(() => setIsVisible(true), 300);
    } else if (activeTab === "draft" && loading.drafts) {
      setIsVisible(false);
      setTimeout(() => setIsVisible(true), 300);
    }
  }, [activeTab, loading]);

  const handleClick = (event) => {
    event.stopPropagation();
  };

  const handleDialogOpen = (post_id) => {
    setPost(posts.find((item) => item.id === post_id));
    setOpen(true);
  };

  const handleTagDialogOpen = (post_id) => {
    setPost(tagData.find((item) => item.id === post_id));
    setOpen(true);
  };

  const handleVideoDialogOpen = (post_id) => {
    setPost(reelsData.find((item) => item.id === post_id));
    setOpen(true);
  };

  const handleDraftDialogOpen = (post_id) => {
    setPost(draftData.find((item) => item.id === post_id));
    setOpen(true);
  };

  const handleDialogClose = () => {
    setOpen(false);
    setPost(null);
  };

  const handleDeleteDialogOpen = (post, event) => {
    event.stopPropagation();
    setPost(post);
    setOpenDeleteDialog(true);
  };

  const handleDeleteDialogClose = () => {
    setOpenDeleteDialog(false);
    setDraftPost(null);
  };

  const handleDeleteDialogDraftOpen = (post, event) => {
    event.stopPropagation();
    setDraftPost(post);
    setOpenDeleteDraftDialog(true);
  };

  const handleDeleteDialogDraftClose = () => {
    setOpenDeleteDraftDialog(false);
    setPost(null);
  };

  const handleMenuOpen = (event, post) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setMenuPost(post);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuPost(null);
  };

  // Add handler to open edit dialog
  const handleEditDialogOpen = (post) => {
    setEditPost(post);
    setIsEditDialogOpen(true);
  };
  const handleEditDialogClose = () => {
    setEditPost(null);
    setIsEditDialogOpen(false);
  };
  const handleEditDialogSave = () => {
    setEditPost(null);
    setIsEditDialogOpen(false);
    if (fetchPosts) fetchPosts(1);
  };

  const renderPostMedia = (item) => {
    if (!item.files || item.files.length === 0) {
      return (
        <div className="bg-gray-100 w-full h-full flex items-center justify-center rounded-md">
          <p className="text-gray-400 text-xs">
            {localesData?.USER_WEB?.NO_IMAGE_OR_VIDEO_AVAILABLE}
          </p>
        </div>
      );
    }

    const firstFile = ensureHttps(item.files[0]);
    const fileType = getFileType(firstFile);

    if (fileType === "video") {
      // Get thumbnail from API response or fallback to video frame capture
      const thumbnailSrc =
        item.thumbnail_files?.length > 0
          ? ensureHttps(item.thumbnail_files[0])
          : "";

      return (
        <div className="relative h-full">
          <video
            className="w-full h-full object-cover rounded-lg"
            src={firstFile}
            muted
            playsInline
            preload="metadata"
            poster={thumbnailSrc}
          />

          <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center cursor-pointer rounded-lg">
            {thumbnailSrc && (
              <img
                src={thumbnailSrc}
                alt="Video thumbnail"
                className="absolute inset-0 w-full h-full object-cover rounded-lg"
                onError={(e) => {
                  e.target.style.display = "none";
                }}
              />
            )}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="w-12 h-12 text-white z-10"
              fill="currentColor"
              viewBox="0 0 24 24"
            >
              <path d="M8 5v14l11-7z" />
            </svg>
          </div>

          {item.files.length > 1 && (
            <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full z-20">
              +{item.files.length - 1}
            </div>
          )}
        </div>
      );
    }

    return (
      <div className="relative h-full">
        <img
          src={firstFile}
          alt="Post"
          className="w-full h-full object-cover rounded-md"
          loading="lazy"
        />
        {item.files.length > 1 && (
          <div className="absolute top-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded-full">
            +{item.files.length - 1}
          </div>
        )}
      </div>
    );
  };

  const getPlatformsFromPost = (post) => {
    if (!post) return {};

    return {
      facebook: post.facebook || false,
      instagram: post.instagram || false,
      linkedin: post.linkedin || false,
      pinterest: post.pinterest || false,
      vimeo: post.vimeo || false,
      youtube: post.youtube || false,
      dailymotion: post.dailymotion || false,
      reddit: post.reddit || false,
      tumblr: post.tumblr || false,
      twitter: post.twitter || false,
      thread: post.thread || false,
      tiktok: post.tiktok || false,
    };
  };

  const renderSkeletons = () => {
    return Array(4)
      .fill(0)
      .map((_, index) => <PostSkeleton key={`skeleton-${index}`} />);
  };

  return (
    <>
      <div className="flex justify-center items-center flex-wrap border-b border-[#E0E0E0] mb-8 gap-4 sm:gap-20 pt-1">
        {["posts", "video", "tag", "draft"].map((tab) => (
          <button
            key={tab}
            onClick={() => setActiveTab(tab)}
            className={`flex flex-row items-center gap-1 py-3 px-2 font-semibold text-md capitalize transition duration-200 ease-in-out ${
              activeTab === tab
                ? "border-b-2 border-[#563D39] text-[#563D39]"
                : "text-[#A9ABAD]"
            }`}
          >
            {/* Icons */}
            {tab === "posts" && (
              <img src={post_icon} className="h-[23px] w-[23px] me-px " />
            )}
            {tab === "video" && <img src={fleez} className="h-7 w-7" />}
            {tab === "tag" && <img src={tag} className="h-7 w-7" />}
            {tab === "draft" && <img src={text_post} className="h-7 w-7" />}

            {/* Text */}
            {tab === "posts"
              ? "Posts"
              : tab === "video"
              ? "Fleez"
              : tab === "tag"
              ? "Tag People"
              : "Draft"}
          </button>
        ))}
      </div>

      <div
        className={`bg-[#FFFFFF] grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 justify-center items-center transition-all duration-500 px-4 md:px-10 lg:px-20 min-h-[550px] ${
          isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        }`}
      >
        {/* Posts Tab */}
        <div
          className={`col-span-full w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 ${
            activeTab !== "posts" && "hidden"
          }`}
        >
          {loading.posts ? (
            renderSkeletons()
          ) : posts.length === 0 ? (
            <NoPost localesData={localesData} />
          ) : (
            posts.map((item) => (
              <div
                key={item.id}
                className="group bg-white rounded-xl border-[1px] border-[#E0E0E0] p-3 hover:shadow-md transition-shadow w-full h-[500px] flex flex-col"
                onClick={() => handleDialogOpen(item.id)}
              >
                {/* Header */}
                <div className="flex items-center gap-3 mb-2 h-12 relative">
                  <img
                    src={
                      userData?.profile_image ||
                      siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                    }
                    alt="Avatar"
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <div>
                    <p className="text-sm font-bold text-[#000000] truncate">
                      {userData?.name || "Modest Mikus"}
                    </p>
                    <p className="text-xs mt-1 font-normal text-[#8B98A5]">
                      @{userData?.username || "ModestMikus"} ·{" "}
                      {moment(item.created_at).format("MMM D, YYYY")}
                    </p>
                  </div>
                  <div className="absolute right-0 top-0">
                    <IconButton
                      aria-label="more"
                      aria-controls="post-menu"
                      aria-haspopup="true"
                      onClick={(e) => handleMenuOpen(e, item)}
                      size="small"
                      sx={{ color: "#A9ABAD" }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </div>
                </div>

                {/* Description */}
                <div className="h-12 mb-2">
                  <p className="text-sm leading-[20px] text-[#6B7280] truncate-2-lines text-ellipsis">
                    {item?.title &&
                    item.title.trim() !== "''" &&
                    item.title.trim() !== "" ? (
                      <strong className="font-semibold text-[#563D39]">
                        {renderHighlightedText(item.title, selectedUser)}
                      </strong>
                    ) : null}{" "}
                    {item?.description
                      ? renderHighlightedText(item.description, selectedUser)
                      : null}
                  </p>
                </div>

                {/* Media */}
                <div className="rounded-xl overflow-hidden flex-grow mb-2">
                  {renderPostMedia(item)}
                </div>

                {/* Social Media Icons */}
                <div className="mb-2 flex items-center justify-between gap-2 min-h-[40px]">
                  <div className="flex-1 overflow-x-auto pe-2 thin-scrollbar mt-3">
                    <SocialMediaIcons platforms={getPlatformsFromPost(item)} />
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Video Tab */}
        <div
          className={`col-span-full w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 ${
            activeTab !== "video" && "hidden"
          }`}
        >
          {loading.reels ? (
            renderSkeletons()
          ) : reelsData.length === 0 ? (
            <NoPost localesData={localesData} />
          ) : (
            reelsData.map((item) => (
              <div
                key={item.id}
                className="group bg-white rounded-xl border-[1px] border-[#E0E0E0] p-3 hover:shadow-md transition-shadow w-full h-[500px] flex flex-col"
                onClick={() => handleVideoDialogOpen(item.id)}
              >
                {/* Header */}
                <div className="flex items-center gap-3 mb-2 h-12 relative">
                  <img
                    src={
                      userData?.profile_image ||
                      siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                    }
                    alt="Avatar"
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <div>
                    <p className="text-sm font-bold text-[#000000] truncate">
                      {userData?.name || "Modest Mikus"}
                    </p>
                    <p className="text-xs font-normal text-[#8B98A5]">
                      @{userData?.username || "ModestMikus"} ·{" "}
                      {moment(item.created_at).format("MMM D, YYYY")}
                    </p>
                  </div>
                  <div className="absolute right-0 top-0">
                    <IconButton
                      aria-label="more"
                      aria-controls="post-menu"
                      aria-haspopup="true"
                      onClick={(e) => handleMenuOpen(e, item)}
                      size="small"
                      sx={{ color: "#A9ABAD" }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </div>
                </div>

                {/* Description */}
                <div className="h-12 mb-2">
                  <p className="text-sm leading-[20px] text-[#6B7280] truncate-2-lines text-ellipsis">
                    {item?.title &&
                    item.title.trim() !== "''" &&
                    item.title.trim() !== "" ? (
                      <strong className="font-semibold text-[#563D39]">
                        {renderHighlightedText(item.title, selectedUser)}
                      </strong>
                    ) : null}{" "}
                    {item?.description
                      ? renderHighlightedText(item.description, selectedUser)
                      : null}
                  </p>
                </div>

                {/* Media */}
                <div className="rounded-xl overflow-hidden flex-grow mb-2">
                  {renderPostMedia(item)}
                </div>

                {/* Social Media Icons */}
                <div className="mb-2 flex items-center justify-between gap-2 min-h-[40px]">
                  <div className="flex-1 overflow-x-auto pe-2 thin-scrollbar mt-3">
                    <SocialMediaIcons platforms={getPlatformsFromPost(item)} />
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Tag Tab */}
        <div
          className={`col-span-full w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 ${
            activeTab !== "tag" && "hidden"
          }`}
        >
          {loading.tags ? (
            renderSkeletons()
          ) : tagData.length === 0 ? (
            <NoPost localesData={localesData} />
          ) : (
            tagData.map((item) => (
              <div
                key={item.id}
                className="group bg-white rounded-xl border-[1px] border-[#E0E0E0] p-3 hover:shadow-md transition-shadow w-full h-[500px] flex flex-col"
                onClick={() => handleTagDialogOpen(item.id)}
              >
                {/* Header */}
                <div className="flex items-center gap-3 mb-2 h-12 relative">
                  <img
                    src={
                      item.user?.profile_image ||
                      siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                    }
                    alt="Avatar"
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <div>
                    <p className="text-sm font-bold text-[#000000] truncate">
                      {item.user?.name || "Modest Mikus"}
                    </p>
                    <p className="text-xs font-normal text-[#8B98A5]">
                      @{item.user?.username || "ModestMikus"} ·{" "}
                      {moment(item.created_at).format("MMM D, YYYY")}
                    </p>
                  </div>
                  {activeTab !== "tag" && (
                    <div className="absolute right-0 top-0">
                      <IconButton
                        aria-label="more"
                        aria-controls="post-menu"
                        aria-haspopup="true"
                        onClick={(e) => handleMenuOpen(e, item)}
                        size="small"
                        sx={{ color: "#A9ABAD" }}
                      >
                        <MoreVertIcon />
                      </IconButton>
                    </div>
                  )}
                </div>

                {/* Description */}
                <div className="h-12 mb-2">
                  <p className="text-sm leading-[20px] text-[#6B7280] truncate-2-lines text-ellipsis">
                    {item?.title &&
                    item.title.trim() !== "''" &&
                    item.title.trim() !== "" ? (
                      <strong className="font-semibold text-[#563D39]">
                        {renderHighlightedText(item.title, selectedUser)}
                      </strong>
                    ) : null}{" "}
                    {item?.description
                      ? renderHighlightedText(item.description, selectedUser)
                      : null}
                  </p>
                </div>

                {/* Media */}
                <div className="rounded-xl overflow-hidden flex-grow mb-2">
                  {renderPostMedia(item)}
                </div>

                {/* Social Media Icons */}
                <div className="flex-1 overflow-x-auto pe-2 thin-scrollbar mt-3">
                  <SocialMediaIcons platforms={getPlatformsFromPost(item)} />
                </div>
              </div>
            ))
          )}
        </div>

        {/* Draft Tab */}
        <div
          className={`col-span-full w-full grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 lg:gap-6 ${
            activeTab !== "draft" && "hidden"
          }`}
        >
          {loading.drafts ? (
            renderSkeletons()
          ) : draftData.length === 0 ? (
            <NoPost localesData={localesData} />
          ) : (
            draftData.map((item) => (
              <div
                key={item.id}
                className="group bg-white rounded-xl border-[1px] border-[#E0E0E0] p-3 hover:shadow-md transition-shadow w-full h-[600px] flex flex-col"
                onClick={() => handleDraftDialogOpen(item.id)}
              >
                {/* Header */}
                <div className="flex items-center gap-3 mb-2 h-12 relative">
                  <img
                    src={
                      userData?.profile_image ||
                      siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                    }
                    alt="Avatar"
                    className="w-8 h-8 rounded-full object-cover"
                  />
                  <div>
                    <p className="text-sm font-bold text-[#000000] truncate">
                      {userData?.name || "Modest Mikus"}
                    </p>
                    <p className="text-xs font-normal text-[#8B98A5]">
                      @{userData?.username || "ModestMikus"} ·{" "}
                      {moment(item.created_at).format("MMM D, YYYY")}
                    </p>
                  </div>
                  <div className="absolute right-0 top-0">
                    <IconButton
                      aria-label="more"
                      aria-controls="post-menu"
                      aria-haspopup="true"
                      onClick={(e) => handleMenuOpen(e, item)}
                      size="small"
                      sx={{ color: "#A9ABAD" }}
                    >
                      <MoreVertIcon />
                    </IconButton>
                  </div>
                </div>

                {/* Description */}
                <div className="h-12 mb-2">
                  <p className="text-sm leading-[20px] text-[#6B7280] line-clamp-2">
                    {item?.description
                      ? renderHighlightedText(item?.description, selectedUser)
                      : "No description available"}
                  </p>
                </div>

                {/* Media */}
                <div className="rounded-xl overflow-hidden flex-grow mb-2">
                  {renderPostMedia(item)}
                </div>

                {/* Social Media Icons */}
                <div className="mb-2 flex items-center justify-between gap-2 min-h-[40px]">
                  <div className="flex-1 overflow-x-auto pe-2 thin-scrollbar mt-3">
                    <SocialMediaIcons platforms={getPlatformsFromPost(item)} />
                  </div>
                  <div className="flex-shrink-0 flex items-center gap-[14px]">
                    <button
                      className="bg-[#563D391A] h-[35px] w-[112px] rounded-[60px] text-[#563D39] font-normal text-[14px] transition-colors hover:bg-[#563D3933] mt-3"
                      onClick={(event) => {
                        event.stopPropagation();
                        postDraftPosts(item.id);
                      }}
                    >
                      {isLoading ? "Uploading..." : "Upload"}
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Single Post Dialog */}
        <Dialog
          open={open}
          onClose={handleDialogClose}
          fullWidth
          maxWidth="md"
          PaperProps={{
            sx: {
              borderRadius: { xs: "10px", sm: "15px" },
              width: {
                xs: "90%",
                sm: "600px",
                md: "700px",
              },
              maxWidth: "100%",
              margin: { xs: "16px", sm: "32px" },
              overflow: "hidden",
            },
          }}
        >
          <DialogContent sx={{ padding: 0, backgroundColor: "white" }}>
            <Suspense
              fallback={
                <div className="w-full h-[500px] flex items-center justify-center">
                  <div className="w-4/5">
                    <Skeleton variant="rectangular" width="100%" height={300} />
                    <div className="mt-4">
                      <Skeleton variant="text" width="70%" height={24} />
                      <Skeleton variant="text" width="100%" height={20} />
                      <Skeleton variant="text" width="90%" height={20} />
                    </div>
                  </div>
                </div>
              }
            >
              {post && (
                <SinglePost
                  userData={activeTab === "tag" ? post.user : userData}
                  tagData={tagData}
                  draftData={draftData}
                  post={post}
                  open={open}
                  activeTab={activeTab}
                  handleDialogClose={handleDialogClose}
                />
              )}
            </Suspense>
          </DialogContent>
        </Dialog>

        {/* Delete Post Dialog */}
        <Suspense
          fallback={
            <div className="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
              <div className="bg-white p-6 rounded-lg shadow-lg w-80">
                <Skeleton variant="text" width="100%" height={40} />
                <Skeleton variant="text" width="80%" height={20} />
                <div className="flex justify-end mt-4 gap-2">
                  <Skeleton variant="rectangular" width={80} height={36} />
                  <Skeleton variant="rectangular" width={80} height={36} />
                </div>
              </div>
            </div>
          }
        >
          {openDeleteDialog && (
            <DeletePostModel
              open={openDeleteDialog}
              fetchPosts={
                activeTab === "video"
                  ? getVideoPosts
                  : activeTab === "draft"
                  ? getDraftPosts
                  : fetchPosts
              }
              handleDialogClose={handleDeleteDialogClose}
              post={post}
              postDeleteURL={URL.DELETE_POST}
              fetchProfile={fetchProfile}
            />
          )}

          {openDeleteDraftDialog && (
            <DeletePostDraftModel
              open={openDeleteDraftDialog}
              fetchPosts={getDraftPosts}
              handleDialogClose={handleDeleteDialogDraftClose}
              post={draftPost}
              postDeleteURL={URL.DELETE_DRAFT_POST}
              fetchProfile={fetchProfile}
            />
          )}
        </Suspense>
      </div>
      {/* Render the menu globally so it works for all cards */}
      <Popover
        id={Boolean(anchorEl) ? "post-popover" : undefined}
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        onClose={handleMenuClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
        transformOrigin={{ vertical: "top", horizontal: "right" }}
        PaperProps={{
          sx: {
            mt: 1.5,
            borderRadius: 3,
            minWidth: 180,
            boxShadow: "0px 8px 32px rgba(80, 80, 80, 0.18)",
            p: 1,
            background: "#fff",
            transition: "all 0.2s cubic-bezier(0.4,0,0.2,1)",
          },
        }}
        PopperProps={{
          modifiers: [
            {
              name: "offset",
              options: {
                offset: [-20, 10],
              },
            },
          ],
        }}
      >
        <div className="w-[230px] py-2">
          <div
            className="flex gap-2 items-center p-2.5 hover:rounded-[8px] hover:bg-[#F2F2F2] mb-1"
            onClick={() => {
              handleMenuClose();
              handleEditDialogOpen(menuPost);
            }}
          >
            <div className="bg-white rounded-[8px] border">
              <img src={Edit} className="h-7 w-7 p-1" />
            </div>
            <div>Edit</div>
          </div>
          <Divider className=" "  />
          <div
            className="flex gap-2 items-center p-2.5  hover:rounded-[8px] hover:bg-[#F2F2F2] mt-1"
            onClick={(e) => {
              handleMenuClose();
              if (activeTab === "draft") {
                handleDeleteDialogDraftOpen(menuPost, e);
              } else {
                handleDeleteDialogOpen(menuPost, e);
              }
            }}
          >
            <div className="bg-white rounded-[8px] border">
              <img src={Delete} className="h-7 w-7 p-1" />
            </div>
            <p>Delete</p>
          </div>
        </div>
      </Popover>

      {/* Render EditPostPopup dialog (reusing from Planner) */}
      {isEditDialogOpen && (
        <EditPostPopup
          post={editPost}
          onClose={handleEditDialogClose}
          onSave={handleEditDialogSave}
        />
      )}
    </>
  );
}

export default GridPost;
