import React, {
  useContext,
  useState,
  useRef,
  useEffect,
  Suspense,
} from "react";
import ReactPlayer from "react-player";
import { Badge, Dialog, DialogContent } from "@mui/material";
import { RiDeleteBin6Line } from "react-icons/ri";
import moment from "moment";
import SocialMediaIcons from "./SocialMediaIcons";
import { IntlContext } from "../../../App";
import { renderHighlightedText } from "../../../helpers/context/common";
import { getFileType, ensureHttps } from "../../../helpers/constant/utils";
import styled from "@emotion/styled";
import { URL } from "../../../helpers/constant/Url";
const SinglePost = React.lazy(() => import("./SiglePost"));
const DeletePostModel = React.lazy(() => import("./deletePost"));

const StyledBadge = styled(Badge)(({ theme }) => ({
  "& .MuiBadge-badge": {
    right: 4,
    top: 52,
    border: `1px solid gray`,
    padding: "6.5px",
    borderRadius: "50%",
    backgroundColor: "white",
    color: "black",
    fontSize: "0.75rem",
  },
}));

const PostList = ({ userData, fetchPosts, istable, posts }) => {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages || {};

  const [open, setOpen] = useState(false);
  const [post, setPost] = useState(null);
  const [thumb, setThumb] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showMore, setShowMore] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  const playerRef = useRef(null);
  useEffect(() => {
    setIsVisible(true);
  }, []);

  const truncateDescription = (text, limit) => {
    if (text.split(" ").length > limit) {
      return text.split(" ").slice(0, limit).join(" ") + "...";
    }
    return text;
  };
  const handleDialogClose = () => setOpen(false);
  const handleDeleteDialogClose = () => setDeleteDialogOpen(false);

  const handleClickOpen = (post) => {
    setPost(post);
    setOpen(true);
  };

  const handleDeleteOpen = (post) => {
    setPost(post);
    setDeleteDialogOpen(true);
  };
  const handleClick = (event) => {
    event.stopPropagation();
  };

  const renderThumbnail = (url) => {
    const fileType = getFileType(url);
    const safeUrl = ensureHttps(url);
    if (fileType === "video") {
      return (
        <div
          style={{
            width: 62,
            height: 62,
          }}
        >
          <ReactPlayer
            className="thumbnail-img"
            url={safeUrl}
            ref={playerRef}
            width="100%"
            height="100%"
            playing={false}
            controls={false}
            style={{
              borderWidth: "2px",
              borderColor: "#563D39",
              padding: "5px",
              borderRadius: "20px",
              height: "56px",
              width: "56px",
              objectFit: "cover",
            }}
          />
        </div>
      );
    }
    return (
      <img
        src={safeUrl}
        className="border-2 border-Red p-[5px] rounded-[20px] text-[7px] lg:rounded-[25px] object-cover h-14 w-14 sm:h-14 sm:w-14 lg:h-16 lg:w-16"
        alt="not available"
        loading="lazy"
      />
    );
  };

  return (
    <>
      <div
        className={`min-w-full container transition-all duration-500 ${
          isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        }`}
      >
        <div className="mt-5 sm:mt-5 overflow-x-auto channel-scrollbar">
          <table className="w-full table-auto text-sm text-left text-gray-500 dark:text-gray-400  border-separate border-spacing-y-4">
            <thead className="text-xs text-Red bg-profileCardBG dark:bg-gray-700 dark:text-gray-400">
              <tr className="text-Red text-md sm:text-[16px] text-center whitespace-nowrap">
                <th className="ps-4 md:ps-2 sm:px-2 py-8 font-Ubuntu">
                  {localesData?.USER_WEB?.USER_PROFILE?.PHOTO}
                </th>
                <th className="px-2 py-3 font-Ubuntu">
                  {localesData?.USER_WEB?.USER_PROFILE?.DESCRIPTION}
                </th>
                <th className="ps-10 sm:px-1 py-3 font-Ubuntu">
                  {localesData?.USER_WEB?.USER_PROFILE?.CONNECTED_CHANNELS}
                </th>
                <th className="ps-14 sm:ps-0 px-2 py-3 font-Ubuntu">
                  {localesData?.USER_WEB?.USER_PROFILE?.DATE}
                </th>
                <th className="p-6 py-3 font-Ubuntu">
                  {localesData?.USER_WEB?.USER_PROFILE?.ACTION}
                </th>
              </tr>
            </thead>
            <tbody>
              {posts.length ? (
                posts.map((item, postIndex) => (
                  <tr
                    key={postIndex}
                    className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 text-center hover:bg-primarBG cursor-pointer"
                    onClick={() => handleClickOpen(item)}
                  >
                    <td className="ms-3 sm-ms-0 px-4 py-3 font-medium text-Red dark:text-white flex items-center justify-center">
                      <StyledBadge
                        badgeContent={
                          // item?.files?.length ? item?.files?.length - 1 : 0
                          item?.files?.length > 1 ? item?.files?.length - 0 : 0
                        }
                      >
                        {renderThumbnail(item?.files?.[0])}
                      </StyledBadge>
                    </td>
                    <td className="ps-10 py-2 text-Red text-start w-[500px]">
                      <p
                        className={`font-Ubuntu break-words ${
                          item?.description?.length > 10
                            ? showMore
                              ? "line-clamp-none"
                              : "line-clamp-2"
                            : "line-clamp-1 sm:line-clamp-2 2xl:line-clamp-none"
                        }`}
                      >
                        {item?.description
                          ? renderHighlightedText(
                              showMore
                                ? item.description
                                : truncateDescription(item.description, 15)
                            )
                          : "No description available"}
                        {item?.description?.split(" ").length > 20 && (
                          <button
                            onClick={(event) => {
                              setShowMore(!showMore);
                              handleClick(event);
                            }}
                            className="text-Red text-sm font-bold"
                          >
                            {showMore ? "less" : "more"}
                          </button>
                        )}
                      </p>
                    </td>
                    <td className="">
                      <div className="flex -space-x-2 rtl:space-x-reverse justify-center">
                        {item && (
                          <SocialMediaIcons
                            platforms={item}
                            istable={istable}
                            imgStyle="w-10 h-10 sm:w-11 sm:h-11 border-[1px] bg-white border-gray-200 rounded-full dark:border-gray-800 p-1 "
                          />
                        )}
                      </div>
                    </td>
                    <td className="ps-10 sm:px-2 py-2 relative">
                      <div className="group whitespace-nowrap text-lightyellow">
                        {moment(item?.created_at)
                          .local()
                          .format("DD MMM, YYYY")}
                        <div className="absolute hidden group-hover:block w-max max-w-xs bg-gray-700 text-white text-xs rounded py-1 px-2 top-0 left-1/2 transform -translate-x-1/2">
                          {moment(item?.created_at)
                            .local()
                            .format("MMMM DD, YYYY hh:mm a")}
                        </div>
                      </div>
                    </td>
                    <td className="ps-10 sm:px-2 py-2 relative">
                      <div className="flex justify-center py-2 pr-6 text-Red bg-gray">
                        <div
                          className="bg-gray-100 rounded-md flex justify-center items-center h-11 w-11 hover:bg-[#efebe9] cursor-pointer sm:ms-8"
                          onClick={(event) => {
                            event.stopPropagation();
                            handleDeleteOpen(item);
                          }}
                        >
                          <RiDeleteBin6Line
                            style={{ height: "27px", width: "30px" }}
                          />
                        </div>
                      </div>
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td
                    colSpan="5"
                    className="text-center py-4 font-Ubuntu pt-  0 text-sm lg:text-lg"
                  >
                    <div className=" col-span-full my-5 sm:mt-5 lg:mt-10 2xl:mt-20 ">
                      <div className="text-center">
                        <p className="text-sm md:text-lg font-semibold text-Red">
                          {localesData?.USER_WEB?.NO_POST}
                        </p>
                        <p className="text-sm text-lightyellow py-2">
                          {
                            localesData?.USER_WEB
                              ?.BE_THE_FIRST_TO_SHARE_SOMTHING_AWESOME
                          }
                        </p>
                        <p className="text-sm md:text-md  font-semibold text-Red">
                          {localesData?.USER_WEB?.SHARE_YOUR_FIRST_PHOTO}
                        </p>
                      </div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      <>
        <>
          <Dialog
            open={open}
            onClose={handleDialogClose}
            fullWidth
            maxWidth="md"
            PaperProps={{
              sx: {
                borderRadius: { xs: "10px", sm: "15px", md: "20px" },
                width: {
                  xs: "70%",
                  sm: "500px",
                  md: "600px",
                  xl: "700px",
                },
              },
            }}
          >
            <DialogContent sx={{ backgroundColor: "white" }}>
              {post && (
                <SinglePost
                  open={open}
                  handleDialogClose={handleDialogClose}
                  onDeleteClick={() => handleDeleteOpen(post)}
                  userData={userData}
                  post={post}
                />
              )}
            </DialogContent>
          </Dialog>

          <Suspense fallback="">
            {deleteDialogOpen && (
              <DeletePostModel
                open={deleteDialogOpen}
                handleDialogClose={handleDeleteDialogClose}
                post={post}
                fetchPosts={fetchPosts}
                postDeleteURL={URL.DELETE_POST}
              />
            )}
          </Suspense>
        </>
      </>
    </>
  );
};

export default PostList;
