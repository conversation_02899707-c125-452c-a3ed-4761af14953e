import React, { useRef, useState } from "react";
import send from "../../../assets/images/Chating/send.svg";
import emoji from "../../../assets/images/Chating/emoji.svg";
import plus from "../../../assets/images/Chating/plus.svg";
import Picker from "@emoji-mart/react";
import data from "@emoji-mart/data";
import { Spinner } from "flowbite-react";

function Chatinput({
  newMessage,
  setNewMessage,
  messageInputRef,
  sendFacebookMessage,
  sendInstagramMessage,
  file,
  setFile,
  plateform,
  msgloader,
}) {
  const [showEmojiPicker, setShowEmojiPicker] = useState(false);
  const fileInputRef = useRef(null);

  const handleFileChange = (event) => {
    const selectedFiles = Array.from(event.target.files);
    setFile((prevFiles) => [...prevFiles, ...selectedFiles]);
  };

  const removeFile = (index) => {
    setFile((prevFiles) => prevFiles.filter((_, i) => i !== index));
  };

  const handleKeyDown = (event) => {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault();
      if (plateform === Number(2)) {
        sendInstagramMessage();
      } else {
        sendFacebookMessage();
      }
    }
  };

  const handleEmojiSelect = (emoji) => {
    setNewMessage((prevMessage) => prevMessage + emoji.native);
  };

  return (
    <div className="bg-white rounded-xl border-gray-200 p-1.5 sm:p-2 relative">
      <div className="mt-2 sm:mt-3">
        {file.length > 0 && (
          <div className="mt-2 sm:mt-3 flex gap-1.5 sm:gap-2 flex-wrap">
            {file.map((i, index) => (
              <div key={index} className="relative">
                {i.type.startsWith("image/") ? (
                  <img
                    src={URL.createObjectURL(i)}
                    alt={i.name}
                    className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 object-cover rounded-lg"
                  />
                ) : i.type.startsWith("video/") ? (
                  <video
                    src={URL.createObjectURL(i)}
                    controls
                    className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24 rounded-lg"
                  />
                ) : null}

                {/* Remove Button */}
                <button
                  onClick={() => removeFile(index)}
                  className="absolute -top-1.5 -right-1.5 sm:-top-2 sm:-right-2 bg-red-500 text-white rounded-full w-4 h-4 sm:w-5 sm:h-5 md:w-6 md:h-6 flex items-center justify-center text-[8px] sm:text-[10px] md:text-xs"
                >
                  ✕
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="flex items-center border border-gray-300 rounded-lg p-1 sm:p-1.5 md:p-2 relative">
        <input
          type="file"
          className="hidden"
          id="fileInput"
          accept="image/*, video/*"
          onChange={handleFileChange}
          ref={fileInputRef}
          multiple
        />
        <label
          htmlFor="fileInput"
          className="cursor-pointer p-1 sm:p-1.5 md:p-2 text-gray-500 hover:text-gray-700"
        >
          <img
            src={plus}
            alt="Upload"
            className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6"
          />
        </label>

        <div className="w-px h-4 sm:h-5 md:h-6 bg-gray-300 mx-1 sm:mx-1.5 md:mx-2"></div>

        <button
          type="button"
          className="text-gray-500 p-1 sm:p-1.5 md:p-2 hover:text-gray-700 relative"
          aria-label="Emoji"
          onClick={() => setShowEmojiPicker(!showEmojiPicker)}
        >
          <img
            src={emoji}
            alt="Emoji"
            className="h-4 w-4 sm:h-5 sm:w-5 md:h-6 md:w-6"
          />
        </button>

        {showEmojiPicker && (
          <div className="absolute bottom-10 sm:bottom-12 md:bottom-14 left-0 z-9">
            <Picker data={data} onEmojiSelect={handleEmojiSelect} />
          </div>
        )}

        <input
          ref={messageInputRef}
          type="text"
          value={newMessage}
          onKeyDown={handleKeyDown}
          onChange={(e) => setNewMessage(e.target.value)}
          placeholder="Type a message..."
          className="flex-grow mx-1 sm:mx-1.5 md:mx-2 p-1 sm:p-1.5 md:p-2 rounded-lg focus:ring-2 focus:ring-Red focus:border-none text-xs sm:text-sm md:text-base"
          aria-label="Message input"
        />

        <button
          onClick={sendFacebookMessage}
          className="p-1 sm:p-1.5 md:p-2 text-Red hover:text-Red-dark"
        >
          {msgloader ? (
            <Spinner />
          ) : (
            <img
              src={send}
              alt="Send"
              className="h-3.5 w-3.5 sm:h-4 sm:w-4 md:h-5 md:w-5"
            />
          )}
        </button>
      </div>
    </div>
  );
}

export default Chatinput;
