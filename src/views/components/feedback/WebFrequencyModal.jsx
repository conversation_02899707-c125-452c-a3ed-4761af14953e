import { Dialog } from "@mui/material";
import React, { useContext, useEffect, useState } from "react";
import { X } from "lucide-react";
import bg from "../../../assets/images/Feedback/bg.svg";
import option_1 from "../../../assets/images/Feedback/option_01.svg";
import option_2 from "../../../assets/images/Feedback/option_02.svg";
import option_3 from "../../../assets/images/Feedback/option_03.svg";
import option_1_light from "../../../assets/images/Feedback/option_01_light.svg";
import option_2_light from "../../../assets/images/Feedback/option_02_light.svg";
import option_3_light from "../../../assets/images/Feedback/option_03_light.svg";
import { IntlContext } from "../../../App";

function WebFrequencyModal({ open, onClose, onSelect }) {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const [selectedOption, setSelectedOption] = useState("Less 1 Hour");

  const handleOptionSelect = (option) => {
    setSelectedOption(option);
    onSelect(option);
    onClose();
  };

  useEffect(() => {
    console.log(selectedOption);
  }, [selectedOption]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      PaperProps={{
        style: {
          borderRadius: "12px",
          maxWidth: "460px",
          width: "100%",
          height: "450px",
          overflow: "hidden",
        },
      }}
    >
      <div className="font-Ubuntu p-5 rounded-[12px] h-[500px] bg-white">
        {/* Header with close button */}
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-medium text-[#563D39]">
            {localesData?.USER_WEB?.WEB_USING_FREQUENCY}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={20} />
          </button>
        </div>

        <div className="border-t border-gray-200 pt-4"></div>

        {/* Description */}
        <div className="text-center my-6">
          <p className="text-sm font-medium">
            {
              localesData?.USER_WEB
                ?.HOW_MANY_HOURS_HAVE_YOU_SPENT_ON_FLOWKAR_WEB
            }
          </p>
          <p className="text-sm mt-2">
            {localesData?.USER_WEB?.SHARE_YOUR_EXPERIENCE_WE_LOVE_TO_HEAR}
            <br />
            {localesData?.USER_WEB?.HOW_FLOWKAR_MADE_YOUR_JOURNEY_SMOOTHER}
          </p>
        </div>

        {/* Background and options container */}
        <div
          className="relative flex justify-center items-center h-64"
          style={{
            backgroundImage: `url(${bg})`,
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
            backgroundSize: "contain",
          }}
        >
          {/* Options */}
          <div className="flex justify-center items-end space-x-12 z-10 absolute bottom-[60px] left-[16px] w-full">
            {/* 1-2 Hours */}
            <div className="flex flex-col items-center">
              <div
                className={`w-16 h-16 rounded-full flex items-center justify-center cursor-pointer mb-2 transition-all relative group ${
                  selectedOption === "1-2 Hours"
                    ? "bg-[#563D39] scale-100"
                    : "bg-gray-200"
                }`}
                onClick={() => handleOptionSelect("1-2 Hours")}
              >
                {/* Default Image */}
                <img
                  src={option_2_light}
                  alt="Default"
                  className="transition-all duration-300 group-hover:opacity-0"
                />
                {/* Hover Image */}
                <img
                  src={option_2}
                  alt="Hover"
                  className="absolute inset-0 w-full h-full object-contain transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:scale-110"
                />
              </div>
              <span className="text-xs bg-gray-200 px-3 py-1 rounded-full mt-2 whitespace-nowrap">
                1-2 Hours
              </span>
            </div>

            {/* Less 1 Hour */}
            <div className="flex flex-col items-center">
              <div
                className={`w-20 h-20 rounded-full flex items-center justify-center cursor-pointer mt-10 mb-2 ml-4 transition-all relative group`}
                onClick={() => handleOptionSelect("Less 1 Hour")}
              >
                <img
                  src={option_1_light}
                  alt="Default"
                  className="transition-all duration-300 group-hover:opacity-0"
                />
                <img
                  src={option_1}
                  alt="Hover"
                  className="absolute inset-0 w-full h-full object-contain transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:scale-110"
                />
              </div>
              <span className="text-xs bg-gray-200 px-3 py-1 rounded-full mt-4 -mb-4 ml-4 whitespace-nowrap">
                Less 1 Hour
              </span>
            </div>

            {/* 3 & More Hours */}
            <div className="flex flex-col items-center pr-[15px]">
              <div
                className={`w-16 h-16 rounded-full flex items-center justify-center cursor-pointer mb-2 transition-all relative group ${
                  selectedOption === "3 & More Hours"
                    ? "bg-[#563D39] scale-100"
                    : "bg-gray-200"
                }`}
                onClick={() => handleOptionSelect("3 & More Hours")}
              >
                <img
                  src={option_3_light}
                  alt="Default"
                  className="transition-all duration-300 group-hover:opacity-0"
                />
                <img
                  src={option_3}
                  alt="Hover"
                  className="absolute inset-0 w-full h-full object-contain transition-all duration-300 opacity-0 group-hover:opacity-100 group-hover:scale-110"
                />
              </div>
              <span className="text-xs bg-gray-200 px-3 py-1 rounded-full mt-2 whitespace-nowrap">
                3 & More Hours
              </span>
            </div>
          </div>
        </div>
      </div>
    </Dialog>
  );
}

export default WebFrequencyModal;
