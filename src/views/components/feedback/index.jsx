import { Dialog } from "@mui/material";
import React, { useRef, useState, useEffect, useContext } from "react";
import { Mic, Square, Trash2, Volume2, Pause } from "lucide-react";
import { IntlContext } from "../../../App";
import demoProfile from "../../../assets/images/svg_icon/demoProfile.svg";
import star_feedback from "../../../assets/images/svg_icon/star_feedback.svg";
import star_feedback_filled from "../../../assets/images/svg_icon/star_feedback_filled.svg";
import mic from "../../../assets/images/Feedback/mic.svg";
import listen from "../../../assets/images/Feedback/listen.svg";
import bin from "../../../assets/images/Feedback/delete.svg";
import Rating from "@mui/material/Rating";
import { SvgIcon } from "@mui/material";
import Stack from "@mui/material/Stack";
import WebFrequencyModal from "../../../views/components/feedback/WebFrequencyModal.jsx";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL as ApiUrl } from "../../../helpers/constant/Url";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";

function Feedpage() {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const [isRecording, setIsRecording] = useState(false);
  const [audioURL, setAudioURL] = useState("");
  const [visualizerValues, setVisualizerValues] = useState(
    new Array(100).fill(0)
  );
  const [recordedVisualizerValues, setRecordedVisualizerValues] = useState([]);
  const [textFeedback, setTextFeedback] = useState("");
  const [userName, setUserName] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState("");
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const animationFrameRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const [rating, setRating] = useState(0);
  const [isPlaying, setIsPlaying] = useState(false);
  const audioElementRef = useRef(null);
  const visualizerContainerRef = useRef(null);
  const isDeletedRef = useRef(false);
  const [isFrequencyModalOpen, setIsFrequencyModalOpen] = useState(false);
  const [selectedFrequency, setSelectedFrequency] = useState("");
  const audioBlobRef = useRef(null);
  const subscription = localStorage.getItem("subscriptionId");
  const brandId = localStorage.getItem("BrandId");
  const user = localStorage.getItem("UserId");

  const openFrequencyModal = () => {
    setIsFrequencyModalOpen(true);
  };

  const closeFrequencyModal = () => {
    setIsFrequencyModalOpen(false);
  };

  const handleFrequencySelect = (frequency) => {
    setSelectedFrequency(frequency);
  };

  const CustomStar = (props) => (
    <SvgIcon {...props}>
      <path
        d="M11.3506 0.893555C11.6039 0.401401 12.3257 0.37036 12.6377 0.801758L12.6943 0.894531L15.3018 5.95898C15.5282 6.39868 15.9347 6.7131 16.4082 6.83691L16.6143 6.87793L22.4453 7.69043C23.0418 7.77357 23.2462 8.39792 22.9326 8.79004L22.8633 8.86523L18.6436 12.8076C18.2818 13.146 18.0873 13.6203 18.1113 14.1074L18.1357 14.3174L19.1309 19.8838C19.2185 20.3732 18.7024 20.8409 18.1523 20.6533L18.042 20.6074L12.8271 17.9795H12.8262C12.3851 17.7575 11.8706 17.7299 11.4111 17.8965L11.2178 17.9795L6.00293 20.6074C5.45136 20.8855 4.89485 20.4655 4.9043 19.9814L4.91309 19.8838L5.90918 14.3174C5.99687 13.8288 5.8591 13.3348 5.5459 12.96L5.40039 12.8076L1.18164 8.86523C0.798204 8.5068 0.942168 7.86534 1.48535 7.71387L1.59863 7.69043H1.59961L7.42969 6.87793C7.91987 6.80977 8.36017 6.54219 8.63574 6.13965L8.74316 5.95898L11.3506 0.894531V0.893555Z"
        stroke="#563D39"
      />
    </SvgIcon>
  );

  useEffect(() => {
    console.log(rating);
  }, [rating]);

  // Initialize audio context for visualization
  const initAudioContext = () => {
    if (!audioContextRef.current) {
      try {
        const AudioContext = window.AudioContext || window.webkitAudioContext;
        audioContextRef.current = new AudioContext();
        analyserRef.current = audioContextRef.current.createAnalyser();
        analyserRef.current.fftSize = 256;
      } catch (err) {
        console.error("Error creating audio context:", err);
      }
    }
  };

  // Consolidated cleanup function
  const cleanupRecording = () => {
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state !== "inactive"
    ) {
      mediaRecorderRef.current.stop();
      if (mediaRecorderRef.current.stream) {
        mediaRecorderRef.current.stream
          .getTracks()
          .forEach((track) => track.stop());
      }
    }
  };

  // Updates visualization during recording
  const updateVisualization = () => {
    if (!analyserRef.current) return;

    const analyser = analyserRef.current;
    const bufferLength = analyser.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);

    const draw = () => {
      animationFrameRef.current = requestAnimationFrame(draw);
      analyser.getByteFrequencyData(dataArray);

      // Calculate dynamic number of bars based on container width
      const containerWidth = visualizerContainerRef.current?.clientWidth || 300;

      // Adjust number of bars based on screen size
      let maxBars;
      if (window.innerWidth <= 480) {
        maxBars = Math.min(25, Math.floor(containerWidth / 4)); // Fewer bars on mobile
      } else if (window.innerWidth <= 768) {
        maxBars = Math.min(40, Math.floor(containerWidth / 5)); // Medium number on tablets
      } else {
        maxBars = Math.min(60, Math.floor(containerWidth / 6)); // More bars on desktop
      }

      // Sample the data array to get evenly distributed bars
      const sampledData = [];
      const step = Math.ceil(bufferLength / maxBars);

      for (let i = 0; i < maxBars; i++) {
        const startIndex = i * step;
        let sum = 0;
        let count = 0;

        for (let j = 0; j < step && startIndex + j < bufferLength; j++) {
          sum += dataArray[startIndex + j];
          count++;
        }

        // Scale the height based on screen size
        const maxHeight = window.innerWidth <= 480 ? 20 : 40;
        sampledData.push(Math.min(maxHeight, sum / count));
      }

      setVisualizerValues(sampledData);
    };

    draw();
  };

  const startRecording = async () => {
    try {
      // Reset the deletion flag when starting a new recording
      isDeletedRef.current = false;

      // Clean up any existing recording first
      cleanupRecording();

      // Initialize audio context
      initAudioContext();

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

      // Connect the stream to the analyser for visualization
      if (audioContextRef.current && analyserRef.current) {
        const source = audioContextRef.current.createMediaStreamSource(stream);
        source.connect(analyserRef.current);
      }

      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = () => {
        // Only create the audio URL if we're not deleting
        if (!isDeletedRef.current) {
          const audioBlob = new Blob(audioChunksRef.current, {
            type: "audio/wav",
          });
          audioBlobRef.current = audioBlob; // Save the blob for later submission
          const url = URL.createObjectURL(audioBlob);
          setAudioURL(url);
        }
      };

      mediaRecorder.start();
      setIsRecording(true);

      // Start visualizer animation
      updateVisualization();
    } catch (error) {
      console.error("Error accessing microphone:", error);
    }
  };

  const stopRecording = () => {
    if (
      mediaRecorderRef.current &&
      mediaRecorderRef.current.state === "recording"
    ) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream
        .getTracks()
        .forEach((track) => track.stop());
    }

    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }

    // Only save the visualizer values if we're not deleting
    if (!isDeletedRef.current) {
      setRecordedVisualizerValues([...visualizerValues]);
    }

    setIsRecording(false);
  };

  const deleteRecording = () => {
    // Set the deletion flag before stopping recording
    isDeletedRef.current = true;

    if (isRecording) {
      stopRecording();
    }

    if (audioURL) {
      URL.revokeObjectURL(audioURL);
      setAudioURL("");
    }

    setIsPlaying(false);

    if (audioElementRef.current) {
      audioElementRef.current.pause();
      audioElementRef.current.currentTime = 0;
    }

    audioBlobRef.current = null;
    setRecordedVisualizerValues([]);
    setVisualizerValues(new Array(100).fill(0));
  };

  const togglePlayback = () => {
    if (!audioElementRef.current || !audioURL) return;

    if (isPlaying) {
      audioElementRef.current.pause();
    } else {
      audioElementRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  // Handle audio element events
  useEffect(() => {
    const audioElement = audioElementRef.current;
    if (!audioElement) return;

    const handleEnded = () => {
      setIsPlaying(false);
    };

    audioElement.addEventListener("ended", handleEnded);

    return () => {
      audioElement.removeEventListener("ended", handleEnded);
    };
  }, [audioURL]);

  // Add responsive resizing for visualizer
  useEffect(() => {
    const handleResize = () => {
      // Re-render visualizer on window resize
      if (recordedVisualizerValues.length > 0) {
        // Maintain the recorded pattern but adjust for screen size
        const containerWidth =
          visualizerContainerRef.current?.clientWidth || 300;
        let maxBars;

        if (window.innerWidth <= 480) {
          maxBars = Math.min(80, Math.floor(containerWidth / 4));
        } else if (window.innerWidth <= 768) {
          maxBars = Math.min(120, Math.floor(containerWidth / 5));
        } else {
          maxBars = Math.min(300, Math.floor(containerWidth / 6));
        }

        // If we have more recorded values than maxBars, sample them down
        if (recordedVisualizerValues.length > maxBars) {
          const sampledValues = [];
          const step = Math.ceil(recordedVisualizerValues.length / maxBars);

          for (let i = 0; i < maxBars; i++) {
            const index = Math.min(
              i * step,
              recordedVisualizerValues.length - 1
            );
            sampledValues.push(recordedVisualizerValues[index]);
          }

          setRecordedVisualizerValues(sampledValues);
        }
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [recordedVisualizerValues]);

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      setSubmitMessage(""); // Reset message

      // Create FormData object
      const formData = new FormData();

      // Add all form data
      formData.append("app_using_frequency", selectedFrequency);
      formData.append("description", textFeedback);
      formData.append("stars", rating);

      // Add audio file if available
      if (audioBlobRef.current) {
        formData.append("file", audioBlobRef.current, "recording.wav");
      }
      // Send the data
      const response = await apiInstance.post(ApiUrl.FEEDBACK, formData, {
        headers: {
          "Content-Type": "multipart/form-data",
          Authorization: `Bearer ${token}`,
          subscription: subscription,
          brand: brandId,
          user: user,
        },
      });

      console.log("Feedback submitted successfully:", response.data.message);
      setSubmitMessage("Feedback submitted successfully!");

      // Reset form
      setUserName("");
      setSelectedFrequency("");
      setTextFeedback("");
      setRating(0);
      deleteRecording();
    } catch (error) {
      console.error("Error submitting feedback:", error);
      setSubmitMessage("Error submitting feedback. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Validate form
  const isFormValid = () => {
    return userName.trim() !== "" && selectedFrequency !== "";
  };

  // Calculate responsive bar widths
  const getBarWidth = () => {
    if (window.innerWidth <= 480) return "1.5px";
    if (window.innerWidth <= 768) return "2px";
    return "3px";
  };

  // Calculate responsive bar spacing
  const getBarSpacing = () => {
    if (window.innerWidth <= 480) return "2px";
    return "4px";
  };

  return (
    <>
      <div className="p-3 sm:p-4 md:p-6 min-h-screen overflow-y-scroll md:ps-6 lg:ps-12 md:pe-6 lg:pe-12 bg-[#FFFFFF] w-full mt-4 sm:mt-6 font-Ubuntu">
        <h2 className="text-xl sm:text-2xl font-bold mb-4 sm:mb-6">
          {localesData?.USER_WEB?.FEEDBACK}
        </h2>

        {/* Name Input and Frequency - Responsive Layout */}
        <div className="mb-4 flex flex-col md:flex-row justify-center items-stretch gap-3 sm:gap-5">
          <input
            type="text"
            placeholder="Name"
            className="w-full border border-[#E0E0E0] rounded-lg p-2 sm:p-3 mb-3 md:mb-0"
            value={userName}
            onChange={(e) => setUserName(e.target.value)}
          />

          {/* Dropdown */}
          <div className="relative w-full">
            <div
              className="w-full border border-[#E0E0E0] rounded-lg p-2 sm:p-3 flex justify-between items-center cursor-pointer"
              onClick={openFrequencyModal}
            >
              <span className="text-gray-500 text-sm sm:text-base truncate">
                {selectedFrequency || "Web Using Frequency"}
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400 flex-shrink-0"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>

        {/* Testimonial Box */}
        <div className="border border-[#E0E0E0] rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
          <div className="flex items-center mb-2 sm:mb-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gray-500 rounded-full overflow-hidden mr-2 sm:mr-3 flex-shrink-0">
              <img
                src={demoProfile}
                alt="User"
                className="w-full h-full object-cover"
              />
            </div>
            <span className="font-bold text-[#000000] text-sm sm:text-base truncate">
              {userName || "John Doe"}
            </span>
          </div>

          <textarea
            className="text-xs sm:text-sm mb-2 mt-3 sm:mt-5 text-[#000000] w-full h-24 sm:h-32 p-2 border-none rounded-lg resize-none"
            placeholder="Share your thoughts here..."
            value={textFeedback}
            onChange={(e) => setTextFeedback(e.target.value)}
          ></textarea>
        </div>

        {/* Audio Waveform and Controls - Responsive Section */}
        <div className="flex flex-col items-center justify-center w-full sm:w-[90%] md:w-[80%] lg:w-[70%] mx-auto">
          <div className="w-full rounded-lg p-2 sm:p-4 border border-[#E0E0E0]">
            {/* IMPORTANT: Fixed layout with flex and explicit min-widths to prevent disappearing controls */}
            <div className="flex items-center w-full">
              {/* Record/Stop Button - Fixed min-width to ensure it never collapses */}
              <div className="min-w-[40px] w-10 sm:w-12 md:w-16 flex items-center justify-center flex-shrink-0">
                <button
                  className="w-6 h-6 sm:w-8 sm:h-8 rounded-full flex items-center justify-center"
                  onClick={isRecording ? stopRecording : startRecording}
                >
                  {isRecording ? (
                    <Square size={12} className="sm:size-5" color="#563D39" />
                  ) : (
                    <img
                      src={mic}
                      alt="mic"
                      className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12"
                    />
                  )}
                </button>
              </div>

              {/* Visualizer Container - Uses flex-1 to take available space */}
              <div className="flex-1 mx-2 sm:mx-3 md:mx-4">
                {/* Hidden audio element for playback */}
                {audioURL && (
                  <audio
                    ref={audioElementRef}
                    src={audioURL}
                    className="hidden"
                  />
                )}

                {/* Responsive height container with overflow hidden and vertical alignment */}
                <div
                  ref={visualizerContainerRef}
                  className="flex items-center justify-start h-10 sm:h-12 md:h-16 overflow-hidden"
                  style={{
                    minHeight: window.innerWidth <= 480 ? "40px" : "48px",
                  }}
                >
                  <div
                    className="flex items-center h-full"
                    style={{ gap: getBarSpacing() }}
                  >
                    {isRecording
                      ? visualizerValues.map((value, index) => (
                          <div
                            key={index}
                            className="bg-gray-700 rounded-sm"
                            style={{
                              width: getBarWidth(),
                              height: `${value}px`,
                              transition: "height 0.1s ease",
                              alignSelf: "center",
                            }}
                          />
                        ))
                      : recordedVisualizerValues.length > 0
                      ? recordedVisualizerValues.map((value, index) => (
                          <div
                            key={index}
                            className="bg-gray-700 rounded-sm"
                            style={{
                              width: getBarWidth(),
                              height: `${value}px`,
                              alignSelf: "center",
                            }}
                          />
                        ))
                      : Array(
                          window.innerWidth <= 480
                            ? 20
                            : window.innerWidth <= 768
                            ? 30
                            : 40
                        )
                          .fill(0)
                          .map((_, index) => (
                            <div
                              key={index}
                              className="bg-gray-300 rounded-sm"
                              style={{
                                width: getBarWidth(),
                                height: "2px",
                                alignSelf: "center",
                              }}
                            />
                          ))}
                  </div>
                </div>
              </div>

              {/* Control buttons - Fixed width container with flex-shrink-0 to prevent disappearing */}
              <div className="min-w-[60px] w-16 sm:w-20 flex justify-end items-center flex-shrink-0">
                <div className="flex space-x-2 sm:space-x-3 md:space-x-4">
                  {/* Play/Pause Button */}
                  <button
                    className={`text-[#563D39] ${
                      !audioURL ? "" : ""
                    } p-1 flex-shrink-0`}
                    disabled={!audioURL}
                    onClick={togglePlayback}
                  >
                    {isPlaying ? (
                      <Pause size={16} className="sm:size-5 md:size-5" />
                    ) : (
                      <img
                        src={listen}
                        alt="listen"
                        className="w-4 h-4 sm:w-5 sm:h-5"
                      />
                    )}
                  </button>
                  {/* Delete Button */}
                  <button
                    className={`text-stone-800 ${
                      !audioURL && !isRecording ? "" : ""
                    } p-1 flex-shrink-0`}
                    disabled={!audioURL && !isRecording}
                    onClick={deleteRecording}
                  >
                    <img
                      src={bin}
                      alt="trash"
                      className="w-4 h-4 sm:w-5 sm:h-5"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Audio Playing Status */}
        {audioURL && (
          <div className="text-center mt-1 sm:mt-2 text-xs sm:text-sm text-gray-600">
            {isPlaying
              ? "Playing recorded audio..."
              : "Recorded audio ready for playback"}
          </div>
        )}

        {/* Rating - Responsive */}
        <div className="mb-4 sm:mb-6 mt-3 sm:mt-4">
          <h3 className="text-center font-medium mb-3 sm:mb-6 mt-3 sm:mt-6 text-[#000000] text-base sm:text-lg md:text-xl">
            {localesData?.USER_WEB?.RATE}
          </h3>

          <div className="flex justify-center">
            <Stack spacing={25} sx={{ alignItems: "center" }}>
              <Rating
                name="half-rating"
                value={rating}
                onChange={(event, newValue) => {
                  setRating(parseFloat(newValue).toFixed(1));
                }}
                defaultValue={2.5}
                precision={0.5}
                sx={{
                  display: "flex",
                  gap: { xs: "8px", sm: "16px" },
                  "& .MuiRating-icon": {
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "center",
                    borderRadius: "50%",
                    backgroundColor: "#563D3921",
                    width: { xs: "56px", sm: "56px" },
                    height: { xs: "56", sm: "56px" },
                    fontSize: { xs: "16px", sm: "24px" },
                  },
                  "& .MuiRating-iconFilled": {
                    color: "#563D39",
                  },
                  "& .MuiRating-iconEmpty": {
                    color: "#563D3921",
                  },
                  "& .MuiRating-iconHover": {
                    color: "#563D39",
                  },
                  "& .MuiRating-decimal": {
                    position: "relative",
                  },
                  "& .MuiRating-iconActive": {
                    transform: "none",
                  },
                }}
                icon={<CustomStar />}
                emptyIcon={<CustomStar />}
              />
            </Stack>
          </div>
        </div>

        {/* Submit Status Message */}
        {submitMessage && (
          <div
            className={`text-center text-xs sm:text-base ${
              submitMessage.includes("Error")
                ? "text-red-500"
                : "text-green-500"
            }`}
          >
            {submitMessage}
          </div>
        )}

        {/* Submit Button - Responsive */}
        <div className="flex justify-center ">
          <button
            className={`bg-[#563D39] text-white px-4 sm:px-8 py-2 sm:py-3 rounded-[8px] sm:rounded-[12px] w-[180px] sm:w-[220px] md:w-[260px] mt-4 sm:mt-8 text-sm sm:text-base mb-10 ${
              !isFormValid() || isSubmitting
                ? "opacity-50 cursor-not-allowed"
                : ""
            }`}
            onClick={handleSubmit}
            disabled={!isFormValid() || isSubmitting}
          >
            {isSubmitting ? "Submitting..." : "Submit"}
          </button>
        </div>
      </div>
      <WebFrequencyModal
        open={isFrequencyModalOpen}
        onClose={closeFrequencyModal}
        onSelect={handleFrequencySelect}
      />
    </>
  );
}

export default Feedpage;
