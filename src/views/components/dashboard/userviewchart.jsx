import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Area,
} from "recharts";
import moment from "moment";
import _ from "lodash";
import UserChartOption from "./UserchartOption";

const UserViewChart = ({ graphData, timePeriod, onSelect, setRange }) => {
  const [data, setData] = useState([]);
  useEffect(() => {
    if (graphData?.likes) {
      const chartData = graphData?.likes?.map((item, index) => ({
        name: timePeriod == "this_week" ? item?.day : item?.date,
        Likes: item?.count,
        Comments: graphData?.comments[index]?.count,
        Posts: graphData?.posts[index]?.count,
      }));
      if (timePeriod === "this_year") {
        const chartDataWithMonth = chartData.map((item) => {
          const dateMoment = moment(
            item.name,
            timePeriod === "this_week" ? "dddd" : "DD-MM-YYYY"
          );
          return {
            ...item,
            monthYear: dateMoment.format("MMMM YYYY"),
          };
        });

        const groupedByMonth = _.groupBy(chartDataWithMonth, "monthYear");

        const result = _.map(groupedByMonth, (items, monthYear) => {
          const totalLikes = _.sumBy(items, "Likes");
          const totalComments = _.sumBy(items, "Comments");
          const totalPosts = _.sumBy(items, "Posts");

          return {
            name: monthYear,
            Likes: totalLikes,
            Comments: totalComments,
            Posts: totalPosts,
          };
        });
        setData(result);
      } else {
        setData(chartData);
      }
    }
  }, [graphData]);

  const renderCustomLegend = ({ payload }) => {
    return (
      <div style={{ display: "flex", justifyContent: "flex-end", padding: 10 }}>
        <ul style={{ display: "flex", gap: 20 }}>
          {payload.map((entry, index) => (
            <li
              key={`item-${index}`}
              style={{
                display: "flex",
                alignItems: "center",
                gap: 6,
                fontSize: 12,
                color: "gray",
              }}
            >
              <span
                style={{
                  width: 8,
                  height: 8,
                  borderRadius: "50%",
                  backgroundColor: entry.color,
                  display: "inline-block",
                }}
              />
              {entry.value}
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className="bg-[#F9F9F9] p-1 rounded-xl flex flex-col font-Ubuntu">
      <div className="flex justify-between items-center px-4 pt-2 ">
        <p className="font-bold text-[#563D39] text-[26px]">Growth</p>
        <UserChartOption onSelect={onSelect} setRange={setRange} />
      </div>
      <div className="flex-grow sm:pe-5 ">
        <ResponsiveContainer
          width="100%"
          height={428}
          className="user-dashboard"
        >
          <AreaChart
            data={data}
            margin={{ top: 0, right: 5, left: -8, bottom: 0 }}
          >
            <defs>
              <linearGradient
                id="gradientFillLikes"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="0%" stopColor="#563D39" stopOpacity={0.3} />
                <stop offset="100%" stopColor="#563D39" stopOpacity={0.05} />
              </linearGradient>
              <linearGradient
                id="gradientFillPosts"
                x1="0"
                y1="0"
                x2="0"
                y2="1"
              >
                <stop offset="0%" stopColor="#AA8882" stopOpacity={0.3} />
                <stop offset="100%" stopColor="#AA8882" stopOpacity={0.05} />
              </linearGradient>
            </defs>

            <CartesianGrid
              vertical={false}
              horizontal={true}
              strokeDasharray="0 0"
              style={{ opacity: 0.3 }}
            />

            <XAxis dataKey="name" tick={{ fontSize: 12, fill: "gray" }} />
            <YAxis tick={{ fontSize: 12, fill: "gray" }} />
            <Tooltip
              contentStyle={{
                fontSize: "14px",
                backgroundColor: "#fff",
                border: "1px solid #ddd",
              }}
              labelStyle={{ fontSize: "12px" }}
            />
            <Legend
              layout="horizontal"
              align="right"
              verticalAlign="top"
              content={renderCustomLegend}
            />

            {/* Area for Likes with gradient fill */}
            <Area
              type="monotone"
              dataKey="Likes"
              stroke="#563D39"
              strokeWidth={3}
              fill="url(#gradientFillLikes)"
              fillOpacity={1}
            />

            {/* Area for Posts with gradient fill */}
            <Area
              type="monotone"
              dataKey="Posts"
              stroke="#AA8882"
              strokeWidth={3}
              fill="url(#gradientFillPosts)"
              fillOpacity={1}
            />
          </AreaChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default UserViewChart;
