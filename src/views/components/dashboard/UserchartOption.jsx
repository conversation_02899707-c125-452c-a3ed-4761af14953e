import React, { useContext, useEffect, useRef, useState } from "react";
import { Menu } from "@headlessui/react";
import { BsChevronDown } from "react-icons/bs";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { IntlContext } from "../../../App";
import dropDown from "../../../assets/images/svg_icon/dropDown.svg";

export default function UserChartOption({ onSelect, setRange }) {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [startDate, setStartDate] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [selectedOption, setSelectedOption] = useState("this_week");
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const calendarRef = useRef(null);

  const optionLabels = {
    this_week: localesData?.USER_WEB?.USER_PROFILE?.THIS_WEEK || "This Week",
    this_month: localesData?.USER_WEB?.USER_PROFILE?.THIS_MONTH || "This Month",
    this_year: localesData?.USER_WEB?.USER_PROFILE?.THIS_YEAR || "This Year",
    custom_range:
      localesData?.USER_WEB?.USER_PROFILE?.CELENDER || "Custom Range",
  };

  const handleOptionSelect = (value) => {
    setSelectedOption(value);
    onSelect(value);
  };

  const handleCalendarClick = () => {
    setIsCalendarOpen(!isCalendarOpen);
  };

  const handleDateChange = (dates) => {
    const [start, end] = dates;
    setStartDate(start);
    setEndDate(end);
    if (start && end) {
      setIsCalendarOpen(false);
      setRange({ fromDate: start, toDate: end });
      handleOptionSelect("custom_range");
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        setIsCalendarOpen(false);
      }
    };

    if (isCalendarOpen) {
      window.addEventListener("mousedown", handleClickOutside);
    } else {
      window.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      window.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isCalendarOpen]);

  return (
    <div className="relative inline-block text-left border rounded-md">
      <Menu as="div">
        <div>
          <Menu.Button className="flex items-center gap-2 px-4 py-2 text-sm font-normal bg-[#563d3905]  rounded-[6px] shadow-sm hover:bg-gray-50 focus:outline-none text-[#000000] ">
            {selectedOption ? optionLabels[selectedOption] : ""}
            <img src={dropDown} alt="dropDown" className="" />
          </Menu.Button>
        </div>
        <Menu.Items className="absolute right-0 z-10 mt-2 w-36 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
          <div className="py-1">
            <Menu.Item>
              <a
                href="#"
                onClick={() => handleOptionSelect("this_week")}
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                {optionLabels.this_week}
              </a>
            </Menu.Item>
            <Menu.Item>
              <a
                href="#"
                onClick={() => handleOptionSelect("this_month")}
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                {optionLabels.this_month}
              </a>
            </Menu.Item>
            <Menu.Item>
              <a
                href="#"
                onClick={() => handleOptionSelect("this_year")}
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                {optionLabels.this_year}
              </a>
            </Menu.Item>
            <Menu.Item>
              <a
                href="#"
                onClick={handleCalendarClick}
                className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                {optionLabels.custom_range}
              </a>
            </Menu.Item>
          </div>
        </Menu.Items>
      </Menu>

      {isCalendarOpen && (
        <div className="absolute right-0 z-20 mt-2" ref={calendarRef}>
          <DatePicker
            selected={startDate}
            onChange={handleDateChange}
            startDate={startDate}
            endDate={endDate}
            selectsRange
            inline
            maxDate={new Date()}
          />
        </div>
      )}
    </div>
  );
}
