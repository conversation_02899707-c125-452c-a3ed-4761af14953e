import { Dialog } from "@mui/material";
import React, { useContext, useState } from "react";
import { IntlContext } from "../../../App";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import { setApiMessage } from "../../../helpers/context/toaster";

const DeleteScheduledPostModel = ({
  open,
  handleDialogClose,
  post,
  fetchPosts,
  postDeleteURL,
  fetchProfile,
}) => {
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const isUserDeletion = URL.ADMIN_USER_DELETE === postDeleteURL;
  const [loading, setLoading] = useState(false);

  const handleDelete = async () => {
    if (!post || !post.id) {
      setApiMessage("error", "No post selected for deletion");
      return;
    }

    setLoading(true);
    try {
      const response = await apiInstance.get(URL.SCHEDULED_POST_DELETE, {
        params: {
          post_id: post.id,
        },
      });

      // Success handling
      if (response.status === 200) {
        setApiMessage("success", "Post deleted successfully");

        // Close the modal
        handleDialogClose();
      }
    } catch (error) {
      console.log(error);
      setApiMessage(
        "error",
        error?.response?.data?.message || "Failed to delete post"
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={handleDialogClose}
        className="bg-transparent"
      >
        <div className="bg-white p-7 rounded-xl shadow-lg w-full max-w-md">
          <div className="flex flex-col gap-3 items-center">
            <div className="mb-4">
              <img
                src={intlContext?.socialIcons?.DELETE_POST_CONFIRMATION}
                alt="Delete Image"
                className="w-26 h-26"
              />
            </div>
            <p className="text-[11px] sm:text-sm md:text-lg font-Ubuntu mb-4 whitespace-nowrap">
              {isUserDeletion
                ? localesData?.USER_WEB?.DELETE_USER_CONFIRMATION
                : localesData?.USER_WEB?.DELETE_POST_CONFIRMATION}
            </p>
            <div className="flex space-x-6 sm:space-x-8 md:space-x-12">
              <button
                className="text-Red py-1 sm:py-3 w-20 sm:w-24 md:w-28 lg:w-36 rounded-xl hover:bg-[#EFEBE9] text-[13px] sm:text-[15px] font-semibold border border-pink-100"
                onClick={() => handleDialogClose()}
                disabled={loading}
              >
                {localesData?.USER_WEB?.CANCEL}
              </button>
              <button
                className="bg-Red text-white py-1 sm:py-3 w-20 sm:w-24 md:w-28 lg:w-36 text-[13px] sm:text-[15px] rounded-xl font-semibold disabled:opacity-50"
                onClick={handleDelete}
                disabled={loading}
              >
                {loading ? "Deleting..." : localesData?.USER_WEB?.DELETE}
              </button>
            </div>
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default DeleteScheduledPostModel;
