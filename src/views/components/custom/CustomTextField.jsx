import { TextField } from "@mui/material";
import { withStyles } from "@mui/styles";

export const CustomTextField = withStyles({
  root: {
    "& .MuiInputBase-root": {
      fontFamily: "Ubuntu, sans-serif",
      fontWeight: "500",
      fontSize: "15px",
      color: "#674941",
    },
    "& .MuiOutlinedInput-root": {
      borderRadius: (props) => props.borderRadius || "8px",
      "& .MuiOutlinedInput-input": {
        padding: "17px 15px",
        fontFamily: "Ubuntu, sans-serif",
        fontSize: "15px",
        fontWeight: "500",
        color: "#674941",
        "&::placeholder": {
          color: "#674941",
          fontSize: "14px",
          fontFamily: "Ubuntu, sans-serif",
          fontWeight: "500",
        },
      },
      "& .MuiInputBase-inputMultiline": {
        padding: "0px",
        margin: "0px",
      },
      "& .MuiOutlinedInput-notchedOutline": {
      border: (props) => props.bordercolor || "1px solid #D4D6D8",
        "& legend": {
          marginLeft: "25px", // Adjust the value as needed
        },
      },
      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
        borderColor: (props) => props.bordercolor || "#4582c3",
        borderWidth: "1px",
      },
      "&:hover .MuiOutlinedInput-notchedOutline": {
        borderColor: (props) => props.bordercolor || "#4582c3",
      },

      "& .Mui-disabled": {
        cursor: "not-allowed",
      },
    },
    "& .MuiInputLabel-root": {
      fontFamily: "Ubuntu, sans-serif",
      color: "#674941",
      fontSize: "14px",
      fontWeight: "500",
      "&.Mui-focused": {
        color: "#674941",
      },
      "&.Mui-error": {
        color: "#f44336",
      },
    },
    "& .MuiInputBase-input:focus": {
      boxShadow: "none",
    },
  },
})(TextField);
