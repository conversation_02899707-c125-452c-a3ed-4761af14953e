import { FormControl } from "@mui/material";
import { styled } from "@mui/material/styles";

export const CustomFormControl = styled(FormControl, {
  shouldForwardProp: (prop) =>
    prop !== "borderColor" && prop !== "borderRadius",
})(({ borderColor = "#D4D6D8", borderRadius = "8px" }) => ({
  "& .MuiOutlinedInput-root": {
    borderRadius,
    "& .MuiOutlinedInput-input": {
      padding: "17px 15px",
      fontFamily: "Ubuntu, sans-serif",
      fontSize: "15px",
      fontWeight: 500,
      "&::placeholder": {
        color: "#252520",
        fontSize: "14px",
        fontFamily: "Ubuntu, sans-serif",
        fontWeight: 500,
      },
    },
    "& .MuiOutlinedInput-notchedOutline": {
      border: `1px solid ${borderColor}`,
    },
    "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
      borderColor,
      borderWidth: "1px",
    },
    "&:hover .MuiOutlinedInput-notchedOutline": {
      borderColor,
    },
    "&.Mui-error .MuiOutlinedInput-notchedOutline": {
      borderColor: "#D4D6D8",
    },
  },
  "& .MuiInputLabel-root": {
    fontFamily: "Ubuntu, sans-serif",
    fontSize: "14px",
    color: "black",
    "&.Mui-focused": {
      color: "black",
    },
    "&.Mui-error": {
      color: "#000000",
    },
  },
  "& .MuiFormHelperText-root": {
    fontFamily: "Ubuntu, sans-serif",
    fontSize: "14px",
    color: "#000000",
  },
}));
