import React from "react";
import datanotfound from "../../../assets/images/datanotfound.svg";
import datanotBG from "../../../assets/images/datanotBG.svg";

const DataNotFound = () => {
  return (
    <div className="flex flex-col items-center justify-center h-screen ">
      <div
        className="absolute inset-0 z-0 bg-center bg-cover"
        style={{ backgroundImage: `url(${datanotBG})` }}
      ></div>
      <div className="relative z-10 flex flex-col items-center">
        <img
          src={datanotfound}
          alt="Data Not Found"
          className="max-w-xs mb-2 md:max-w-2xl"
        />
        <div className="mb-8 text-center">
          <p className="text-2xl font-bold font-Ubuntu "> Data not found.</p>
          <p className="pt-2 text-sm text-gray-400 font-Ubuntu">
            Oops! We couldn't find any data.
          </p>
        </div>
        <div className="flex justify-center">
          <button className="px-8 py-2 font-bold text-white bg-Red  rounded-xl">
            Refresh
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataNotFound;
