import React, { useContext, useEffect, useState } from "react";
import { AiOutlineSearch, AiOutlinePlus, AiOutlineMore } from "react-icons/ai";
import { IoCloseOutline } from "react-icons/io5";
import { MdKeyboardArrowDown, MdEdit, MdDelete } from "react-icons/md";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import Adduser from "./adduser";
import { IntlContext } from "../../../App";
import Loader from "../../../helpers/UI/Loader";
import siteConstant from "../../../helpers/constant/siteConstant";
import crown from "../../../assets/images/user-manage/crown.svg";
import crownLight from "../../../assets/images/user-manage/crown_light.svg";
import tick from "../../../assets/images/user-manage/tick.svg";
import Dialog from "@mui/material/Dialog";
import DialogContent from "@mui/material/DialogContent";
import DialogTitle from "@mui/material/DialogTitle";
import IconButton from "@mui/material/IconButton";
import UserManagementSkeleton from "../skeletons/UserManagementSkeleton.jsx";

function UserManagePage() {
  const [activeTab, setActiveTab] = useState("Users");
  const [userSubTab, setUserSubTab] = useState("Invited");
  const [isModalTab, setIsModalTab] = useState("Users");
  const [searchQuery, setSearchQuery] = useState("");
  const [dropdownOpen, setDropdownOpen] = useState(null);
  const [selectedBrand, setSelectedBrand] = useState("Any brand");
  const [isLoading, setIsLoading] = useState(true);
  const [addUserDialog, setAddUserDialog] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [invitee, setInvitee] = useState([]);
  const [invited, setInvited] = useState([]);
  const [pendingInvited, setPendingInvited] = useState([]);
  const [pendingInvitee, setPendingInvitee] = useState([]);
  const [rolesData, setRolesData] = useState([]);

  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const handleOpenRoleModal = (role) => {
    setSelectedRole(role);
    setIsModalOpen(true);
    setIsModalTab("Users");
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedRole(null);
  };

  // Dummy data for roles and permissions
  const rolePermissions = [
    {
      id: 1,
      name: "Client",
      permissionsCount: 5,
      permissions: ["Post", "Messages", "Analytics"],
      users: 3,
    },
    {
      id: 2,
      name: "Content Creator",
      permissionsCount: 0,
      permissions: [],
      users: 0,
    },
    {
      id: 3,
      name: "Analytics",
      permissionsCount: 0,
      permissions: [],
      users: 0,
    },
    {
      id: 4,
      name: "Editor",
      permissionsCount: 0,
      permissions: [],
      users: 0,
    },
    {
      id: 5,
      name: "Manager",
      permissionsCount: 0,
      permissions: [],
      users: 0,
    },
  ];

  // Dummy data for pending requests
  const pendingRequests = [
    {
      id: 1,
      email: "<EMAIL>",
      profile_image: "",
      requestedRole: "Editor",
      requestDate: "2025-04-28",
      status: "Pending Review",
      brands: [
        { name: "Brands 1" },
        { name: "Brands 2" },
        { name: "Brands 3" },
        { name: "Brands 4" },
      ],
    },
    {
      id: 2,
      email: "<EMAIL>",
      profile_image: "",
      requestedRole: "Administrator",
      requestDate: "2025-05-01",
      status: "Pending Approval",
      brands: [
        { name: "Brands 1" },
        { name: "Brands 2" },
        { name: "Brands 3" },
        { name: "Brands 4" },
      ],
    },
    {
      id: 3,
      email: "<EMAIL>",
      profile_image: "",
      requestedRole: "Viewer",
      requestDate: "2025-05-03",
      status: "Awaiting Confirmation",
      brands: [
        { name: "Brands 1" },
        { name: "Brands 2" },
        { name: "Brands 3" },
        { name: "Brands 4" },
      ],
    },
    {
      id: 4,
      email: "<EMAIL>",
      profile_image: "",
      requestedRole: "Viewer",
      requestDate: "2025-05-03",
      status: "Awaiting Confirmation",
      brands: [
        { name: "Brands 1" },
        { name: "Brands 2" },
        { name: "Brands 3" },
        { name: "Brands 4" },
      ],
    },
    {
      id: 5,
      email: "<EMAIL>",
      profile_image: "",
      requestedRole: "Viewer",
      requestDate: "2025-05-03",
      status: "Awaiting Confirmation",
      brands: [
        { name: "Brands 1" },
        { name: "Brands 2" },
        { name: "Brands 3" },
        { name: "Brands 4" },
      ],
    },
  ];

  const users = [
    { id: 1, email: "<EMAIL>", emoji: "👩🏽" },
    { id: 2, email: "<EMAIL>", emoji: "👤" },
    { id: 3, email: "<EMAIL>", emoji: "👨🏽" },
    { id: 4, email: "<EMAIL>", emoji: "👩🏾" },
    { id: 5, email: "<EMAIL>", emoji: "👤" },
  ];

  const permissions = [
    {
      id: 1,
      name: "Post",
      description: "Can view all documents in the system",
      status: "enabled",
    },
    {
      id: 2,
      name: "Post , Messages",
      description: "Can edit existing documents",
      status: "enabled",
    },
    {
      id: 3,
      name: "Post , Messages , Analytics",
      description: "Can delete existing documents",
      status: "disabled",
    },
    {
      id: 4,
      name: "Post , Messages",
      description: "Full administrative access to the system",
      status: "disabled",
    },
    {
      id: 5,
      name: "Post",
      description: "Full administrative access to the system",
      status: "disabled",
    },
  ];

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleOpenDialog = () => {
    setAddUserDialog(true);
  };

  const handleCloseDialog = () => {
    setAddUserDialog(false);
  };

  const GetRoleData = async () => {
    try {
      setIsLoading(true);
      const response = await apiInstance.get(URL?.GET_ROLES);
      setRolesData(response.data.data || []);
      return response.data.data || [];
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const GetPendingInvitedUsers = async () => {
    try {
      setIsLoading(true);
      const response = await apiInstance.get(URL?.GET_PENDING_INVITED_USERS);
      const data = response.data.data || [];
      setPendingInvited(data);
      return data;
    } catch (error) {
      console.error("Error fetching pending invited users:", error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const GetPendingInviteeUsers = async () => {
    try {
      setIsLoading(true);
      const response = await apiInstance.get(URL?.GET_PENDING_INVITEE_USERS);
      const data = response.data.data || [];
      setPendingInvitee(data);
      return data;
    } catch (error) {
      console.error("Error fetching pending invitee users:", error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const formdata = new FormData();
  const AcceptRequest = async (id) => {
    const formData = new FormData();
    formData.append("invite_id", id);

    try {
      setIsLoading(true);
      await apiInstance.post(URL?.ACCEPT_REQUEST, formData);

      // Remove user from pending lists
      setPendingInvited((prev) => prev.filter((user) => user.id !== id));
      setPendingInvitee((prev) => prev.filter((user) => user.id !== id));

      // Optional: refresh invitee list if it affects the final list
      await GetInviteeUsers();
    } catch (error) {
      console.error("Error accepting request:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const declineRequest = async (id) => {
    const formData = new FormData();
    formData.append("decline_id", id);

    try {
      setIsLoading(true);
      await apiInstance.post(URL?.DECLINE_REQUEST, formData);

      // Remove user from pending lists
      setPendingInvited((prev) => prev.filter((user) => user.id !== id));
      setPendingInvitee((prev) => prev.filter((user) => user.id !== id));
    } catch (error) {
      console.error("Error declining request:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const revokeRequest = async (id) => {
    const formData = new FormData();
    formData.append("invite_id", id);

    try {
      setIsLoading(true);
      await apiInstance.post(URL?.REVOKE_REQUEST, formData);

      // Update correct state
      setInvited((prev) => prev.filter((user) => user.id !== id));
      setInvitee((prev) => prev.filter((user) => user.id !== id));
      setPendingInvited((prev) => prev.filter((user) => user.id !== id));
      setPendingInvitee((prev) => prev.filter((user) => user.id !== id));
    } catch (error) {
      console.error("Error revoking request:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const GetInviteeUsers = async () => {
    try {
      setIsLoading(true);
      const response = await apiInstance.get(URL?.GET_INVITEE_USERS);
      const data = response.data.data || [];
      setInvitee(data);
      return data;
    } catch (error) {
      console.error("Error fetching invitee users:", error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const GetInvitedUsers = async () => {
    try {
      setIsLoading(true);
      const response = await apiInstance.get(URL?.GET_INVITED_USERS);
      const data = response.data.data || [];
      setInvited(data);
      return data;
    } catch (error) {
      console.error("Error fetching invited users:", error);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    GetPendingInvitedUsers();
    GetPendingInviteeUsers();
    GetInviteeUsers();
    GetInvitedUsers();
    GetRoleData();
  }, []);

  // Filter data based on search query
  const filterDataBySearchQuery = (data) => {
    if (!searchQuery.trim()) return data;

    return data.filter((item) => {
      // Search through email
      if (item.email?.toLowerCase().includes(searchQuery.toLowerCase())) {
        return true;
      }

      // Search through brands if they exist
      if (item.brands && item.brands.length > 0) {
        const brandNames = item.brands.map((brand) => brand.name.toLowerCase());
        if (
          brandNames.some((name) => name.includes(searchQuery.toLowerCase()))
        ) {
          return true;
        }
      }

      // Search through role name if it exists
      if (
        item.requestedRole?.toLowerCase().includes(searchQuery.toLowerCase())
      ) {
        return true;
      }

      // Search through status if it exists
      if (item.status?.toLowerCase().includes(searchQuery.toLowerCase())) {
        return true;
      }

      // Search through name if it exists
      if (item.name?.toLowerCase().includes(searchQuery.toLowerCase())) {
        return true;
      }

      return false;
    });
  };

  // Apply filters to get the current data based on tab and search query
  const currentData =
    userSubTab === "Invited"
      ? filterDataBySearchQuery(pendingInvited ?? [])
      : filterDataBySearchQuery(pendingInvitee ?? []);

  // Improved tab switching handler - keeps UI visible while loading data
  const handleTabSwitch = React.useCallback(
    (tab) => {
      if (tab !== userSubTab) {
        setUserSubTab(tab);
        setIsLoading(true);

        // Use setTimeout to ensure the UI updates before API call
        setTimeout(() => {
          if (tab === "Invited") {
            GetPendingInvitedUsers();
          } else if (tab === "Invitee") {
            GetPendingInviteeUsers();
          }
        }, 10);
      }
    },
    [userSubTab]
  );

  useEffect(() => {
    if (userSubTab === "Invited") {
      GetPendingInvitedUsers();
    } else {
      GetPendingInviteeUsers();
    }
  }, []);

  const fetchUsers = async () => {
    setIsLoading(true);
    await Promise.all([GetInviteeUsers(), GetInvitedUsers()]);
    setIsLoading(false);
  };

  useEffect(() => {
    if (activeTab === "Users") {
      fetchUsers();
    } else {
      setIsLoading(false);
    }
  }, [activeTab]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        dropdownOpen !== null &&
        !event.target.closest(".dropdown-menu") &&
        !event.target.closest(".dropdown-toggle")
      ) {
        setDropdownOpen(null);
      }
    };

    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, [dropdownOpen]);

  // Filter users for the Users tab
  const filteredInvitedUsers = filterDataBySearchQuery(invited);
  const filteredInviteeUsers = filterDataBySearchQuery(invitee);
  const displayedUsers =
    userSubTab === "Invitee" ? filteredInviteeUsers : filteredInvitedUsers;

  // Filter roles for the Roles and Permissions tab
  const filteredRoles = searchQuery.trim()
    ? rolePermissions.filter(
        (role) =>
          role.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          role.permissions.some((perm) =>
            perm.toLowerCase().includes(searchQuery.toLowerCase())
          )
      )
    : rolePermissions;

  // Filter modal users and permissions
  const filteredModalUsers = searchQuery.trim()
    ? users.filter((user) =>
        user.email.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : users;

  const filteredPermissions = searchQuery.trim()
    ? permissions.filter(
        (perm) =>
          perm.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          perm.description.toLowerCase().includes(searchQuery.toLowerCase())
      )
    : permissions;

  const toggleDropdown = (userId) => {
    setDropdownOpen(dropdownOpen === userId ? null : userId);
  };

  const handleEdit = (userId) => {
    console.log("Edit user", userId);
  };

  const handleDelete = (userId) => {
    console.log("Delete user", userId);
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    // Reset loading when changing tabs
    if (tab === "Users") {
      GetPendingInvitedUsers();
      GetPendingInviteeUsers();
      setIsLoading(true);
    }
    if (tab === "Pending") {
      GetPendingInvitedUsers();
      GetPendingInviteeUsers();
    }
    if (tab === "Roles and Permissions") {
      GetRoleData();
    }
  };

  // Reusable user card component

  // Content loader component to show while data is loading
  const ContentLoader = () => <UserManagementSkeleton />;

  // No Results component
  const NoResults = () => (
    <div className="flex justify-center items-center py-16">
      <div className="flex flex-col items-center">
        <p className="mt-2 text-[#A9ABAD]">
          No results found for "{searchQuery}"
        </p>
      </div>
    </div>
  );

  // Render different content based on the active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case "Users":
        return (
          <>
            {/* Tab switching buttons - always visible */}
            <div className="flex justify-center items-center mb-6">
              <div className="flex bg-[#FFFFFF] rounded-[8px] border-[1px] border-[#E0E0E0]">
                {["Invited", "Invitee"].map((tab) => (
                  <button
                    key={tab}
                    className={`px-10 py-2 rounded-[8px] text-base font-medium transition-all duration-200 ${
                      userSubTab === tab
                        ? "bg-[#563D39] text-white shadow"
                        : "text-[#A9ABAD] bg-transparent"
                    }`}
                    onClick={() => handleTabSwitch(tab)}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>

            {/* Content area with loading state */}
            <div className="w-full overflow-y-auto h-full sm:h-full">
              {/* Dynamic content with conditional column headers */}
              <div className="min-h-[200px] relative">
                {isLoading ? (
                  <ContentLoader />
                ) : displayedUsers.length === 0 ||
                  !displayedUsers.some((user) => user.is_accepted) ? (
                  <div className="text-center py-6 text-[#A9ABAD] text-base">
                    {searchQuery
                      ? "No results match your search."
                      : "No users available at the moment."}
                  </div>
                ) : (
                  <>
                    <div className="rounded-lg hidden md:flex justify-start items-center text-base text-[#000000] font-normal py-5">
                      <span className="w-1/3 pl-5">User</span>
                      <span className="w-1/3 pl-3">Brands</span>
                    </div>
                    {/* Column headers - only visible when there is data */}
                    <div className="space-y-4 mt-2">
                      {displayedUsers
                        .filter((user) => user.is_accepted === true)
                        .map((user) => (
                          <div
                            key={user.id}
                            className="bg-[#FFFFFF] border-[1px] border-[#E0E0E0] p-4 rounded-lg flex flex-col md:flex-row md:items-center md:justify-between gap-4"
                          >
                            <div className="flex items-center gap-4 w-full md:w-1/3">
                              <div className="w-12 h-12 rounded-full bg-[#F9F9F9] overflow-hidden">
                                <img
                                  src={
                                    user?.profile_image
                                      ? `${URL.SOCKET_URL}/${user.profile_image}`
                                      : siteConstant?.SOCIAL_ICONS
                                          ?.DUMMY_PROFILE
                                  }
                                  alt="Avatar"
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <span className="text-[#000000] font-normal text-base">
                                {user.email}
                              </span>
                            </div>

                            <div className="flex flex-wrap gap-2 w-full md:w-1/3 text-[#000000] text-base font-normal">
                              {user.brands && user.brands.length > 0 ? (
                                <span>
                                  {user.brands.map((b) => b.name).join(", ")}
                                </span>
                              ) : (
                                <span className="text-gray-500">
                                  No brands available
                                </span>
                              )}
                            </div>

                            <div className="relative w-full md:w-1/3 flex justify-between items-center">
                              <div className="flex gap-2">
                                {userSubTab === "Invited" ? (
                                  <button
                                    className="bg-transparent text-[#563D39] border border-[#563D39] rounded-md px-4 py-1 text-[14px]"
                                    onClick={() => {
                                      console.log("userId ===>", user.id);

                                      revokeRequest(user.id);
                                    }}
                                  >
                                    Revoke
                                  </button>
                                ) : null}
                              </div>
                              <img
                                src={crown}
                                alt="crown"
                                className="w-6 h-6 ml-2"
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        );

      case "Roles and Permissions":
        return (
          <>
            {/* Section container */}
            <div className="w-full h-full sm:h-full">
              <div className="min-h-[200px] relative ">
                {isLoading ? (
                  <ContentLoader />
                ) : rolesData.length === 0 ? (
                  <div className="text-center py-6 text-[#A9ABAD] text-base">
                    {searchQuery
                      ? "No results match your search."
                      : "No roles available at the moment."}
                  </div>
                ) : (
                  <>
                    {/* Column headers - only visible on md and up */}
                    <div className="rounded-lg hidden md:flex justify-start items-center text-base text-[#000000] font-normal py-5">
                      <span className="w-1/3 pl-5">Role</span>
                      <span className="w-1/3 pl-3">Enables Permissions</span>
                      <span className="w-1/3 pl-3">Users</span>
                    </div>

                    {/* Role rows */}
                    <div className="space-y-4 mt-2">
                      {rolesData.map((role) => {
                        const permissionsCount = role.role_description
                          ? Object.keys(role.role_description).length
                          : 0;
                        const usersCount = role.users ? role.users.length : 0;

                        return (
                          <div
                            key={role.id}
                            onClick={() => handleOpenRoleModal(role)}
                            className="bg-[#FFFFFF] border-[1px] border-[#E0E0E0] p-4 rounded-lg flex flex-col md:flex-row md:items-center md:justify-between gap-4 cursor-pointer"
                          >
                            <div className="w-full md:w-1/3 px-4 text-[#000000] text-base font-medium">
                              {role.role_name}
                            </div>

                            <div className="w-full md:w-1/3 px-4 text-[#000000] text-base font-normal">
                              {permissionsCount} Permission
                              {permissionsCount !== 1 ? "s" : ""}
                            </div>

                            <div className="w-full md:w-1/3 px-4 flex justify-between items-center text-[#000000] text-base font-normal">
                              <span>
                                {usersCount} user{usersCount !== 1 ? "s" : ""}
                              </span>
                              <img
                                src={crown} // Replace with your actual path
                                alt="crown"
                                className="w-6 h-6 ml-2"
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        );

      case "Pending":
        return (
          <>
            {/* Tab switching buttons - always visible */}
            <div className="flex justify-center items-center mb-6">
              <div className="flex bg-[#FFFFFF] rounded-[8px] border-[1px] border-[#E0E0E0]">
                {["Invited", "Invitee"].map((tab) => (
                  <button
                    key={tab}
                    className={`px-10 py-2 rounded-[8px] text-base font-medium transition-all duration-200 ${
                      userSubTab === tab
                        ? "bg-[#563D39] text-white shadow"
                        : "text-[#A9ABAD] bg-transparent"
                    }`}
                    onClick={() => handleTabSwitch(tab)}
                  >
                    {tab}
                  </button>
                ))}
              </div>
            </div>

            {/* Content area with loading state */}
            <div className="w-full overflow-y-auto h-full sm:h-full">
              {/* Content with loading state and conditional headers */}
              <div className="min-h-[200px] relative">
                {isLoading ? (
                  <ContentLoader />
                ) : currentData.length === 0 ? (
                  <div className="text-center py-6 text-[#A9ABAD] text-base">
                    {searchQuery
                      ? "No results match your search."
                      : "No pending requests found."}
                  </div>
                ) : (
                  <>
                    {/* Column headers - only visible when there is data */}
                    <div className="space-y-4 mt-2">
                      {currentData.some(
                        (user) => user.is_accepted === false
                      ) && (
                        <div className="rounded-lg hidden md:flex justify-start items-center text-base text-[#000000] font-normal py-5">
                          <span className="w-1/3 pl-5">User</span>
                          <span className="w-1/3 pl-3">Brands</span>
                        </div>
                      )}
                      {currentData.map((user, index) =>
                        user.is_accepted === false ? (
                          <div key={index} className="your-user-row-styles">
                            {/* Render user-specific data here */}
                          </div>
                        ) : null
                      )}
                      {currentData.every(
                        (user) => user.is_accepted !== false
                      ) && (
                        <div className="rounded-lg hidden md:flex justify-center items-center text-base text-[#000000] font-normal py-5">
                          <span className="text-[#A9ABAD] text-base">
                            No pending Invited Users Found
                          </span>
                        </div>
                      )}
                      {currentData
                        .filter((user) => {
                          if (userSubTab === "Invited") {
                            return user?.is_accepted !== true; // Show only non-accepted
                          } else if (userSubTab === "Invitee") {
                            return user?.is_accepted === false; // Show only accepted
                          } else {
                            return true; // Show all for other tabs
                          }
                        })
                        .map((user) => (
                          <div
                            key={user.id}
                            className="bg-[#FFFFFF] border-[1px] border-[#E0E0E0] p-4 rounded-lg flex flex-col md:flex-row md:items-center md:justify-between gap-4"
                          >
                            <div className="flex items-center gap-4 w-full md:w-1/3">
                              <div className="w-12 h-12 rounded-full bg-[#F9F9F9] overflow-hidden">
                                <img
                                  src={
                                    user?.profile_image
                                      ? `${URL.SOCKET_URL}/${user.profile_image}`
                                      : siteConstant?.SOCIAL_ICONS
                                          ?.DUMMY_PROFILE
                                  }
                                  alt="Avatar"
                                  className="w-full h-full object-cover"
                                />
                              </div>
                              <span className="text-[#000000] font-normal text-base">
                                {user.email}
                              </span>
                            </div>

                            <div className="flex flex-wrap gap-2 w-full md:w-1/3 text-[#000000] text-base font-normal">
                              {user.brands && user.brands.length > 0 ? (
                                <span>
                                  {user.brands.map((b) => b.name).join(", ")}
                                </span>
                              ) : (
                                <span className="text-gray-500">
                                  No brands available
                                </span>
                              )}
                            </div>

                            <div className="relative w-full md:w-1/3 flex justify-between items-center">
                              <div className="flex gap-2">
                                {userSubTab === "Invited" ? (
                                  user.is_accepted === true ? (
                                    <button
                                      className="bg-transparent text-[#563D39] border border-[#563D39] rounded-md px-4 py-1 text-[14px]"
                                      onClick={() => {
                                        console.log(user.id);
                                      }}
                                    >
                                      Revoke
                                    </button>
                                  ) : (
                                    <button
                                      className="bg-transparent text-[#563D39] border border-[#563D39] rounded-md px-4 py-1 text-[14px]"
                                      disabled
                                    >
                                      Pending
                                    </button>
                                  )
                                ) : (
                                  <>
                                    <button
                                      className="flex text-[#FFFFFF] border border-[#563D39] bg-[#563D39] rounded-[60px] px-6 py-1 text-[14px]"
                                      onClick={() => {
                                        AcceptRequest(user.id);
                                      }}
                                    >
                                      <img
                                        src={tick}
                                        alt="tick"
                                        className="w-4 h-4 mr-2 mt-[2px]"
                                      />
                                      Accept
                                    </button>
                                    <button
                                      className="flex bg-transparent text-[#563D39] border border-[#563D39] rounded-[60px] px-6 py-1 text-[14px]"
                                      onClick={() => declineRequest(user.id)}
                                    >
                                      <IoCloseOutline className="w-4 h-4 mr-2 mt-[2px]" />
                                      Decline
                                    </button>
                                  </>
                                )}
                              </div>
                              <img
                                src={crown}
                                alt="crown"
                                className="w-6 h-6 ml-2"
                              />
                            </div>
                          </div>
                        ))}
                    </div>
                  </>
                )}
              </div>
            </div>
          </>
        );

      default:
        return <p>Select a tab to view content</p>;
    }
  };
  // Improved RoleDetailDialog Component
  const RoleDetailDialog = () => {
    return (
      <Dialog
        open={isModalOpen}
        onClose={closeModal}
        fullWidth
        maxWidth="md"
        PaperProps={{
          style: { boxShadow: "none" }, // Removes the shadow
        }}
      >
        <div className="flex flex-col items-center bg-white rounded-[12px] py-8 px-4 md:px-6 relative font-Ubuntu w-full max-w-full h-[450px] mx-auto overflow-y-auto">
          {/* Modal Header with Close Button */}
          <button
            onClick={closeModal}
            className="absolute right-6 top-6 text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#A9ABAD"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          {/* Tab Navigation */}
          <div className="flex rounded-lg border border-[#E0E0E0] w-full max-w-md mb-6">
            {["Users", "Permissions"].map((modalTab) => (
              <button
                key={modalTab}
                className={`flex-1 py-2 rounded-lg text-base font-medium transition-all duration-200 ${
                  isModalTab === modalTab
                    ? "bg-[#563D39] text-white shadow"
                    : "text-[#A9ABAD] bg-transparent"
                }`}
                onClick={() => setIsModalTab(modalTab)}
              >
                {modalTab}
              </button>
            ))}
          </div>

          {/* Modal Content */}
          <div className="bg-white w-full px-2">
            {/* Users Tab */}
            {isModalTab === "Users" && (
              <div className="space-y-6">
                {!selectedRole?.users || selectedRole.users.length === 0 ? (
                  <p className="text-center py-4 text-[#A9ABAD] mt-28">
                    No users assigned to this role
                  </p>
                ) : (
                  <div className="space-y-3">
                    {selectedRole.users.map((user) => (
                      <div
                        key={user.id}
                        className="flex items-center p-3 border border-[#E0E0E0] rounded-lg hover:bg-gray-100"
                      >
                        <div className="w-8 h-8 rounded-full flex items-center justify-center bg-gray-200 overflow-hidden">
                          {user?.profile_picture ? (
                            <img
                              src={user?.profile_picture}
                              alt={user.username}
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <span className="text-xl"></span>
                          )}
                        </div>
                        <span className="ml-3 text-[#000000] font-normal text-base">
                          {user.username}
                        </span>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Permissions Tab */}
            {isModalTab === "Permissions" && (
              <div className="space-y-3">
                {!selectedRole?.role_description ||
                Object.keys(selectedRole.role_description).length === 0 ? (
                  <p className="text-center py-4 text-[#A9ABAD]">
                    No permissions defined
                  </p>
                ) : (
                  Object.entries(selectedRole.role_description).map(
                    ([key, description]) => (
                      <div
                        key={key}
                        className="flex flex-col p-3 border rounded-lg border-[#E0E0E0] hover:bg-gray-100"
                      >
                        <p className="text-[#000000] font-normal text-base">
                          {description}
                        </p>
                      </div>
                    )
                  )
                )}
              </div>
            )}
          </div>
        </div>
      </Dialog>
    );
  };

  return (
    isLoading ? (
      <UserManagementSkeleton />
    ) : (
      <div className="mx-auto p-8 bg-[#FFFFFF] rounded-lg shadow-sm font-Ubuntu mt-4 min-h-screen ">
        <h1 className="text-2xl font-bold text-[#000000] mb-6">
          {localesData?.USER_WEB?.USER_MANAGE?.USER_MANAGEMENT ||
            "User Management"}
        </h1>

        <div className="mt-6 mb-6 p-[2px] rounded-[12px] bg-gradient-to-r from-[#563D39] to-[#BC857D]">
          <div className="bg-[#FFFFFF] rounded-lg p-3 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div className="flex gap-4">
              <img src={crown} alt="crown" />
              <p className="text-[#000000]">
                Offer unlimited permissions to your team so they can assist in
                content management.
              </p>
            </div>
            <div>
              <button className="bg-gradient-to-b from-[#563D39] to-[#BC857D] text-white px-4 py-2 rounded-[10px]">
                Upgrade your plan
              </button>
            </div>
          </div>
        </div>

        <div className="border-b border-gray-200 mb-6 mt-4">
          <div className="flex">
            {["Users", "Roles and Permissions", "Pending"].map((tab) => (
              <button
                key={tab}
                className={`py-3 px-6 font-normal text-[18px] flex flex-row-reverse items-center gap-2 ${
                  activeTab === tab
                    ? "text-[#000000] border-b-2 border-gray-800"
                    : "text-[#A9ABAD]"
                }`}
                onClick={() => handleTabChange(tab)}
              >
                {/* Conditionally show crown image */}
                {(tab === "Users" || tab === "Roles and Permissions") && (
                  <img
                    src={activeTab === tab ? crown : crownLight}
                    alt="Crown"
                    className="w-5 h-5"
                  />
                )}
                {tab}
              </button>
            ))}
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4 mb-8">
          <div className="relative flex-grow">
            {/* Search icon (left) */}
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <AiOutlineSearch className="h-5 w-5 text-gray-400" />
            </div>

            {/* Input */}
            <input
              type="text"
              placeholder="Search"
              className="w-full pl-10 pr-10 py-2 bg-[#FFFFFF] border border-[#E0E0E0] rounded-[10px] focus:ring-0 focus:outline-none focus:border-[#563D39]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />

            {/* Clear icon (right) */}
            {searchQuery && (
              <button
                type="button"
                onClick={() => setSearchQuery("")}
                className="absolute inset-y-0 right-3 flex items-center text-gray-400 hover:text-gray-600"
              >
                <IoCloseOutline className="h-5 w-5" />
              </button>
            )}
          </div>

          <button
            className="flex items-center justify-between gap-20 px-8 py-2 bg-[#AA8882] rounded-[10px]"
            onClick={handleOpenDialog}
          >
            <div className="flex gap-2">
              <AiOutlinePlus className="h-5 w-5 text-[#FFFFFF] mt-[3px]" />
              <span className="text-[#FFFFFF] font-medium text-[18px]">
                {localesData?.USER_WEB?.USER_MANAGE?.ADD_USER || "Add User"}
              </span>
            </div>
            <span>
              <img src={crown} alt="crown" />
            </span>
          </button>
        </div>

        {renderTabContent()}

        {/* Render the role details dialog */}
        <RoleDetailDialog />

        {addUserDialog && (
          <Adduser
            open={addUserDialog}
            onClose={handleCloseDialog}
            fetchUsers={fetchUsers}
          />
        )}
      </div>
    )
  );
}

export default UserManagePage;
