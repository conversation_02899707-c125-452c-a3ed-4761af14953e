import { Dialog } from "@mui/material";
import React, { useContext, useState, useEffect, useCallback } from "react";
import debounce from "lodash.debounce";
import { fetchFromStorage } from "../../../helpers/context/storage";
import siteConstant from "../../../helpers/constant/siteConstant";
import { socket } from "../../../helpers/context/socket";
import { Navigate, useNavigate } from "react-router-dom";
import BrandList from "../brands/brandlist";
import { IntlContext } from "../../../App";
import { URL } from "../../../helpers/constant/Url";

function AddUser({ open, onClose, fetchUsers }) {
  const [search, setSearch] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [selectedUser, setSelectedUser] = useState(null);
  const [Brandlist, setBrandlist] = useState(false);
  const [userId, setUserId] = useState(null);
  console.log("userId", userId);
  const [brandId, setBrandId] = useState([]);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const token = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA)?.token;
  const Navigate = useNavigate();

  console.log("suggestions", suggestions);

  const debouncedHandleChange = useCallback(
    debounce((inputValue) => {
      if (inputValue.trim() !== "") {
        socket?.emit("search_user", {
          Authorization: token,
          search_text: inputValue,
        });
      } else {
        setSuggestions([]);
      }
    }, 300),
    [socket, token]
  );

  useEffect(() => {
    const handleReceiveSuggestions = (response) => {
      setSuggestions(response?.status && response?.data ? response.data : []);
    };

    socket?.on("search_user", handleReceiveSuggestions);
    return () => {
      socket?.off("search_user", handleReceiveSuggestions);
    };
  }, [socket]);

  useEffect(() => {
    debouncedHandleChange(search);
  }, [search, debouncedHandleChange]);

  const handleSelectUser = (user) => {
    setSearch(user.name);
    setUserId(user.id);
    setSelectedUser(user);
    setSuggestions([]);
  };

  const handleNext = () => {
    if (selectedUser) {
      handleOpenDialog();
    }
  };

  const handleOpenDialog = () => {
    setBrandlist(true);
  };

  // Modified to ensure both dialogs close in sequence
  const handleCloseDialog = () => {
    setBrandlist(false);
    // Reset state when all dialogs close
    setSelectedUser(null);
    setUserId(null);
    setSearch("");
    setBrandId([]);
    onClose(); // Close the AddUser dialog as well
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        fullWidth
        maxWidth="md"
        PaperProps={{
          style: { boxShadow: "none" }, // Removes the shadow
        }}
      >
        <div className="flex flex-col items-center bg-white rounded-[12px] py-8 px-4 md:px-36 relative font-Ubuntu w-full max-w-full min-h-[450px] mx-auto">
          <h2 className="text-xl text-black font-semibold text-center mb-2">
            {localesData?.USER_WEB?.USER_MANAGE?.ADD_USER}
          </h2>
          <p className="text-[#A9ABAD] font-bold text-[14px] sm:text-[16px] text-center mb-6">
            Add users in a click grow your team instantly
          </p>

          <button
            onClick={onClose}
            className="absolute right-6 top-6 text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#A9ABAD"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>

          <div className="w-full mb-4">
            <fieldset className="w-full border border-gray-300 rounded-[12px] px-3 py-1 transition-all focus-within:rounded-none focus-within:rounded-tl-[12px] focus-within:rounded-tr-[12px] focus-within:border-gray-500">
              <legend className="text-sm font-medium text-gray-700 px-1">
                Add User
              </legend>
              <input
                type="text"
                value={search}
                onChange={(e) => setSearch(e.target.value)}
                placeholder=" "
                className="w-full bg-transparent border-none p-1 text-sm mb-[6px] text-gray-900 placeholder-transparent focus:outline-none focus:ring-0"
              />
            </fieldset>
          </div>

          {selectedUser && (
            <div className="w-full p-3 flex items-center gap-3 mb-4 justify-between">
              <div className="flex items-center gap-3">
                <img
                  src={
                    selectedUser.profile
                      ? `${URL.SOCKET_URL}${selectedUser.profile}`
                      : siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                  }
                  alt={selectedUser.name}
                  className="h-10 w-10 rounded-full"
                />
                <div>
                  <p className="text-sm font-medium text-gray-800">
                    {selectedUser.name}
                  </p>
                </div>
              </div>
              <button
                onClick={() => {
                  setSelectedUser(null);
                  setUserId(null);
                  setSearch("");
                }}
                className="text-[#B42318] hover:text-red-700 text-sm font-medium"
              >
                Remove
              </button>
            </div>
          )}

          {!selectedUser && (
            <>
              {suggestions.length > 0 ? (
                <ul className="w-full -mt-[17px] border border-gray-300 rounded-bl-[12px] rounded-br-[12px] max-h-[200px] overflow-y-auto">
                  {suggestions.map((user) => (
                    <li
                      key={user.id || user.name}
                      onClick={() => handleSelectUser(user)}
                      className="flex items-center gap-3 px-4 py-2 hover:bg-gray-100 cursor-pointer transition-colors"
                    >
                      <img
                        src={
                          user.profile
                            ? `${URL.SOCKET_URL}${user.profile}`
                            : siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                        }
                        alt={user.name || "User Profile"}
                        className={`h-10 w-10 ${user.profile ? "rounded-full p-1" : "rounded-full border-[2px] p-1 border-[#674941]"}`}
                      />
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {user.name || "Unknown User"}
                        </p>
                        <p className="text-xs text-gray-500">
                          @{user.username || user.email?.split("@")[0]}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              ) : (
                <p className="text-Red text-md font-normal mt-2 text-center">
                  {search
                    ? "No users found. Try another name!"
                    : "Start typing to search for users..."}
                </p>
              )}
            </>
          )}

          <div className="absolute flex justify-center items-center bottom-6 px-8 w-full">
            <button
              type="submit"
              className="w-[60%] sm:w-[40%] bg-[#563D39] text-white py-3 rounded-[12px] font-semibold hover:bg-opacity-90 transition-all"
              onClick={handleNext}
            >
              {localesData?.USER_WEB?.BRANDS?.ADD_BRAND || "Add Brand"}
            </button>
          </div>
        </div>
      </Dialog>

      {Brandlist && (
        <BrandList
          open={Brandlist}
          onClose={handleCloseDialog}
          setBrandId={setBrandId}
          brandId={brandId}
          userId={userId}
          fetchUsers={fetchUsers}
        />
      )}
    </>
  );
}

export default AddUser;
