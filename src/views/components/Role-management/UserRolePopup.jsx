import React, { useState, useContext } from "react";
import { Dialog } from "@mui/material";
import { URL } from "../../../helpers/constant/Url";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { setApiMessage } from "../../../helpers/context/toaster";
import { IntlContext } from "../../../App";
import tickCheckbox from "../../../assets/images/user-manage/tickCheckbox.svg";

const UserRolePopup = ({ isOpen, onClose }) => {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isPost, setIsPost] = useState(false);
  const [isAnalytics, setIsAnalytics] = useState(false);
  const [isUserManagement, setIsUserManagement] = useState(false);
  const [isBrandManagement, setIsBrandManagement] = useState(false);
  const [isBlockAndUnblock, setIsBlockAndUnblock] = useState(false);
  const [isFeedback, setIsFeedback] = useState(false);
  const [isMessage, setIsMessage] = useState(false);
  const [roleName, setRoleName] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [roleError, setRoleError] = useState("");
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const permissions = [
    { id: "post", label: "Post", state: isPost, setState: setIsPost },
    {
      id: "messages",
      label: "Messages",
      state: isMessage,
      setState: setIsMessage,
    },
    {
      id: "analytics",
      label: "Analytics",
      state: isAnalytics,
      setState: setIsAnalytics,
    },
    {
      id: "User Management",
      label: "User Management",
      state: isUserManagement,
      setState: setIsUserManagement,
    },
    {
      id: "Brand Management",
      label: "Brand Management",
      state: isBrandManagement,
      setState: setIsBrandManagement,
    },
    {
      id: "Block & Unblock",
      label: "Block & Unblock",
      state: isBlockAndUnblock,
      setState: setIsBlockAndUnblock,
    },
    {
      id: "Feedback",
      label: "Feedback",
      state: isFeedback,
      setState: setIsFeedback,
    },
  ];

  const handleCreateRole = async () => {
    if (!roleName.trim()) {
      setRoleError("Role Name is required.");
      return;
    }
    setRoleError("");

    const roleDescription = {};
    if (isPost) roleDescription["1"] = "Post";
    if (isMessage) roleDescription["2"] = "Message";
    if (isAnalytics) roleDescription["3"] = "Analytics";
    if (isUserManagement) roleDescription["4"] = "User Management";
    if (isBrandManagement) roleDescription["5"] = "Brand Management";
    if (isBlockAndUnblock) roleDescription["6"] = "Block & Unblock";
    if (isFeedback) roleDescription["7"] = "Feedback";

    const payload = {
      role_name: roleName,
      role_description: roleDescription,
    };

    setIsLoading(true);

    try {
      const response = await apiInstance.post(URL.CREATE_ROLE, payload, {
        headers: { "Content-Type": "application/json" },
      });
      if (response.status) {
        setRoleName("");
        setIsPost(false);
        setIsMessage(false);
        setIsAnalytics(false);
        setIsUserManagement(false);
        setIsBrandManagement(false);
        setIsBlockAndUnblock(false);
        setIsFeedback(false);
        setIsLoading(false);
        setApiMessage("success", response.data.message);
        onClose();
      }
    } catch (error) {
      setIsLoading(false);
      console.error("Error Creating Role:", error);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      BackdropProps={{ style: { backgroundColor: "transparent" } }}
      PaperProps={{
        style: { boxShadow: "none" },
      }}
    >
      <div className="flex flex-col items-center bg-white rounded-[12px] py-8 px-4 md:px-36 relative font-Ubuntu w-full h-[450px] mx-auto">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-[22px] font-normal text-[#563D39] text-center w-full">
            {localesData?.USER_WEB?.ROLE_MANAGEMENT?.USER_ROLE || "User Role"}
          </h1>
          <button
            onClick={onClose}
            className="absolute right-6 top-6 text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#A9ABAD"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className="mb-6">
          <fieldset className="relative w-[450px] border border-gray-300 rounded-xl px-3  transition-all focus-within:border-gray-500">
            <legend className="text-sm font-medium text-gray-700 px-1">
              Role Name
            </legend>
            <input
              type="text"
              value={roleName}
              onChange={(e) => {
                setRoleName(e.target.value);
                if (roleError && e.target.value.trim()) setRoleError("");
              }}
              placeholder=" "
              className="w-full text-sm text-gray-900 bg-transparent border-none focus:outline-none focus:ring-0 mb-[6px]"
            />
          </fieldset>

          {roleError && (
            <p className="text-red-500 text-sm mt-1">{roleError}</p>
          )}
        </div>

        <div className="mb-6">
          <div className="text-sm font-medium text-gray-700 mb-3">
            Permissions
          </div>

          <div className="flex justify-center items-center flex-wrap gap-3">
            {permissions.map((permission) => (
              <label
                key={permission.id}
                htmlFor={permission.id}
                className={`flex items-center justify-start gap-2 pl-4 pr-24 py-2 rounded-xl border text-sm font-medium cursor-pointer transition-colors
        ${
          permission.state
            ? "bg-[#4c3434] text-white border-[#4c3434]"
            : "bg-white text-[#4c3434] border-[#d1cfcf]"
        }
      `}
              >
                <div
                  className={`relative h-4 w-4 rounded border-2 border-[#4c3434] ${
                    permission.state ? "bg-[#4c3434]" : "bg-transparent"
                  } cursor-pointer`}
                  onClick={() => permission.setState(!permission.state)}
                >
                  <input
                    type="checkbox"
                    id={permission.id}
                    checked={permission.state}
                    onChange={() => permission.setState(!permission.state)}
                    className="opacity-0 absolute inset-0 cursor-pointer"
                  />
                  <span
                    className={`absolute top-0 bg-white rounded-[2px] left-0 w-full h-full flex justify-center items-center transition-opacity duration-200 ${
                      permission.state ? "opacity-100" : "opacity-0"
                    }`}
                  >
                    <img src={tickCheckbox} alt="tick" className="h-2 w-2" />
                  </span>
                </div>

                {permission.label}
              </label>
            ))}
          </div>
        </div>

        <div className="absolute flex justify-center items-center bottom-6 px-8 w-full">
          <button
            onClick={handleCreateRole}
            disabled={isLoading}
            className="w-[60%] sm:w-[335px] bg-[#563D39] text-white py-3 rounded-[12px] font-semibold hover:bg-opacity-90 transition-all disabled:bg-gray-400 disabled:cursor-not-allowed"
          >
            {isLoading ? "Creating..." : "Create"}
          </button>
        </div>
      </div>
    </Dialog>
  );
};

export default UserRolePopup;
