import React, { useContext, useEffect, useState, useCallback } from "react";
import { Dialog } from "@mui/material";
import debounce from "lodash.debounce";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import { URL } from "../../../helpers/constant/Url";
import Loader from "../../../helpers/UI/Loader";
import { setApiMessage } from "../../../helpers/context/toaster";
import UserRolePopup from "../Role-management/UserRolePopup.jsx";
import { IntlContext } from "../../../App";
import GetPendingInvitedUsers from "../user_management/index.jsx";
import GetPendingInviteeUsers from "../user_management/index.jsx";

const RoleManagement = ({ open, onClose, brandId, userId, fetchUsers }) => {
  const [search, setSearch] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRole, setSelectedRole] = useState(null);
  const [isRolePopupOpen, setIsRolePopupOpen] = useState(false);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  const subscriptionId = localStorage.getItem("subscriptionId");

  const getRoles = async () => {
    setLoading(true);
    try {
      const response = await apiInstance.get(URL.USER_ROLES);
      return response?.data?.data || [];
    } catch (error) {
      console.error("Error fetching roles:", error);
      return [];
    } finally {
      setLoading(false);
    }
  };

  const debouncedHandleSearch = useCallback(
    debounce(async (searchText) => {
      if (searchText.trim() !== "") {
        setLoading(true);
        try {
          const allRoles = await getRoles();
          const filteredRoles = allRoles.filter((role) =>
            role.role_name.toLowerCase().includes(searchText.toLowerCase())
          );
          setSuggestions(filteredRoles);
        } catch (error) {
          console.error("Error searching roles:", error);
          setSuggestions([]);
        } finally {
          setLoading(false);
        }
      } else {
        setSuggestions([]);
      }
    }, 300),
    []
  );

  useEffect(() => {
    debouncedHandleSearch(search);
  }, [search, debouncedHandleSearch]);

  const handleSelectRole = (role) => {
    setSelectedRole(role);
    setSearch("");
    setSuggestions([]);
  };

  const handleRemoveRole = () => {
    setSelectedRole(null);
  };

  const handleAddRoleClick = () => {
    setIsRolePopupOpen(true);
  };

  const handleSubmit = async () => {
    if (!selectedRole) {
      setApiMessage("error", "Please select a role");
      return;
    }

    const payload = {
      user_id: Number(userId),
      brands_list: Array.isArray(brandId) ? brandId : [brandId],
      role_id: Number(selectedRole.id),
    };

    try {
      setLoading(true);
      const response = await apiInstance.post(URL.USER_MANAGEMENT, payload, {
        headers: {
          subscription: subscriptionId,
          "Content-Type": "application/json",
        },
      });

      if (response?.data?.status) {
        setApiMessage("success", response?.data?.message);
        onClose();
        fetchUsers();
        GetPendingInvitedUsers();
        GetPendingInviteeUsers();
        getRoles();
      } else {
        setApiMessage(
          "error",
          response?.data?.message || "Failed to assign role"
        );
      }
    } catch (error) {
      console.error(
        "Error submitting data:",
        error?.response?.data || error.message
      );
      setApiMessage(
        "error",
        error?.response?.data?.message || "Failed to assign role"
      );
    } finally {
      setLoading(false);
    }
  };

  const refreshRoles = async () => {
    setIsRolePopupOpen(false);
    setSuggestions(await getRoles());
  };

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        fullWidth
        maxWidth="md"
        BackdropProps={{ style: { backgroundColor: "transparent" } }}
        PaperProps={{
          style: { boxShadow: "none" },
        }}
      >
        <div className="flex flex-col items-center bg-white rounded-[12px] py-8 px-4 md:px-36 relative font-Ubuntu w-full max-w-full h-[450px] mx-auto">
          <h2 className="text-xl text-black font-semibold text-center mb-2">
            {localesData?.USER_WEB?.ROLE_MANAGEMENT?.ADD_ROLE ||
              "Role Management"}
          </h2>
          <button
            onClick={onClose}
            className="absolute right-6 top-6 text-gray-500 hover:text-gray-700"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="30"
              height="30"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#A9ABAD"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
          <p className="text-[#A9ABAD] font-bold text-[14px] sm:text-[16px] text-center mb-6">
            {localesData?.USER_WEB?.ROLE_MANAGEMENT?.SELECT_ROLE_DESC ||
              "Select a role to assign to the user for the selected brands"}
          </p>

          <div className="w-full mb-4 flex items-center gap-4">
            <div className="flex-grow relative">
              <div className="flex-grow relative ">
                <fieldset className="w-full border border-gray-300 rounded-[12px] px-3 py-1 transition-all focus-within:rounded-none focus-within:rounded-tl-[12px] focus-within:rounded-tr-[12px] focus-within:border-gray-500">
                  <legend className="text-sm font-medium text-gray-700 px-1">
                    {localesData?.USER_WEB?.ROLE_MANAGEMENT?.SEARCH_ROLES ||
                      "Search roles..."}
                  </legend>
                  <input
                    type="text"
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    placeholder=" "
                    className="w-full bg-transparent border-none p-0 text-sm text-gray-900 placeholder-transparent focus:outline-none focus:ring-0 mb-[6px]"
                  />
                </fieldset>
              </div>

              {!loading && suggestions.length > 0 && !selectedRole && (
                <ul className="absolute left-0 top-full w-full z-10 border border-t-0 border-gray-300 rounded-bl-[12px] rounded-br-[12px] max-h-[200px] overflow-y-auto bg-white shadow-md">
                  {suggestions.map((role) => (
                    <li
                      key={role.id}
                      onClick={() => handleSelectRole(role)}
                      className="flex items-center gap-3  px-4 py-4 cursor-pointer hover:bg-gray-100 transition-colors"
                    >
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {role.role_name}
                        </p>
                      </div>
                      <div className="ml-auto">
                        <p className="text-sm text-[#000000] font-normal">
                          {role.role_description
                            ? Object.keys(role.role_description).length
                            : 0}{" "}
                          {localesData?.USER_WEB?.ROLE_MANAGEMENT
                            ?.PERMISSIONS || "Permissions"}
                        </p>
                      </div>
                    </li>
                  ))}
                </ul>
              )}
            </div>

            <button
              className=" p-3 rounded-[12px] border border-gray-300 text-sm font-medium flex items-center gap-2 transition-colors"
              onClick={handleAddRoleClick}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              {localesData?.USER_WEB?.ROLE_MANAGEMENT?.ADD_ROLE || "Add Role"}
            </button>
          </div>

          {selectedRole && (
            <div className="w-full p-3 flex items-center justify-between gap-3 mb-4 rounded-[12px] font-Ubuntu">
              {/* Role Name */}
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-800">
                  {selectedRole.role_name}
                </p>
              </div>

              {/* Permission Count Centered */}
              <div className="flex-1 text-center">
                <p className="text-sm text-[#000000] font-normal">
                  {selectedRole.role_description
                    ? Object.keys(selectedRole.role_description).length
                    : 0}{" "}
                  {localesData?.USER_WEB?.ROLE_MANAGEMENT?.PERMISSIONS ||
                    "Permissions"}
                </p>
              </div>

              {/* Remove Button */}
              <div className="flex-1 flex justify-end">
                <button
                  onClick={handleRemoveRole}
                  className="text-[#B42318] hover:text-red-700 text-sm font-medium"
                >
                  {localesData?.USER_WEB?.ROLE_MANAGEMENT?.REMOVE || "Remove"}
                </button>
              </div>
            </div>
          )}

          {loading && <Loader />}

          {!loading && search && suggestions.length === 0 && !selectedRole && (
            <p className="text-Red text-md font-normal mt-2 text-center">
              {localesData?.USER_WEB?.ROLE_MANAGEMENT?.NO_ROLES_FOUND ||
                "No roles found. Try another search!"}
            </p>
          )}

          {!loading && !search && !selectedRole && suggestions.length === 0 && (
            <p className="text-[#A9ABAD] text-md font-normal mt-2 text-center">
              {localesData?.USER_WEB?.ROLE_MANAGEMENT?.START_TYPING ||
                "Start typing to search for roles..."}
            </p>
          )}

          <div className="absolute flex justify-center items-center bottom-6 px-8 w-full">
            <button
              type="submit"
              className="w-[60%] sm:w-[40%] bg-[#563D39] text-white py-3 rounded-[12px] font-semibold hover:bg-opacity-90 transition-all disabled:bg-gray-400 disabled:cursor-not-allowed"
              onClick={handleSubmit}
              disabled={!selectedRole || loading}
            >
              {loading
                ? localesData?.USER_WEB?.ROLE_MANAGEMENT?.PROCESSING ||
                  "Processing..."
                : localesData?.USER_WEB?.ROLE_MANAGEMENT?.SUBMIT || "Submit"}
            </button>
          </div>
        </div>
      </Dialog>

      {isRolePopupOpen && (
        <UserRolePopup isOpen={isRolePopupOpen} onClose={refreshRoles} />
      )}
    </>
  );
};

export default RoleManagement;
