import React, { useContext, useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { IntlContext } from "../../../App";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import siteConstant from "../../../helpers/constant/siteConstant";
import { URL } from "../../../helpers/constant/Url";
import Loader from "../../../helpers/UI/Loader";

const ShareProfile = () => {
  const { username } = useParams();
  const [sharedata, setSharedata] = useState([]);
  const [Loading, setLoading] = useState(true);
  const [Error, setError] = useState(null);
  const navigate = useNavigate();
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;

  useEffect(() => {
    const fetchSharedata = async () => {
      const brandId = localStorage.getItem("BrandId");
      try {
        const { data } = await apiInstance.get(`${URL.SHARE_PROFILE}`, {
          headers: {
            brand: brandId,
          },
        });
        console.log("API Response Data:", data);
        setSharedata([data?.profile]);
      } catch (error) {
        setError("Error fetching,Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchSharedata();
  }, [username]);

  if (Loading) return <Loader />;
  if (Error) return <p>{Error}</p>;

  const platformColors = {
    instagram: "#fce7f3",
    facebook: "#e0edfc",
    youtube: "#fde7e8",
    pinterest: "#ffd6d6",
    linkedin: "#d1e7ff",
    vimeo: "#c8effe",
    tumblr: "#d5e3fd",
    reddit: "#fcd0c1",
    tiktok: "#f3f4f6",
    thread: "#d1d4db",
  };

  return (
    <div className="font-Ubuntu h-screen overflow-y-auto bg-gray-50">
      <div className="ps-4">
        <img
          src={siteConstant.SOCIAL_ICONS.FLOWKARLOGO}
          className="h-14 w-14 sm:h-16 sm:w-16 md:h-20 md:w-20 "
        />
      </div>
      <div className=" w-full border border-b border-gray-100"></div>

      {sharedata && sharedata.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-4 mb-12 sm:mb-0">
          {sharedata.map((profile) =>
            profile.social_links && profile.social_links.length > 0 ? (
              profile.social_links.map((link) => (
                <div key={link.platform} className="flex justify-center">
                  <div className="p-4">
                    <div className="p-2 rounded-xl bg-white">
                      <div className="text-center rounded-lg">
                        <div
                          className="py-4 rounded-lg"
                          style={{
                            backgroundColor:
                              platformColors[link.platform.toLowerCase()] ||
                              "#FFFFFF",
                          }}
                        >
                          <div className="flex justify-center">
                            <div className="rounded-[31px] p-1 border-2 border-Red">
                              <img
                                src={
                                  link.profile_image
                                    ? link.profile_image
                                    : siteConstant.SOCIAL_ICONS.DUMMY_PROFILE
                                }
                                alt="profile"
                                className="sm:rounded-[34px] sm:h-20 sm:w-20 rounded-[30px] h-16 w-16 object-cover"
                              />
                            </div>
                          </div>
                          <div className="m-4">
                            <h2 className="text-lg font-bold py-2">
                              {link.username ? link.username : "no Available"}
                            </h2>
                            <p className="text-gray-500">
                              {link.name ? link.name : "no Available"}
                            </p>
                          </div>
                        </div>

                        <div
                          key={link.platform}
                          className="mt-4 flex items-center gap-5 lg:gap-10 justify-between p-4 rounded-lg whitespace-nowrap"
                        >
                          <div className="flex items-center">
                            <img
                              src={
                                siteConstant.SOCIAL_ICONS[
                                  link.platform.toUpperCase() + "_ICON"
                                ]
                              }
                              alt="Social Icon"
                              className="mr-2 bg-gray-50 rounded-md p-2 h-12 w-12"
                            />
                            <span className="text-md sm:text-lg font-bold">
                              {link.platform}
                            </span>
                          </div>
                          <a
                            href={link.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-Red text-white font-semibold py-2 px-3 rounded-lg text-sm sm:text-[16px]"
                          >
                            Visit Profile
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="col-span-full">
                <div className="flex justify-start ">
                  <div className="p-4 ms-10">
                    <div className="p-2 rounded-xl bg-white">
                      <div className="text-center rounded-lg">
                        <div className="py-4 rounded-lg bg-profileCardBG">
                          <div className="flex justify-center">
                            <div className="rounded-[36px] p-1 border-2 border-Red">
                              <img
                                src={
                                  profile?.profile_picture
                                    ? profile?.profile_picture
                                    : siteConstant?.SOCIAL_ICONS?.DUMMY_PROFILE
                                }
                                alt="profile"
                                className="sm:rounded-[34px] sm:h-20 sm:w-20 rounded-[30px] h-16 w-16"
                              />
                            </div>
                          </div>
                          <div className="m-4">
                            <h2 className="text-lg font-bold py-2">
                              {profile?.username}
                            </h2>
                            <p className="text-gray-500">{profile?.name}</p>
                          </div>
                        </div>

                        <div className="mt-4 flex items-center gap-5 lg:gap-10 justify-between p-4 rounded-lg whitespace-nowrap">
                          <div className="flex items-center">
                            <img
                              src={siteConstant.SOCIAL_ICONS.FLOWKARLOGO}
                              alt="Social Icon"
                              className="mr-2 bg-gray-50 rounded-lg p-2 h-12 w-12"
                            />
                            <span className="text-md sm:text-lg font-bold"></span>
                          </div>
                          <a
                            onClick={() => navigate("/profile")}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="bg-Red text-white font-semibold py-2 px-3 rounded-lg text-sm sm:text-[16px]"
                          >
                            Visit Profile
                          </a>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          )}
        </div>
      ) : (
        <div className="flex items-center justify-center bg-Red mt-20">
          <p className="text-center text-gray-500 text-sm md:text-lg ">
            No Data Available
          </p>
        </div>
      )}
    </div>
  );
};

export default ShareProfile;
