import React from "react";
import { <PERSON>, Skeleton, Stack, Avatar } from "@mui/material";

const shimmerStyle = {
  background: 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)', 
  backgroundSize: '200% 100%',
  animation: 'shimmer 5s infinite',
};

const DashboardSkeleton = () => (
  <Box className="px-4 md:px-10 pb-20 font-Ubuntu bg-[#FAFAFB] mt-[26px] mb-[26px]"> {/* light grey background */}
    <style>{`
      @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
      }
    `}</style>
    <div className="pt-4">
      {/* Main Content - Two Column Layout with Equal Heights */}
      <div className="flex flex-col md:flex-row gap-6 md:h-[695px]">
        {/* Left Column - WelcomeCard, <PERSON>s<PERSON><PERSON><PERSON>, QuickPostUpload */}
        <div className="w-full md:w-[45%] flex flex-col h-full gap-6">
          {/* WelcomeCard Skeleton */}
          <Box className="flex gap-5 items-center bg-gradient-to-r from-[#FAFAFB] to-[#F0F0F0] border-[1px] border-[#E0E0E0] rounded-[16px] w-full px-4 mb-2 h-[120px] shadow-sm">
            <Skeleton variant="circular" width={72} height={72} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
            <Box className="flex-1">
              <Skeleton variant="text" width={180} height={28} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
              <Skeleton variant="rectangular" width={120} height={20} sx={{ mt: 2, ...shimmerStyle, bgcolor: '#F0F0F0' }} />
            </Box>
            <Skeleton variant="circular" width={32} height={32} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
          </Box>
          {/* Today's Planner Skeleton */}
          <Box className="bg-gradient-to-r from-[#FAFAFB] to-[#F0F0F0] rounded-2xl px-4 border-[1px] border-[#E0E0E0] w-full shadow mb-2 h-[280px] flex flex-col">
            <Skeleton variant="text" width={160} height={32} sx={{ my: 2, ...shimmerStyle, bgcolor: '#F0F0F0' }} />
            <Stack spacing={2} className="flex-grow">
              {[...Array(4)].map((_, i) => (
                <Box key={i} className="flex items-center space-x-4">
                  <Skeleton variant="circular" width={40} height={40} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                  <Box>
                    <Skeleton variant="text" width={100} height={18} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                    <Skeleton variant="text" width={140} height={16} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                  </Box>
                  <Skeleton variant="rectangular" width={32} height={16} sx={{ borderRadius: 8, ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                </Box>
              ))}
            </Stack>
          </Box>
          {/* QuickPostUpload Skeleton */}
          <Box className="bg-gradient-to-r from-[#FAFAFB] to-[#F0F0F0] border border-[#E0E0E0] rounded-2xl p-4 flex-grow h-full md:h-[240px] flex flex-col shadow">
            <Skeleton variant="text" width={140} height={28} sx={{ mb: 2, ...shimmerStyle, bgcolor: '#F0F0F0' }} />
            <Skeleton variant="rectangular" width="100%" height={80} sx={{ mb: 3, ...shimmerStyle, bgcolor: '#F0F0F0' }} />
            <Skeleton variant="rectangular" width={160} height={40} sx={{ borderRadius: 12, ...shimmerStyle, bgcolor: '#F0F0F0' }} />
          </Box>
        </div>
        {/* Right Column - AnalyticsSnapshot, RecentPosts */}
        <div className="w-full md:w-[55%] flex flex-col h-full gap-6">
          {/* AnalyticsSnapshot Skeleton */}
          <Box className="flex flex-col justify-center items-center bg-gradient-to-r from-[#FAFAFB] to-[#F0F0F0] border-[1px] border-[#E0E0E0] rounded-[16px] w-full px-4 mb-2 h-[180px] shadow-sm">
            <Box className="flex justify-between items-center w-full">
              <Skeleton variant="text" width={160} height={32} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
              <Skeleton variant="text" width={100} height={24} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
            </Box>
            <Box className="flex gap-5 justify-between items-center w-full mt-4">
              {[...Array(4)].map((_, i) => (
                <Box key={i} className="text-center rounded-[12px] p-2">
                  <Skeleton variant="circular" width={32} height={32} sx={{ mx: 'auto', ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                  <Skeleton variant="text" width={40} height={18} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                  <Skeleton variant="text" width={60} height={14} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                </Box>
              ))}
            </Box>
          </Box>
          {/* RecentPosts Skeleton */}
          <Box className="bg-gradient-to-r from-[#FAFAFB] to-[#F0F0F0] border border-[#E0E0E0] rounded-2xl flex-grow h-[522px] flex flex-col shadow p-3">
            <Box className="flex justify-between items-center mb-4">
              <Skeleton variant="text" width={140} height={28} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
              <Skeleton variant="rectangular" width={40} height={20} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0', borderRadius: 8 }} />
            </Box>
            {[...Array(1)].map((_, i) => (
              <Box key={i} className="flex flex-col gap-4">
                {/* User Info */}
                <Box className="flex gap-4 items-center">
                  <Skeleton variant="circular" width={56} height={56} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                  <Box className="flex-1">
                    <Skeleton variant="text" width={100} height={20} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                    <Box className="flex gap-2 items-center mt-1">
                      <Skeleton variant="text" width={60} height={16} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                      <Skeleton variant="rectangular" width={8} height={8} sx={{ borderRadius: 4, ...shimmerStyle, bgcolor: '#E0E0E0' }} />
                      <Skeleton variant="text" width={80} height={16} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
                    </Box>
                  </Box>
                </Box>
                {/* Title & Description */}
                <Box className="mt-2 mb-2">
                  <Skeleton variant="text" width={180} height={20} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0', mb: 1 }} />
                  <Skeleton variant="text" width={260} height={16} sx={{ ...shimmerStyle, bgcolor: '#E0E0E0' }} />
                </Box>
                {/* Media (Image/Video) */}
                <Skeleton variant="rectangular" width="100%" height={220} sx={{ borderRadius: 3, ...shimmerStyle, bgcolor: '#F0F0F0', mb: 2 }} />
                {/* Slider Dots */}
                <Box className="flex gap-2 justify-center mt-2 mb-2">
                  {[...Array(3)].map((_, idx) => (
                    <Skeleton key={idx} variant="circular" width={10} height={10} sx={{ ...shimmerStyle, bgcolor: '#E0E0E0' }} />
                  ))}
                </Box>
              </Box>
            ))}
          </Box>
        </div>
      </div>
      {/* Bottom Section - Chart and Channel Connect */}
      <div className="flex flex-col md:flex-row gap-6 mt-[26px] items-stretch">
        <div className="w-full md:w-[65%]">
          <Box className="border border-[#E0E0E0] rounded-2xl overflow-hidden h-full bg-gradient-to-r from-[#FAFAFB] to-[#F0F0F0] shadow">
            <Skeleton variant="rectangular" width="100%" height={320} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
          </Box>
        </div>
        <div className="w-full md:w-[35%]">
          <Box className="h-full bg-gradient-to-r from-[#FAFAFB] to-[#F0F0F0] rounded-2xl shadow">
            <Skeleton variant="rectangular" width="100%" height={320} sx={{ ...shimmerStyle, bgcolor: '#F0F0F0' }} />
          </Box>
        </div>
      </div>
    </div>
  </Box>
);

export default DashboardSkeleton; 