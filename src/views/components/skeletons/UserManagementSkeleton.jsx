import React from "react";
import { Box, Skeleton, Stack } from "@mui/material";

const UserManagementSkeleton = () => (
  <Box className="mx-auto p-8 bg-[#FFFFFF] rounded-lg shadow-sm font-Ubuntu mt-4 min-h-screen ">
    {/* Header */}
    <Skeleton variant="text" width={240} height={36} className="mb-6" />

    {/* Gradient Info Card */}
    <Box className="mt-6 mb-6 p-[2px] rounded-[12px] bg-gradient-to-r from-[#563D39] to-[#BC857D]">
      <Box className="bg-[#FFFFFF] rounded-lg p-3 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <Box className="flex gap-4 items-center">
          <Skeleton variant="circular" width={32} height={32} />
          <Skeleton variant="text" width={260} height={24} />
        </Box>
        <Skeleton variant="rectangular" width={180} height={40} style={{ borderRadius: 10 }} />
      </Box>
    </Box>

    {/* Tabs */}
    <Box className="border-b border-gray-200 mb-6 mt-4">
      <Box className="flex">
        {[...Array(3)].map((_, i) => (
          <Box key={i} className="flex flex-row-reverse items-center gap-2 py-3 px-6">
            <Skeleton variant="circular" width={20} height={20} />
            <Skeleton variant="text" width={90} height={24} />
          </Box>
        ))}
      </Box>
    </Box>

    {/* Search bar and Add User button */}
    <Box className="flex flex-col md:flex-row gap-4 mb-8">
      <Box className="relative flex-grow">
        <Skeleton variant="rectangular" width="100%" height={44} style={{ borderRadius: 10 }} />
        {/* Search icon and clear icon as circles */}
        <Box className="absolute left-3 top-1/2 -translate-y-1/2">
          <Skeleton variant="circular" width={20} height={20} />
        </Box>
        <Box className="absolute right-3 top-1/2 -translate-y-1/2">
          <Skeleton variant="circular" width={20} height={20} />
        </Box>
      </Box>
      <Box className="flex items-center gap-20">
        <Skeleton variant="rectangular" width={220} height={44} style={{ borderRadius: 10 }} />
      </Box>
    </Box>

    {/* Main content area: tab switcher and user/role rows */}
    <Box className="flex flex-col gap-6">
      {/* Tab switcher (for Users/Pending) */}
      <Box className="flex justify-center items-center mb-6">
        <Box className="flex bg-[#FFFFFF] rounded-[8px] border-[1px] border-[#E0E0E0]">
          {[...Array(2)].map((_, i) => (
            <Skeleton
              key={i}
              variant="rectangular"
              width={110}
              height={40}
              style={{ borderRadius: 8, margin: 8 }}
            />
          ))}
        </Box>
      </Box>
      {/* Table headers (for md+) */}
      <Box className="rounded-lg hidden md:flex justify-start items-center text-base text-[#000000] font-normal py-5">
        <Skeleton variant="text" width={120} height={28} style={{ marginLeft: 20 }} />
        <Skeleton variant="text" width={120} height={28} style={{ marginLeft: 20 }} />
        <Skeleton variant="text" width={120} height={28} style={{ marginLeft: 20 }} />
      </Box>
      {/* User/role rows */}
      <Stack spacing={3} className="space-y-4 mt-2">
        {[...Array(4)].map((_, i) => (
          <Box
            key={i}
            className="bg-[#FFFFFF] border-[1px] border-[#E0E0E0] p-4 rounded-lg flex flex-col md:flex-row md:items-center md:justify-between gap-4"
          >
            {/* Avatar + Email/Role */}
            <Box className="flex items-center gap-4 w-full md:w-1/3">
              <Skeleton variant="circular" width={48} height={48} />
              <Skeleton variant="text" width={120} height={24} />
            </Box>
            {/* Brands/Permissions */}
            <Box className="flex flex-wrap gap-2 w-full md:w-1/3">
              <Skeleton variant="text" width={140} height={20} />
            </Box>
            {/* Actions/Users */}
            <Box className="relative w-full md:w-1/3 flex justify-between items-center">
              <Skeleton variant="rectangular" width={80} height={32} style={{ borderRadius: 8 }} />
              <Skeleton variant="circular" width={24} height={24} style={{ marginLeft: 16 }} />
            </Box>
          </Box>
        ))}
      </Stack>
    </Box>
  </Box>
);

export default UserManagementSkeleton; 