import React, { useContext, useEffect, useState } from "react";
import { IntlContext } from "../../../App";
import apiInstance from "../../../helpers/Axios/axiosINstance";
import siteConstant from "../../../helpers/constant/siteConstant";
import { URL } from "../../../helpers/constant/Url";
import { getFileType } from "../../../helpers/constant/utils";
import no_schedule_post from "../../../assets/images/No-schedule-post.svg";
import { RiDeleteBin6Line } from "react-icons/ri";
import DeletePostModel from "../profile/deletePost";
import Loader from "../../../helpers/UI/Loader";
import { useSelector } from "react-redux";
import { fetchFromStorage } from "../../../helpers/context/storage";

const PostCard = ({ post, fetchPosts, activeList }) => {
  const [openDialog, setOpenDialog] = useState(false);
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const userData = fetchFromStorage(siteConstant.INDENTIFIERS.USERDATA);

  const handleClickOpen = () => {
    setOpenDialog(true);
  };

  const handleClose = () => {
    setOpenDialog(false);
  };

  const renderThumbnail = (url) => {
    const fileType = getFileType(url);
    if (fileType === "video") {
      return (
        <div className="relative">
          <video
            className="w-full h-auto sm:w-[100px] sm:h-[100px] rounded-lg object-cover"
            src={url}
            alt="Video Thumbnail"
            muted
            playsInline
          />
          <div className="absolute top-1 right-1">
            <img
              src={siteConstant?.SOCIAL_ICONS?.REELS_ICON}
              alt="Reels"
              className="w-5 h-5"
            />
          </div>
        </div>
      );
    }
    return (
      <img
        src={url}
        alt="Thumbnail"
        className="w-full h-auto sm:w-[100px] sm:h-[100px] rounded-lg object-cover"
        loading="lazy"
      />
    );
  };

  return (
    <div>
      <div className="bg-white  rounded-[18.73px] p-5 mb-5 shadow-custom-shadow scheduled">
        <div>
          <div className="flex flex-row items-center justify-between gap-3 mb-5 ">
            <div className="flex items-center gap-3 whitespace-nowrap scheduled-sub">
              <img
                src={
                  (post?.user
                    ? post?.user?.profile_image
                    : userData?.profile_image) ||
                  siteConstant?.SOCIAL_ICONS?.DUMMY_PROFILE
                }
                loading="lazy"
                alt="user"
                className="w-[56px] h-[56px] rounded-[23px] p-1 border-2 border-Red scheduled-user"
              />
              <div>
                <h4 className="font-bold text-[#000000] text-[16px] 2xl:text-[16px] leading-[25.11px] font-Ubuntu scheduled-name pe-1">
                  {post?.user ? post?.user?.name : userData?.name}
                </h4>
                <p className="text-[#555555] text-sm 2xl:text-[14.98px] leading-[18.26px] pt-1 font-Ubuntu font-normal scheduled-username ">
                  @{post?.user ? post?.user?.username : userData?.username}
                </p>
              </div>
            </div>
            {activeList === "list1" && (
              <div className="flex items-center gap-2 p-2 right-side">
                <span className="bg-[#efebe9] text-Red text-sm font-semibold px-3 2xl:px-4 py-2 rounded-md scheduled-status">
                  {localesData?.USER_WEB?.SCHEDULED}
                </span>
                <div
                  className="bg-gray-100 rounded-md flex justify-center items-center text-Red h-9 w-9 scheduled-delete hover:bg-profileCardBGcursor-pointer"
                  onClick={handleClickOpen}
                >
                  <RiDeleteBin6Line style={{ height: "22px", width: "24px" }} />
                </div>
              </div>
            )}
            {activeList === "list2" && (
              <div className="flex items-center gap-2 p-2">
                <span className="bg-[#efebe9] text-Red text-sm font-semibold px-3 2xl:px-4 py-2 rounded-md scheduled-status">
                  {localesData?.USER_WEB?.DELETED}
                </span>
              </div>
            )}
          </div>

          <hr className="my-4 border-t-2 border-gray-100" />

          <div className="flex flex-col sm:flex-row gap-4">
            {post?.files && post?.files?.length > 0 ? (
              renderThumbnail(post?.files[0])
            ) : (
              <div className="w-full h-auto sm:w-[100px] sm:h-[100px] rounded-lg object-cover">
                <p className="text-gray-500 text-[11px] sm:text-[13px]">
                  {" "}
                  {localesData?.USER_WEB?.NO_IMAGE_OR_VIDEO_AVAILABLE}
                </p>
              </div>
            )}
            <p className="text-[#555555] font-Ubuntu font-normal text-sm sm:text-[14px] 2xl:text-[15px] leading-[24px] line-clamp-1 break-words sm:line-clamp-2 sm:break-words overflow-hidden">
              {post?.description
                ? post?.description
                : "No description available."}
            </p>
          </div>

          <div className="flex justify-end">
            <span className="text-sm text-[#252520] text-[18.72px] block mt-2 font-Ubuntu">
              {post?.scheduled_at}
            </span>
          </div>
          <DeletePostModel
            open={openDialog}
            handleDialogClose={handleClose}
            fetchPosts={fetchPosts}
            post={post}
            postDeleteURL={URL.REMOVE_SCHEDULED_POSTS}
          />
        </div>
      </div>
    </div>
  );
};

export default function ScheduledPost() {
  const reduxData = useSelector((state) => state.schedule.value);
  let [value, setValue] = useState([]);
  let [deleteValue, setdeleteValue] = useState([]);
  let [scheduleData, setScheduleData] = useState({
    today: 0,
    tomorrow: 0,
    this_month: 0,
    data: [],
  });
  const intlContext = useContext(IntlContext);
  const localesData = intlContext?.messages;
  const [activeList, setActiveList] = useState("list1");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setValue([reduxData.data, ...value]);
  }, [reduxData]);

  const handleListClick = (listId) => {
    setActiveList(listId);
  };

  const fetchSchedulePost = async (list) => {
    try {
      list = list ? list : activeList;
      const { status, data } = await apiInstance.get(
        `${URL.SCHEDULED_POST}${list == "list2" ? "?unscheduled_post=1" : ""}`
      );
      if (list == "list2") {
        setdeleteValue(data?.data);
      } else {
        setValue(data?.data);
      }
      setIsLoading(false);
      setScheduleData({
        today: data?.today,
        tomorrow: data?.tomorrow,
        this_month: data?.this_month,
        data: data?.data || [],
      });
    } catch (e) {
      console.log("Err", e);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchSchedulePost(activeList);
    // fetchDeleteSchedulePost();
  }, [activeList]);

  if (isLoading) {
    return <Loader />;
  }
  return (
    <div className="pb-20">
      <div className="flex flex-col lg:flex-row lg:justify-between p-4 space-y-4 lg:space-y-0 lg:space-x-4">
        <div className="w-full lg:w-1/1 flex flex-col gap-3">
          <div className="flex flex-col gap-2">
            {value?.length || deleteValue?.length ? (
              <div className="relative mb-2">
                <div className="flex justify-center items-center relative  sm:w-6/12 md:w-5/12 lg:w-6/12 xl:w-5/12 2xl:w-4/12 ">
                  <div className="bg-[#efebe9] p-px sm:p-1 rounded-full w-full relative overflow-hidden">
                    <div
                      className={`absolute top-0 left-0 h-full w-[50%] bg-Red transition-transform duration-500 ease-in-out rounded-full ${
                        activeList === "list1"
                          ? "translate-x-0"
                          : "translate-x-full"
                      }`}
                    />
                    <button
                      className={`relative z-10 w-1/2 py-2 text-[12px] sm:text-sm transition-all duration-300 ease-in-out ${
                        activeList === "list1"
                          ? "text-white font-bold"
                          : "text-gray-600"
                      }`}
                      onClick={() => handleListClick("list1")}
                    >
                      {localesData?.USER_WEB?.SCHEDULED_LIST}
                    </button>
                    <button
                      className={`relative z-10 w-1/2 py-2 text-[12px] sm:text-sm transition-all duration-300 ease-in-out ${
                        activeList === "list2"
                          ? "text-white font-bold"
                          : "text-gray-600"
                      }`}
                      onClick={() => handleListClick("list2")}
                    >
                      {localesData?.USER_WEB?.DELETE_LIST}
                    </button>
                  </div>
                </div>
              </div>
            ) : null}

            <>
              {activeList === "list1" ? (
                value?.length ? (
                  value?.map((item, index) => (
                    <PostCard
                      post={item}
                      fetchPosts={fetchSchedulePost}
                      activeList={activeList}
                    />
                  ))
                ) : (
                  <div className="font-Ubuntu rounded-lg  sm:h-full pb-4 sm:pb-0">
                    <div className="text-center mt-5 md:mt-0 2xl:mt-8 mx-12">
                      <img
                        src={no_schedule_post}
                        alt="no_schedule_post "
                        className="mx-auto "
                        loading="lazy"
                      />
                      <div className="pt-3">
                        <p className=" no-scheduled text-xl sm:text-2xl font-semibold mb-2">
                          {localesData?.USER_WEB?.NO_POSTS_SCHEDULED}
                        </p>
                        <p className="scheduled-text text-black font-normal text-[14px]">
                          {localesData?.USER_WEB?.PLAN_AHEAD_AND_STAY_ON_TRACK}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              ) : null}
              {activeList === "list2" &&
                (deleteValue?.length === 0 ? (
                  <div className="font-Ubuntu rounded-lg sm:h-full pb-4 sm:pb-0">
                    <div className="text-center mt-10 sm:mt-20 lg:mt-10 mx-12">
                      <img
                        src={no_schedule_post}
                        alt="no_schedule_post"
                        className="mx-auto"
                        loading="lazy"
                      />
                      <div className="pt-5">
                        <p className="text-xl sm:text-2xl font-semibold mb-2">
                          {localesData?.USER_WEB?.NO_POSTS_SCHEDULED}
                        </p>
                        <p className="text-gray-500 text-[14px]">
                          {localesData?.USER_WEB?.PLAN_AHEAD_AND_STAY_ON_TRACK}
                        </p>
                      </div>
                    </div>
                  </div>
                ) : (
                  deleteValue?.map((item, index) => (
                    <PostCard
                      key={index}
                      post={item}
                      fetchPosts={fetchSchedulePost}
                      activeList={activeList}
                    />
                  ))
                ))}
            </>
          </div>
        </div>
        <div className="w-full lg:w-1/2">
          <div className="bg-[#e1e1e126]  flex flex-col gap-4 pb-2 shadow-custom-shadow rounded-lg font-Ubuntu">
            <div className="w-full bg-[#FFFFFF] p-5 rounded-lg">
              <div className="text-xl lg:text-[20px] 2xl:text-[22px] p-2 font-bold font-Ubuntu leading-[29.26px]">
                {localesData?.USER_WEB?.SCHEDULED}
              </div>
            </div>

            <div className="w-[95%] mx-auto bg-[#FFFFFF] p-5 rounded-lg">
              <h4 className="text-lg lg:text-[18px] 2xl:text-[20px] text-Red font-bold font-Ubuntu leading-[29.26px]">
                {localesData?.USER_WEB?.TODAY_POST}
              </h4>
              <p className="text-xl lg:text-[20px] 2xl:text-[22px] pt-3 text-[#0A043C] font-Ubuntu font-bold">
                {scheduleData.today}
              </p>
            </div>

            <div className="w-[95%] mx-auto bg-[#FFFFFF] p-5 rounded-lg">
              <h4 className="text-lg lg:text-[18px] 2xl:text-[20px] text-Red font-bold font-Ubuntu leading-[29.26px]">
                {localesData?.USER_WEB?.TOMORROW_POST}
              </h4>
              <p className="text-xl lg:text-[20px] 2xl:text-[22px] pt-3 text-[#0A043C] font-Ubuntu font-bold">
                {scheduleData.tomorrow}
              </p>
            </div>

            <div className="w-[95%] mx-auto bg-[#FFFFFF] p-5 mb-2 rounded-lg">
              <h4 className="text-lg lg:text-[18px] 2xl:text-[20px] text-Red font-bold font-Ubuntu leading-[29.26px]">
                {localesData?.USER_WEB?.THIS_MONTH_POST}
              </h4>
              <p className="text-xl lg:text-[20px] 2xl:text-[22px] pt-3 text-[#0A043C] font-Ubuntu font-bold">
                {scheduleData.this_month}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
