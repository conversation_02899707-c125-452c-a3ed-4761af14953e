import React from "react";

const TableSkeleton = ({ rows = 15 }) => (
  <div className="min-w-full container overflow-x-auto channel-scrollbar font-Ubuntu">
    <table className="min-w-full table-auto text-center text-gray-500 dark:text-gray-400 border-separate border-spacing-y-4 ">
      <thead>
        <tr className="bg-profileCardBG text-Red font-Ubuntu whitespace-nowrap ps-6 rounded-md">
          <th className="py-6 text-[14px] sm:text-[15px] ps-3">S. No.</th>
          <th className="py-6 text-[14px] sm:text-[15px] ps-3">User ID</th>
          <th className="py-8 text-[14px] sm:text-[15px] ps-3">Profile Image</th>
          <th className="text-[14px] sm:text-[15px] text-left ps-14">Username</th>
          <th className="text-[14px] sm:text-[15px] text-left ps-16">Email</th>
          <th className="text-[14px] sm:text-[15px] ">Date</th>
          <th className="text-[14px] sm:text-[15px] ">Social Media Connected</th>
          <th className="text-[14px] sm:text-[15px] ">Posts</th>
          <th className="text-[14px] sm:text-[15px] ">Status</th>
          <th className="text-[14px] sm:text-[15px]">Action</th>
        </tr>
      </thead>
      <tbody className="font-Ubuntu">
        {Array.from({ length: rows }).map((_, idx) => (
          <tr key={idx} className="border-b bg-white hover:bg-primarBG">
            {/* S. No. */}
            <td className="p-5">
              <div className="h-4 w-8 bg-gray-200 rounded mx-auto animate-pulse" />
            </td>
            {/* User ID */}
            <td className="p-5">
              <div className="h-4 w-16 bg-gray-200 rounded mx-auto animate-pulse" />
            </td>
            {/* Profile Image */}
            <td>
              <div className="flex items-center justify-center gap-3 py-2">
                <div className="border-2 border-Red p-[5px] rounded-[20px] lg:rounded-[25px] bg-gray-200 h-14 w-14 sm:h-14 sm:w-14 lg:h-16 lg:w-16 animate-pulse" />
              </div>
            </td>
            {/* Username */}
            <td className="p-5 text-left">
              <div className="ps-10 h-4 w-24 bg-gray-200 rounded animate-pulse" />
            </td>
            {/* Email */}
            <td className="p-5 text-left">
              <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
            </td>
            {/* Date */}
            <td className="p-5">
              <div className="h-4 w-20 bg-gray-200 rounded mx-auto animate-pulse" />
            </td>
            {/* Social Media Connected */}
            <td className="p-5">
              <div className="h-4 w-24 bg-gray-200 rounded mx-auto animate-pulse" />
            </td>
            {/* Posts */}
            <td className="p-5">
              <div className="h-4 w-12 bg-gray-200 rounded mx-auto animate-pulse" />
            </td>
            {/* Status */}
            <td className="p-5">
              <div className="h-6 w-20 bg-gray-200 rounded-xl mx-auto animate-pulse" />
            </td>
            {/* Action */}
            <td className="py-4 flex justify-center gap-4 px-5">
              <div className="bg-gray-100 rounded-md flex justify-center items-center text-Red h-12 w-12">
                <div className="h-6 w-6 bg-gray-200 rounded animate-pulse" />
              </div>
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  </div>
);

export const CardSkeleton = ({ count = 6 }) => (
  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4 mt-6 mb-8">
    {Array.from({ length: count }).map((_, idx) => (
      <div key={idx} className="bg-white rounded-xl shadow p-5 flex flex-col items-center animate-pulse">
        <div className="h-8 w-16 bg-gray-200 rounded mb-2" />
        <div className="h-4 w-24 bg-gray-200 rounded mt-2" />
      </div>
    ))}
  </div>
);

export default TableSkeleton; 