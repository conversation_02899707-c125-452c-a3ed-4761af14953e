import React from "react";
import LazyImage from "./LazyImage";

// Thumbnail Component
export const LazyThumbnail = ({
  src,
  alt = "Thumbnail",
  className = "",
  aspectRatio = "square",
  fallbackSrc,
  ...props
}) => {
  const aspectClasses = {
    square: "aspect-square",
    video: "aspect-video",
    portrait: "aspect-[3/4]",
    landscape: "aspect-[4/3]",
  };

  const baseClasses = `object-cover rounded-lg ${aspectClasses[aspectRatio]}`;

  return (
    <LazyImage
      src={src}
      alt={alt}
      className={`${baseClasses} ${className}`}
      skeletonVariant="rectangular"
      fallbackSrc={fallbackSrc}
      {...props}
    />
  );
};

// Logo Component
export const LazyLogo = ({
  src,
  alt = "Logo",
  size = "medium",
  className = "",
  fallbackSrc,
  ...props
}) => {
  const sizeClasses = {
    small: "w-6 h-6",
    medium: "w-8 h-8",
    large: "w-12 h-12",
  };

  const baseClasses = `${sizeClasses[size]} object-contain`;

  return (
    <LazyImage
      src={src}
      alt={alt}
      className={`${baseClasses} ${className}`}
      skeletonVariant="rectangular"
      fallbackSrc={fallbackSrc}
      {...props}
    />
  );
};

// Banner/Cover Image Component
export const LazyBanner = ({
  src,
  alt = "Banner",
  className = "",
  height = "280px",
  fallbackSrc,
  ...props
}) => {
  const baseClasses = `w-full object-cover`;
  const heightClass = `h-[${height}]`;

  return (
    <LazyImage
      src={src}
      alt={alt}
      className={`${baseClasses} ${heightClass} ${className}`}
      skeletonVariant="rectangular"
      fallbackSrc={fallbackSrc}
      {...props}
    />
  );
};

// Post Media Component (for posts with images/videos)
export const LazyPostMedia = ({
  src,
  alt = "Post media",
  className = "",
  fallbackSrc,
  ...props
}) => {
  const baseClasses = "w-full h-full object-cover rounded-xl";

  return (
    <LazyImage
      src={src}
      alt={alt}
      className={`${baseClasses} ${className}`}
      skeletonVariant="rectangular"
      fallbackSrc={fallbackSrc}
      {...props}
    />
  );
};

// Icon Component (for small icons and social media icons)
export const LazyIcon = ({
  src,
  alt = "Icon",
  size = "small",
  className = "",
  fallbackSrc,
  ...props
}) => {
  const sizeClasses = {
    tiny: "w-4 h-4",
    small: "w-5 h-5",
    medium: "w-6 h-6",
    large: "w-8 h-8",
  };

  const baseClasses = `${sizeClasses[size]} object-contain`;

  return (
    <LazyImage
      src={src}
      alt={alt}
      className={`${baseClasses} ${className}`}
      skeletonVariant="rectangular"
      fallbackSrc={fallbackSrc}
      {...props}
    />
  );
};
