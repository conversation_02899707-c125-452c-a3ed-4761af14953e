import React from 'react';

const LoadingSpinner = ({ text = 'Loading...', spinnerSize = 'h-5 w-5', textColor = 'text-white', className = '' }) => {
    return (
        <span className={`flex items-center justify-center space-x-2 ${className}`}>
            <svg
                className={`animate-spin ${spinnerSize} ${textColor}`}
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                ></circle>
                <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8v8H4z"
                ></path>
            </svg>
            <span>{text}</span>
        </span>
    );
};

export default LoadingSpinner;
