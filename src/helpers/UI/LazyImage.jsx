import React, { useState, useRef, useEffect } from "react";
import { Skeleton } from "@mui/material";

const LazyImage = ({
  src,
  alt = "",
  className = "",
  skeletonClassName = "",
  skeletonVariant = "rectangular",
  skeletonWidth,
  skeletonHeight,
  fallbackSrc,
  onLoad,
  onError,
  loading = "lazy",
  threshold = 0.1,
  ...props
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);
  const [inView, setInView] = useState(false);
  const imgRef = useRef(null);
  const observerRef = useRef(null);

  useEffect(() => {
    const currentImg = imgRef.current;
    
    if (!currentImg) return;

    // Create intersection observer to detect when image enters viewport
    observerRef.current = new IntersectionObserver(
      (entries) => {
        const [entry] = entries;
        if (entry.isIntersecting) {
          setInView(true);
          observerRef.current?.disconnect();
        }
      },
      { threshold }
    );

    observerRef.current.observe(currentImg);

    return () => {
      observerRef.current?.disconnect();
    };
  }, [threshold]);

  const handleImageLoad = (e) => {
    setImageLoaded(true);
    onLoad?.(e);
  };

  const handleImageError = (e) => {
    setImageError(true);
    if (fallbackSrc && e.target.src !== fallbackSrc) {
      e.target.src = fallbackSrc;
      setImageError(false);
    } else {
      onError?.(e);
    }
  };

  // Determine skeleton dimensions
  const getSkeletonDimensions = () => {
    if (skeletonWidth && skeletonHeight) {
      return { width: skeletonWidth, height: skeletonHeight };
    }
    
    // Try to extract dimensions from className
    const widthMatch = className.match(/w-\[?(\d+(?:px|rem|em|%)?)\]?/);
    const heightMatch = className.match(/h-\[?(\d+(?:px|rem|em|%)?)\]?/);
    
    return {
      width: widthMatch ? widthMatch[1] : "100%",
      height: heightMatch ? heightMatch[1] : "200px"
    };
  };

  const skeletonDimensions = getSkeletonDimensions();

  // Show skeleton while image is not loaded or not in view
  if (!inView || (!imageLoaded && !imageError)) {
    return (
      <div ref={imgRef} className={className}>
        <Skeleton
          variant={skeletonVariant}
          width={skeletonDimensions.width}
          height={skeletonDimensions.height}
          className={skeletonClassName}
          animation="wave"
          sx={{
            bgcolor: 'rgba(0, 0, 0, 0.06)',
            borderRadius: className.includes('rounded') ? 'inherit' : 0,
          }}
        />
      </div>
    );
  }

  return (
    <img
      ref={imgRef}
      src={inView ? src : undefined}
      alt={alt}
      className={`${className} ${imageLoaded ? 'opacity-100' : 'opacity-0'} transition-opacity duration-300`}
      onLoad={handleImageLoad}
      onError={handleImageError}
      loading={loading}
      {...props}
    />
  );
};

export default LazyImage;
