import React, { createContext, useEffect, useState } from "react";
import { io } from "socket.io-client";

const SOCKET_URL = "https://api.flowkar.com/";
const LIVE_SOCKET_URL = "https://live.flowkar.com/"

// Create socket instance with options
const socket = io(SOCKET_URL, {
  path: "/socket.io/",
  jsonp: false,
  upgrade: false,
  reconnection: true,
  reconnectionDelay: 3000,
  reconnectionDelayMax: 10000,
  timeout: 20000,
});

// Add a dedicated live streaming socket instance
import { io as liveIo } from "socket.io-client";

export const liveSocket = liveIo(LIVE_SOCKET_URL, {
  path: "/socket.io/",
  transports: ["websocket"],
  reconnection: true,
  reconnectionDelay: 3000,
  reconnectionDelayMax: 10000,
  timeout: 20000,
});

// Create context
const SocketContext = createContext();

// Context Provider component
const SocketProvider = ({ children }) => {
  const [isConnected, setIsConnected] = useState(false);

  console.log("isConnected", isConnected);
  useEffect(() => {
    // Connection status handling
    const onConnect = () => {
      console.log("Socket connected successfully");
      setIsConnected(true);
    };

    const onDisconnect = () => {
      console.log("Socket disconnected");
      setIsConnected(false);
    };

    const onError = (error) => {
      console.error("Socket error:", error);
    };

    // Set up listeners
    socket.on("connect", onConnect);
    socket.on("disconnect", onDisconnect);
    socket.on("error", onError);

    // Connect socket if not already connected
    if (!socket.connected) {
      socket.connect();
    }

    // Cleanup listeners on component unmount
    return () => {
      socket.off("connect", onConnect);
      socket.off("disconnect", onDisconnect);
      socket.off("error", onError);
    };
  }, []);

  return (
    <SocketContext.Provider value={socket}>{children}</SocketContext.Provider>
  );
};

export { SocketContext, SocketProvider, socket };

// import React, { createContext } from "react";
// import { io } from "socket.io-client";

// const SOCKET_URL = "https://api.flowkar.com";
// let socket = io(SOCKET_URL, {
//   path: "/socket.io/",
//   jsonp: false,
//   upgrade: false,
//   reconnection: true,
// });

// socket = socket.connect();

// const SocketContext = createContext();

// export { SocketContext, socket };
