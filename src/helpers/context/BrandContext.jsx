import { createContext, useContext, useState, useEffect } from "react";
import { URL } from "../constant/Url";
import apiInstance from "../Axios/axiosINstance";
import siteConstant from "../constant/siteConstant";
import { fetchFromStorage } from "./storage";

const BrandContext = createContext();
const UserContext = createContext();

export const BrandProvider = ({ children }) => {
  const [selectedBrand, setSelectedBrand] = useState(null);
  const [brands, setBrands] = useState([]);
  const [loadingBrand, setLoadingBrand] = useState(true);
  const [brandsLoaded, setBrandsLoaded] = useState(false);
  const [userToken, setUserToken] = useState(null);

  // Effect to track user token changes
  useEffect(() => {
    const checkToken = () => {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const token = userData?.token;

      // If token changes or is removed, reset brand selection
      if (token !== userToken) {
        console.log("Token changed, resetting brand selection");
        setUserToken(token);
        if (!token) {
          // User logged out - reset everything
          setSelectedBrand(null);
          setBrands([]);
          setBrandsLoaded(false);
          localStorage.removeItem("BrandId");
        } else {
          // User logged in - trigger brand fetch
          setBrandsLoaded(false);
          setLoadingBrand(true);
        }
      }
    };

    // Check immediately
    checkToken();

    // Set up interval to check for token changes
    const intervalId = setInterval(checkToken, 1000);

    return () => {
      clearInterval(intervalId);
    };
  }, [userToken]); // Add userToken as dependency

  useEffect(() => {
    const fetchBrands = async () => {
      try {
        const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
        // Only fetch brands if the user is authenticated
        if (!userData?.token) return;

        setLoadingBrand(true);
        const response = await apiInstance.get(URL.GET_BRANDS);
        const fetchedBrands = response.data.data || [];
        console.log("Fetched brands from API:", fetchedBrands);
        setBrands(fetchedBrands);
        setBrandsLoaded(true);
      } catch (error) {
        console.error("Error fetching brands:", error);
      } finally {
        setLoadingBrand(false);
      }
    };

    if (userToken) {
      fetchBrands();
    }
  }, [userToken]); // Re-fetch brands when user token changes

  // Separate effect for brand selection that only runs after brands are loaded
  useEffect(() => {
    if (!brandsLoaded || !brands || brands.length === 0) {
      return;
    }

    console.log("Brands loaded, selecting brand...");
    console.log("Available brands:", brands);
    console.log(
      "Stored BrandId in localStorage:",
      localStorage.getItem("BrandId")
    );

    const storedBrandId = localStorage.getItem("BrandId");
    if (!storedBrandId) {
      console.log("No stored brand ID found");
      // Select first brand by default for new users
      const firstBrand = brands[0];
      if (firstBrand) {
        console.log("Selecting first brand:", firstBrand);
        setSelectedBrand(firstBrand);
        localStorage.setItem("BrandId", firstBrand.id.toString());
      }
      return;
    }

    const brandId = parseInt(storedBrandId, 10);
    console.log("Looking for brand with ID:", brandId);

    const brandToSelect = brands.find((b) => b.id === brandId);
    console.log("Found brand:", brandToSelect);

    if (brandToSelect) {
      console.log("Setting selected brand to:", brandToSelect);
      setSelectedBrand(brandToSelect);
    } else {
      console.log("Stored brand not found in available brands");
      // Only fall back to first brand if the stored brand is not found
      const firstBrand = brands[0];
      console.log("Falling back to first brand:", firstBrand);
      setSelectedBrand(firstBrand);
      localStorage.setItem("BrandId", firstBrand.id.toString());
    }
  }, [brandsLoaded, brands, userToken]); // Re-run when user token changes

  const handleBrandSelect = (brand) => {
    console.log("Handling brand selection:", brand);
    setSelectedBrand(brand);
    localStorage.setItem("BrandId", brand?.id?.toString());
  };

  const resetBrand = () => {
    setSelectedBrand(null);
    localStorage.removeItem("BrandId");
  };

  // Method to refresh brands after deletion
  const refreshBrands = async () => {
    try {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      if (!userData?.token) return;

      setLoadingBrand(true);
      const response = await apiInstance.get(URL.GET_BRANDS);
      const fetchedBrands = response.data.data || [];
      setBrands(fetchedBrands);
    } catch (error) {
      console.error("Error refreshing brands:", error);
    } finally {
      setLoadingBrand(false);
    }
  };

  return (
    <BrandContext.Provider
      value={{
        selectedBrand,
        handleBrandSelect,
        brands,
        setBrands,
        resetBrand,
        loadingBrand,
        refreshBrands,
      }}
    >
      {children}
    </BrandContext.Provider>
  );
};

export const useBrand = () => useContext(BrandContext);

// User Context Provider
export const UserProvider = ({ children }) => {
  const [selectedUser, setSelectedUser] = useState(null);
  const [availableUsers, setAvailableUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [usersLoaded, setUsersLoaded] = useState(false);
  const [userToken, setUserToken] = useState(null);

  // Effect to track user token changes
  useEffect(() => {
    const checkToken = () => {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      const token = userData?.token;

      // If token changes or is removed, reset user selection
      if (token !== userToken) {
        console.log("Token changed, resetting user selection");
        setUserToken(token);
        if (!token) {
          // User logged out - reset everything
          setSelectedUser(null);
          setAvailableUsers([]);
          setUsersLoaded(false);
          localStorage.removeItem("SelectedUserId");
        } else {
          // User logged in - trigger user fetch
          setUsersLoaded(false);
          setLoadingUsers(true);
        }
      }
    };

    checkToken();

    // Set up interval to check for token changes
    const interval = setInterval(checkToken, 1000);
    return () => clearInterval(interval);
  }, [userToken]);

  useEffect(() => {
    const fetchAvailableUsers = async () => {
      try {
        const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
        // Only fetch users if the user is authenticated
        if (!userData?.token) return;

        setLoadingUsers(true);
        const response = await apiInstance.get(URL.GET_INVITEE_USERS, {
          headers: {
            user: userData?.user_id,
          },
        });
        const fetchedUsers = response.data.data || [];
        console.log("Fetched available users from API:", fetchedUsers);

        // Add current user to the list if not already present
        const currentUser = {
          id: userData.user_id,
          user_id: userData.user_id,
          name: userData.name || userData.email,
          email: userData.email,
          profile_image: userData.profile_image,
          is_current: true,
        };

        const allUsers = [
          currentUser,
          ...fetchedUsers.filter((u) => u.user_id !== userData.user_id),
        ];
        setAvailableUsers(allUsers);
        setUsersLoaded(true);
      } catch (error) {
        console.error("Error fetching available users:", error);
      } finally {
        setLoadingUsers(false);
      }
    };

    if (userToken) {
      fetchAvailableUsers();
    }
  }, [userToken]); // Re-fetch users when user token changes

  // Effect to set initial selected user from localStorage or default to current user
  useEffect(() => {
    if (!usersLoaded || availableUsers.length === 0) return;

    const storedUserId = localStorage.getItem("SelectedUserId");
    console.log("Stored user ID from localStorage:", storedUserId);

    const userId = parseInt(storedUserId, 10);
    console.log("Looking for user with ID:", userId);

    const userToSelect = availableUsers.find(
      (u) => u.user_id === userId || u.id === userId
    );
    console.log("Found user:", userToSelect);

    if (userToSelect) {
      console.log("Setting selected user to:", userToSelect);
      setSelectedUser(userToSelect);
    } else {
      console.log("Stored user not found in available users");
      // Fall back to current user (first in list)
      const currentUser =
        availableUsers.find((u) => u.is_current) || availableUsers[0];
      console.log("Falling back to current user:", currentUser);
      setSelectedUser(currentUser);
      localStorage.setItem(
        "SelectedUserId",
        (currentUser.user_id || currentUser.id).toString()
      );
    }
  }, [usersLoaded, availableUsers, userToken]); // Re-run when user token changes

  const handleUserSelect = (user) => {
    console.log("Handling user selection:", user);
    setSelectedUser(user);
    localStorage.setItem(
      "SelectedUserId",
      (user?.user_id || user?.id)?.toString()
    );
  };

  const resetUser = () => {
    setSelectedUser(null);
    localStorage.removeItem("SelectedUserId");
  };

  // Method to refresh users after changes
  const refreshUsers = async () => {
    try {
      const userData = fetchFromStorage(siteConstant?.INDENTIFIERS?.USERDATA);
      if (!userData?.token) return;

      setLoadingUsers(true);
      const response = await apiInstance.get(URL.GET_INVITEE_USERS, {
        headers: {
          user: userData?.user_id,
        },
      });
      const fetchedUsers = response.data.data || [];

      // Add current user to the list
      const currentUser = {
        id: userData.user_id,
        user_id: userData.user_id,
        name: userData.name || userData.email,
        email: userData.email,
        profile_image: userData.profile_image,
        is_current: true,
      };

      const allUsers = [
        currentUser,
        ...fetchedUsers.filter((u) => u.user_id !== userData.user_id),
      ];
      setAvailableUsers(allUsers);
    } catch (error) {
      console.error("Error refreshing users:", error);
    } finally {
      setLoadingUsers(false);
    }
  };

  return (
    <UserContext.Provider
      value={{
        selectedUser,
        availableUsers,
        loadingUsers,
        handleUserSelect,
        resetUser,
        refreshUsers,
      }}
    >
      {children}
    </UserContext.Provider>
  );
};

export const useUser = () => useContext(UserContext);
