const renderHighlightedText = (text, data) => {
  const regex = /[@#][^\s@#]*/g;
  let lastIndex = 0;
  const elements = [];

  text.replace(regex, (match, offset) => {
    elements.push(text.slice(lastIndex, offset));
    elements.push(
      <span
        style={{
          color: "#0079FF",
          backgroundColor: "rgba(15, 127, 255, 0.1)",
          cursor: "pointer",
        }}
        key={offset}
      >
        {match}
      </span>
    );
    lastIndex = offset + match.length;
  });

  if (lastIndex < text.length) {
    elements.push(text.slice(lastIndex));
  }

  return elements;
};

export { renderHighlightedText };
