import React, { createContext, useState, useContext } from "react";

export const PlatformContext = createContext();

export const PlatformProvider = ({ children }) => {
  const [platformName, setPlatformName] = useState([]);

  // const setPlatform = (name) => {
  //   setPlatformName(name);
  // };

  return (
    <PlatformContext.Provider value={{ platformName, setPlatformName }}>
      {children}
    </PlatformContext.Provider>
  );
};

export const usePlatform = () => {
  return useContext(PlatformContext);
};
